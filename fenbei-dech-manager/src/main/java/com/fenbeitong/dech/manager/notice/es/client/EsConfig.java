package com.fenbeitong.dech.manager.notice.es.client;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022-04-26
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "elasticsearch")
public class EsConfig {
    public static EsConfig me() {
        return SpringUtil.getBean(EsConfig.class);
    }
    private String host;
    private int port;
    private String userName;
    private String password;
    private String indexPrefix;

    public String getIndexPrefix() {
        return indexPrefix;
    }

    public void setIndexPrefix(String indexPrefix) {
        this.indexPrefix = indexPrefix;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public static Logger getLog() {
        return log;
    }

}
