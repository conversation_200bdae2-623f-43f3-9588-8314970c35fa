package com.fenbeitong.dech.manager.notice.es.client;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by jianqing.liu on 2022/4/26
 */
@Configuration
public class EsClientConfig implements FactoryBean<RestHighLevelClient>, InitializingBean, DisposableBean {

    public static RestHighLevelClient client() {
        return SpringUtil.getBean(RestHighLevelClient.class);
    }

    private RestHighLevelClient client;

    @Override
    public void destroy() throws Exception {
        if (client != null) {
            client.close();
        }
    }

    @Override
    public RestHighLevelClient getObject() throws Exception {
        return client;
    }

    @Override
    public Class<?> getObjectType() {
        return RestHighLevelClient.class;
    }

    @Override
    public boolean isSingleton() {
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        buildClient();
    }

    private void buildClient() {
        try {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(EsConfig.me().getUserName(), EsConfig.me().getPassword()));

            RestClientBuilder builder = RestClient.builder(new HttpHost(EsConfig.me().getHost(), EsConfig.me().getPort()))
                    .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));

            client = new RestHighLevelClient(builder);
        } catch (Exception ex) {
            FinhubLogger.error("ElasticSearch连接出错：" + ex);
        }
    }
}

