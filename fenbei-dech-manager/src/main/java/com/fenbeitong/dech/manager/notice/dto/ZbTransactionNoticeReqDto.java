package com.fenbeitong.dech.manager.notice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ZbTransactionNoticeReqDto
 * @Description: 众邦交易结果通知
 * <AUTHOR>
 * @Date 2021/3/9
 **/
@Data
public class ZbTransactionNoticeReqDto implements Serializable {

    /*
    II/III 类账户卡
     */
    @JsonProperty(value = "subAccNo")
    private String subAccNo;

    /*
    交易金额(元)
     */
    @JsonProperty(value = "txnAmt")
    private String txnAmt;

    /*
    通知类型
    取值说明
    SA001：消费
    SA002：退货
    SA003：消费冲正
    SA004：消费撤销冲正
     */
    @JsonProperty(value = "tranType")
    private String tranType;

    /*
    交易状态
    取值说明
    成功：00
    失败：01
     */
    @JsonProperty(value = "transState")
    private String transState;

    /*
    交易流水号
    流水号+系统跟踪号确定唯一性
    cups：平台流水号
    银联无卡：银联流水号
    网联：网联流水号
     */
    @JsonProperty(value = "transNo")
    private String transNo;

    /*
    原交易请求流水号
     */
    @JsonProperty(value = "oriTransNo")
    private String oriTransNo;

    /*
    交易传输时间
    当前交易传输时间
    cups：对应 7 域
    银联无卡：银联交易时间
    网联：网联交易时间
     */
    @JsonProperty(value = "transmsnDateTime")
    private String transmsnDateTime;

    /*
    系统跟踪号
    cups：对应 11 域 6位
    银联无卡：wk
    网联：epcc
     */
    @JsonProperty(value = "sysTraceAuditNum")
    private String sysTraceAuditNum;

    /*
    交易商户号
     */
    @JsonProperty(value = "mchntCd")
    private String mchntCd;

    /*
    交易商户名称
     */
    @JsonProperty(value = "mchntName")
    private String mchntName;

    /*
    清算日期
     */
    @JsonProperty(value = "settleDate")
    private String settleDate;

    /*
    通知日期
     */
    @JsonProperty(value = "transDate")
    private String transDate;

    /*
    请求保留域
     */
    @JsonProperty(value = "reqResvFld")
    private String reqResvFld;
}
