package com.fenbeitong.dech.manager.notice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: liuzhitao
 * @Date: 2021/2/27 10:25
 * @Description:打款认证通知参数
 */
@Data
public class ZbPayVerifyNoticeReqDto implements Serializable {

    /**
     * 交易流水号  必填:Y
     */
    @JsonProperty(value = "TxnSrlNo")
    private String TxnSrlNo;

    /**
     * 交易日期格式：YYYYMMDD 必填:Y
     */
    @JsonProperty(value = "TxnDt")
    private String TxnDt;

    /**
     * 交易时间戳格式：HHMMSSNNN 必填:Y
     */
    @JsonProperty(value = "TxnTs")
    private String TxnTs;

    /**
     * 事件唯一标示1、通知事件的 ID
     * 2、由系统生成的唯一 ID
     * 通知失败重试通知时，ID 不变
     * 必填:Y
     */
    @JsonProperty(value = "EvntId")
    private String EvntId;

    /**
     * 事件类型通知事件类型：settle_acct_pay_amount_validation 必填:Y
     */
    @JsonProperty(value = "EvenTp")
    private String EvenTp;

    /**
     * 事件内容结构体 通知事件的内容数据 必填:Y
     */
    @JsonProperty(value = "EvnCntStruct")
    private EvnCntStruct EvnCntStruct;

    /**
     * 创建时间格式：RFC3339 必填:Y
     */
    @JsonProperty(value = "CrtTm")
    private String CrtTm;

    @Data
    public static class EvnCntStruct {
        /**
         * 验证信息结构体 当汇款验证状态为： processing 时返回 可根据指引完成账户验证
         * 必填:C
         */
        @JsonProperty(value = "VldInfoStruct")
        private VldInfoStruct VldInfoStruct;

        /**
         * 绑定账户 ID系统生成的唯一编号 必填:Y
         */
        @JsonProperty(value = "BndActId")
        private String BndActId;

        /**
         * 订单号path 1、平台自定义的唯一编号 2、要求64个字符内，只能是数字、大小写字母和_-  必填:Y
         */
        @JsonProperty(value = "OrdNo")
        private String OrdNo;

    }

    @Data
    public static class VldInfoStruct {

        /**
         * 验证类型固定枚举值：2
         * 银行向平台打款后由平台使用[打款金额验证]接口确认打款验证金额
         * 必填:Y
         */
        @JsonProperty(value = "VldTp")
        private String VldTp;

        /**
         * 付款人户名验证类型为：1：表示平台账户名 2：表示银行账户名
         * 必填:Y
         */
        @JsonProperty(value = "PyAcctNm")
        private String PyAcctNm;

        /**
         * 付款方账号验证类型：1：表示平台账户号 2：表示银行账户号 必填:Y
         */
        @JsonProperty(value = "PyrCnt")
        private String PyrCnt;

        /**
         * 收款人户名验证类型：1：表示银行账户名 2：表示平台账户名 必填:Y
         */
        @JsonProperty(value = "PyeAcctNm")
        private String PyeAcctNm;

        /**
         * 收款人账号验证类型：1：表示银行账户号 2：表示平台账户号 必填:Y
         */
        @JsonProperty(value = "PyeAcctNo")
        private String PyeAcctNo;

        /**
         * 开户银行联行号验证类型：1：表示银行账户开户银行联行号 2：表示平台账户开户银行联行号 必填:Y
         */
        @JsonProperty(value = "OpnActBnkBrnNo")
        private String OpnActBnkBrnNo;

        /**
         * 省份代码验证类型：1：表示银行账户省市信息2：表示平台账户省市信息 必填:N
         */
        @JsonProperty(value = "ProvCd")
        private String ProvCd;

        /**
         * 备注用于二级商户汇款验证时，填写备注信息 必填:N
         */
        @JsonProperty(value = "Rmk")
        private String Rmk;

        /**
         * 结束时间汇款验证截止时间格式：RFC3339 必填:Y
         */
        @JsonProperty(value = "EndTm")
        private String EndTm;

    }

}
