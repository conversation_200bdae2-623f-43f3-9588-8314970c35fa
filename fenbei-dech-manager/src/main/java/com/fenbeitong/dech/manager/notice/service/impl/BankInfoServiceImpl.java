package com.fenbeitong.dech.manager.notice.service.impl;

import com.fenbeitong.dech.manager.notice.dto.BankRespDTO;
import com.fenbeitong.dech.manager.notice.es.base.EsBaseSearchService;
import com.fenbeitong.dech.manager.notice.es.base.enums.EsIndexEnum;
import com.fenbeitong.dech.manager.notice.service.BankInfoService;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-03-21 上午9:42
 */
@Service
@Slf4j
public class BankInfoServiceImpl extends EsBaseSearchService implements BankInfoService {


    @Override
    public BankRespDTO queryBankInfoByBranchCode(String branchCode) {
        SearchSourceBuilder searchBuilder = getSearchRequestBuilder();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.termQuery("bankType", 2));
        boolBuilder.must(QueryBuilders.termQuery("bankBranchCode", branchCode));
        searchBuilder.query(boolBuilder);
        SearchResponse response = search(EsIndexEnum.MATERIAL_BANK, searchBuilder);
        List<BankRespDTO> bankList = handlerResult2Obj(BankRespDTO.class, response);
        return CollectionUtils.isNotEmpty(bankList)?bankList.get(0):null;
    }

    @Override
    public BankRespDTO queryBankInfoByBankCode(String bankCode) {
        SearchSourceBuilder searchBuilder = getSearchRequestBuilder();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.termQuery("bankType", 1));
        boolBuilder.must(QueryBuilders.termQuery("bankCode", bankCode));
        searchBuilder.query(boolBuilder);
        SearchResponse response = search(EsIndexEnum.MATERIAL_BANK, searchBuilder);
        List<BankRespDTO> bankList = handlerResult2Obj(BankRespDTO.class, response);
        return CollectionUtils.isNotEmpty(bankList)?bankList.get(0):null;
    }
}
