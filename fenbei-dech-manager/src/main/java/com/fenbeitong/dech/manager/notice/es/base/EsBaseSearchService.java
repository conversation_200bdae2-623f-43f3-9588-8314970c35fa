package com.fenbeitong.dech.manager.notice.es.base;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.manager.notice.es.base.constant.SearchConstant;
import com.fenbeitong.dech.manager.notice.es.base.enums.EsIndexEnum;
import com.fenbeitong.dech.manager.notice.es.client.EsClientConfig;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.finhub.framework.core.json.JsonUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.ExceptionUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * yang.li
 * 2022-06-27
 * es查询基础服务类
 */
@Slf4j
@Component
public class EsBaseSearchService {
    public static EsBaseSearchService me() {
        return SpringUtil.getBean(EsBaseSearchService.class);
    }

    public SearchSourceBuilder getSearchRequestBuilder() {
        return new SearchSourceBuilder();
    }

    public SearchResponse search(EsIndexEnum indexEnum, SearchSourceBuilder searchBuilder) {
        SearchRequest searchRequest = new SearchRequest(indexEnum.getRealIndex());
        searchRequest.source(searchBuilder);

        RestHighLevelClient client = EsClientConfig.client();
        try {
            return client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            FinhubLogger.error("search error, index:{} exception:{}", indexEnum.getRealIndex(), e.toString());
            throw new FinhubException(600, "es io异常");
        }
    }


    /**
     * 保存文档
     *
     * @param esIndexEnum
     * @return
     */
    public void saveData(EsIndexEnum esIndexEnum, BaseEsDTO data) {
        RestHighLevelClient highLevelClient = EsClientConfig.client();
        IndexRequest request = new IndexRequest(esIndexEnum.getRealIndex());
        request.source(JSON.toJSONString(data), XContentType.JSON);
        request.id(data.getId());
        try {
            IndexResponse index = highLevelClient.index(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("ES保存文档异常", ExceptionUtils.getStackTraceAsString(e));
        }
    }

    /**
     * 删除文档
     *
     * @param esIndexEnum
     * @return
     */
    public void deleteData(EsIndexEnum esIndexEnum, String id, Boolean refresh) {
        RestHighLevelClient highLevelClient = EsClientConfig.client();
        DeleteRequest request = new DeleteRequest(esIndexEnum.getRealIndex());
        request.id(id);
        if (refresh) {
            request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);//插入完成后立即强制刷新索引
        }
        try {
            highLevelClient.delete(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("ES删除文档异常", ExceptionUtils.getStackTraceAsString(e));
        }
    }

    public String saveOrUpdate(EsIndexEnum indexEnum, BaseEsDTO dto, Boolean refresh) {
        if (dto == null || Objects.isNull(indexEnum)) {
            return null;
        }
        ValidateUtils.validate(dto);
        RestHighLevelClient highLevelClient = EsClientConfig.client();

        try {
            IndexRequest request = new IndexRequest(indexEnum.getRealIndex());
            request.id(dto.getId());
            request.source(com.luastar.swift.base.json.JsonUtils.toJson(dto), XContentType.JSON);
            if (refresh) {
                request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);//插入完成后立即强制刷新索引
            }
            IndexResponse response = highLevelClient.index(request, RequestOptions.DEFAULT);
            return response.getId();
        } catch (IOException e) {
            FinhubLogger.error("saveOrUpdate error, exception:{}", e.toString());
            return null;
        }
    }

    /**
     * 根据id获取文档map
     *
     * @param esIndexEnum
     * @param id
     * @return
     */
    public Map<String, Object> getDataById(EsIndexEnum esIndexEnum, String id) {
        RestHighLevelClient highLevelClient = EsClientConfig.client();
        GetRequest request = new GetRequest(esIndexEnum.getRealIndex());
        request.id(id);
        try {
            GetResponse getResponse = highLevelClient.get(request, RequestOptions.DEFAULT);
            return getResponse.getSourceAsMap();
        } catch (IOException e) {
            log.error("ES查询文档异常,id:{}", id, ExceptionUtils.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 根据id获取文档obj
     *
     * @param esIndexEnum
     * @param id
     * @param c
     * @param <T>
     * @return
     */
    public <T> T getDataById(EsIndexEnum esIndexEnum, String id, Class<T> c) {
        RestHighLevelClient highLevelClient = EsClientConfig.client();
        GetRequest request = new GetRequest(esIndexEnum.getRealIndex());
        request.id(id);
        try {
            GetResponse getResponse = highLevelClient.get(request, RequestOptions.DEFAULT);
            String sourceAsString = getResponse.getSourceAsString();
            return JsonUtils.toObj(sourceAsString, c);
        } catch (IOException e) {
            log.error("ES查询文档异常,id:{}", id, ExceptionUtils.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 查询文档2map
     *
     * @param esIndexEnum
     * @param request
     * @return
     */
    public List<Map<String, Object>> searchDataMapBySearchRequest(EsIndexEnum esIndexEnum, SearchRequest request) {
        SearchResponse searchResponse = getSearchResponse(esIndexEnum, request);
        if (searchResponse == null) {
            return Lists.newArrayList();
        }
        return handlerResult2Map(searchResponse);
    }


    /**
     * 查询文档2Object
     *
     * @param esIndexEnum
     * @param request
     * @param c
     * @param <T>
     * @return
     */
    public <T> List<T> getDataObjBySearchRequest(EsIndexEnum esIndexEnum, SearchRequest request, Class<T> c) {
        SearchResponse searchResponse = getSearchResponse(esIndexEnum, request);
        if (searchResponse == null) {
            return Lists.newArrayList();
        }
        return handlerResult2Obj(c, searchResponse);
    }

    /**
     * 根据id集合查询文档2map
     *
     * @param esIndexEnum
     * @param ids
     * @return
     */
    public List<Map<String, Object>> getDataByIds(EsIndexEnum esIndexEnum, Collection<String> ids) {
        if (esIndexEnum == null || CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return searchDataMapBySearchRequest(esIndexEnum, getSearchRequestByIds(esIndexEnum, ids));
    }

    /**
     * 根据id集合查询文档2object
     *
     * @param esIndexEnum
     * @param ids
     * @param c
     * @param <T>
     * @return
     */
    public <T> List<T> getDataByIds(EsIndexEnum esIndexEnum, Collection<String> ids, Class<T> c) {
        if (esIndexEnum == null || CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return getDataObjBySearchRequest(esIndexEnum, getSearchRequestByIds(esIndexEnum, ids), c);
    }

    /**
     * getSearchResponse
     *
     * @param esIndexEnum
     * @param request
     * @return
     */
    public SearchResponse getSearchResponse(EsIndexEnum esIndexEnum, SearchRequest request) {
        if (!checkSearchReqAvailable(esIndexEnum, request)) {
            return null;
        }
        //默认60秒超时
        SearchSourceBuilder source = request.source().timeout(new TimeValue(60, TimeUnit.SECONDS));
        request.source(source);
        try {
            RestHighLevelClient highLevelClient = EsClientConfig.client();
            return highLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("ES查询文档异常,index:{},query:{}", request.indices(), JsonUtils.toJson(source), ExceptionUtils.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 校验查询是否可用
     *
     * @param request
     * @return
     */
    private boolean checkSearchReqAvailable(EsIndexEnum esIndexEnum, SearchRequest request) {
        if (esIndexEnum == null || request == null) {
            return false;
        }
        SearchSourceBuilder source = request.source();
        if (source == null) {
            return false;
        }
        int from = source.from();
        if (from < 0 || from >= SearchConstant.ES_SEARCH_MAX) {
            return false;
        }
        int size = source.size();
        if (size < 1 || size > SearchConstant.ES_SEARCH_MAX) {
            return false;
        }
        return from + size <= SearchConstant.ES_SEARCH_MAX;
    }

    /**
     * @param esIndexEnum
     * @param ids
     * @return
     */
    private SearchRequest getSearchRequestByIds(EsIndexEnum esIndexEnum, Collection<String> ids) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("id", ids));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.timeout(new TimeValue(60, TimeUnit.SECONDS));
        SearchRequest request = new SearchRequest(esIndexEnum.getRealIndex());
        request.source(sourceBuilder);
        return request;
    }

    /**
     * @param searchResponse
     * @return
     */
    private List<Map<String, Object>> handlerResult2Map(SearchResponse searchResponse) {
        if (searchResponse.status() != RestStatus.OK || searchResponse.getHits().getTotalHits().value <= 0) {
            return Lists.newArrayList();
        }
        return Arrays.stream(searchResponse.getHits().getHits()).map(SearchHit::getSourceAsMap).collect(Collectors.toList());
    }

    /**
     * @param c
     * @param searchResponse
     * @param <T>
     * @return
     */
    protected <T> List<T> handlerResult2Obj(Class<T> c, SearchResponse searchResponse) {
        if (searchResponse.status() != RestStatus.OK || searchResponse.getHits().getTotalHits().value <= 0) {
            return Lists.newArrayList();
        }
        return Arrays.stream(searchResponse.getHits().getHits()).map(e -> JsonUtils.toObj(e.getSourceAsString(), c)).collect(Collectors.toList());
    }

}
