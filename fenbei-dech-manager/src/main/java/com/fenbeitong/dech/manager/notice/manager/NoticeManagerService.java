package com.fenbeitong.dech.manager.notice.manager;

import com.fenbeitong.dech.manager.notice.dto.ZbCashInNoticeReqDto;
import com.fenbeitong.dech.manager.notice.dto.ZbNoticeRespDto;
import com.fenbeitong.dech.manager.notice.dto.ZbPayVerifyNoticeReqDto;

/**
 * @Author: liuzhitao
 * @Date: 2021/2/27 16:42
 * @Description:通知服务
 */
public interface NoticeManagerService {

    /**
     * 打款认证通知
     */
    ZbNoticeRespDto noticePayVerify(ZbPayVerifyNoticeReqDto reqDto);

    /**
     * 线下入金通知
     */
    ZbNoticeRespDto noticeCashIn(ZbCashInNoticeReqDto reqDto);

}
