package com.fenbeitong.dech.manager.notice.dto;

import com.fenbeitong.dech.manager.notice.es.base.BaseEsDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BankRespDTO extends BaseEsDTO {

    /**
     * 分贝通机构id
     */
    private Integer originalId;
    /**
     * 银行code
     */
    private String bankCode;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行code
     */
    private String bankBranchCode;
    /**
     * 支行名称.
     */
    private String bankBranchName;
    /**
     * 1：总行2：支行
     */
    private String bankType;
    /**
     * cityCode
     */
    private String cityCode;
    /**
     * telephone
     */
    private String telephone;
    /**
     * createUserId
     */
    private String createUserId;
    /**
     * createUserName
     */
    private String createUserName;
    /**
     * updateUserId
     */
    private String updateUserId;
    /**
     * updateUserName
     */
    private String updateUserName;

}
