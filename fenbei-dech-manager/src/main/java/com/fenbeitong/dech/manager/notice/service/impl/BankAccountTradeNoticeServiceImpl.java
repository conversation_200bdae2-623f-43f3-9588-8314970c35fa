package com.fenbeitong.dech.manager.notice.service.impl;

import com.fenbeitong.dech.dto.mapper.BankAccountTradeNoticeMapper;
import com.fenbeitong.dech.dto.zb.BankAccountTradeNotice;
import com.fenbeitong.dech.manager.notice.service.BankAccountTradeNoticeService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: liuzhitao
 * @Date: 2021/3/2 18:54
 * @Description:银行交易消息通知记录服务
 */
@Service
public class BankAccountTradeNoticeServiceImpl implements BankAccountTradeNoticeService {

    @Autowired
    BankAccountTradeNoticeMapper bankTxnNoticeMapper;

    @Override
    public int saveTradeNotice(BankAccountTradeNotice notice) {
        return bankTxnNoticeMapper.insert(notice);
    }

    @Override
    public BankAccountTradeNotice findBankAccountTradeNoticeByNoticeId(String bankCode, String noticeId) {
        Example example = new Example(BankAccountTradeNotice.class);
        example.createCriteria().andEqualTo("bankName", BankNameEnum.CITIC.getCode())
                .andEqualTo("noticeId", noticeId);
        return bankTxnNoticeMapper.selectOneByExample(example);
    }

    @Override
    public List<String> findNoticeAccountNoByTime(Date startTime, Date endTime) {
        Example example = new Example(BankAccountTradeNotice.class);
        example.createCriteria().andEqualTo("bankName", BankNameEnum.CITIC.getCode())
                .andEqualTo("noticeStatus", "S")
                .andGreaterThanOrEqualTo("noticeTime", startTime).
                andLessThanOrEqualTo("noticeTime", endTime);
        return bankTxnNoticeMapper.selectByExample(example).stream().map(BankAccountTradeNotice::getCompanyAccountId).collect(Collectors.toList());
    }

}
