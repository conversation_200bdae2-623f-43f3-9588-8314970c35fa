package com.fenbeitong.dech.manager.notice.es.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * yang.li
 * 2022-06-27
 */
@Data
public class BaseEsDTO implements Serializable {

    @NotNull
    protected String id;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
            timezone = "GMT+8"
    )
    protected Date createTime;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
            timezone = "GMT+8"
    )
    protected Date updateTime;
}
