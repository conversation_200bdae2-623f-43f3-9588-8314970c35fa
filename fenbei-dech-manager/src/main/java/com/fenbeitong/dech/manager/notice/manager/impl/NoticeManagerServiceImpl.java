package com.fenbeitong.dech.manager.notice.manager.impl;

import com.fenbeitong.dech.dto.zb.BankAccountTradeNotice;
import com.fenbeitong.dech.dto.zb.BankAccountVerifyNotice;
import com.fenbeitong.dech.manager.notice.dto.ZbCashInNoticeReqDto;
import com.fenbeitong.dech.manager.notice.dto.ZbNoticeRespDto;
import com.fenbeitong.dech.manager.notice.dto.ZbPayVerifyNoticeReqDto;
import com.fenbeitong.dech.manager.notice.manager.NoticeManagerService;
import com.fenbeitong.dech.manager.notice.service.BankAccountTradeNoticeService;
import com.fenbeitong.dech.manager.notice.service.BankAccountVerifyNoticeService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.kafka.msg.dech.KafkaDechCashInMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: liuzhitao
 * @Date: 2021/2/27 16:42
 * @Description:银行通知服务
 */
@Service
public class NoticeManagerServiceImpl implements NoticeManagerService {

    @Autowired
    KafkaProducerPublisher kafkaProducerPublisher;

    @Autowired
    BankAccountVerifyNoticeService bankAccountVerifyNoticeService;

    @Autowired
    BankAccountTradeNoticeService bankAccountTradeNoticeService;

    @Override
    public ZbNoticeRespDto noticePayVerify(ZbPayVerifyNoticeReqDto reqDto) {
        // 分布式锁

        // 幂等校验
        payVerifyIdempotentCheck(BankNameEnum.ZBBANK.getCode(), reqDto.getEvntId());
        // 构建db对象
        BankAccountVerifyNotice bankAccountVerifyNotice = buildBankAccountVerifyNotice(reqDto);
        // 落通知数据
        bankAccountVerifyNoticeService.saveVerifyNotice(bankAccountVerifyNotice);

        return buildZbNoticeRespDto();
    }

    @Override
    public ZbNoticeRespDto noticeCashIn(ZbCashInNoticeReqDto reqDto) {
        // 分布式锁

        // 幂等校验
        cashInIdempotentCheck(BankNameEnum.ZBBANK.getCode(), reqDto.getEvntId());
        // 构建db对象
        BankAccountTradeNotice bankAccountTradeNotice = buildBankAccountTradeNotice(reqDto);
        // 落通知数据
        bankAccountTradeNoticeService.saveTradeNotice(bankAccountTradeNotice);
        // 异步发送打款认证kafka消息
        CompletableFuture.runAsync(() -> {
            KafkaDechCashInMsg cashInMsg = new KafkaDechCashInMsg();
            cashInMsg.setTxnId(reqDto.getTxnSrlNo());
            cashInMsg.setBankName(BankNameEnum.ZBBANK.getCode());
            cashInMsg.setCreateTime(reqDto.getCrtTm());
            ZbCashInNoticeReqDto.EvnCntStruct evnCntStruct = reqDto.getEvnCntStruct();
            cashInMsg.setReceiveAccountNo(evnCntStruct.getFctId());
            cashInMsg.setPayBankAccountName(evnCntStruct.getPayBnkActNm());
            cashInMsg.setPayBankName(evnCntStruct.getPayBankName());
            cashInMsg.setPayBankAccountNo(evnCntStruct.getSwtchOutAcctNo());
            cashInMsg.setAmount(new BigDecimal(evnCntStruct.getAmt()));
            cashInMsg.setName("入金通知");
            kafkaProducerPublisher.publish(cashInMsg);
        });
        return buildZbNoticeRespDto();
    }

    private ZbNoticeRespDto buildZbNoticeRespDto() {
        ZbNoticeRespDto response = new ZbNoticeRespDto();
        response.setCode("000000");
        response.setMsg("");
        return response;
    }

    /**
     * 打款认证消息通知幂等校验
     */
    private void payVerifyIdempotentCheck(String bankCode, String noticeId) {
        BankAccountVerifyNotice bankTxnNotice = bankAccountVerifyNoticeService.findBankAccountVerifyNoticeByNoticeId(bankCode, noticeId);
        if (Objects.nonNull(bankTxnNotice)) {
            throw new FinhubException(1, "打款认证消息重复通知，noticeId[{}]", noticeId);
        }
        return;
    }

    /**
     * 线下入金消息通知幂等校验
     */
    private void cashInIdempotentCheck(String bankCode, String noticeId) {
        BankAccountTradeNotice bankTxnNotice = bankAccountTradeNoticeService.findBankAccountTradeNoticeByNoticeId(bankCode, noticeId);
        if (Objects.nonNull(bankTxnNotice)) {
            throw new FinhubException(1, "线下入金消息重复通知，noticeId[{}]", noticeId);
        }
        return;
    }

    /**
     * 众邦打款认证消息转换db实体
     */
    private BankAccountVerifyNotice buildBankAccountVerifyNotice(ZbPayVerifyNoticeReqDto reqDto) {
        BankAccountVerifyNotice notice = new BankAccountVerifyNotice();
        notice.setNoticeId(reqDto.getEvntId());
        notice.setBankCode(BankNameEnum.ZBBANK.getCode());
        notice.setNoticeMsg(JsonUtils.toJson(reqDto));
        ZbPayVerifyNoticeReqDto.EvnCntStruct evnCntStruct = reqDto.getEvnCntStruct();
        if (Objects.isNull(evnCntStruct)) {
            throw new FinhubException(1, "打款认证消息内容为空");
        }
        notice.setAccountId(evnCntStruct.getOrdNo());
        notice.setCreateTime(new Date());
        notice.setModifyTime(new Date());
        return notice;
    }

    /**
     * 众邦线下入金消息转换db实体
     */
    private BankAccountTradeNotice buildBankAccountTradeNotice(ZbCashInNoticeReqDto reqDto) {
        BankAccountTradeNotice notice = new BankAccountTradeNotice();
        notice.setNoticeId(reqDto.getEvntId());
        notice.setTxnType(1);
        notice.setTxnId(reqDto.getTxnSrlNo());
        notice.setBankName(BankNameEnum.ZBBANK.getCode());
        notice.setTxnTime(new Date());
        notice.setNoticeCode("0");
        notice.setNoticeStatus("S");
        notice.setEmployeeId("");
        notice.setCompanyId("");
        notice.setNoticeTime(new Date());
        notice.setCreateTime(new Date());
        notice.setUpdateTime(new Date());
        notice.setCallCashierStatus(0);
        notice.setCallNextTime(new Date());
        notice.setCallNum(0);
        // 虚户id
        notice.setCompanyAccountId(reqDto.getEvnCntStruct().getFctId());
        notice.setNoticeMessage("");
        notice.setNoticeReqData(JsonUtils.toJson(reqDto));
        return notice;
    }

}
