package com.fenbeitong.dech.manager.notice.service;

import com.fenbeitong.dech.dto.zb.BankAccountTradeNotice;

import java.util.Date;
import java.util.List;

/**
 * @Author: liuzhitao
 * @Date: 2021/3/2 18:54
 * @Description:银行交易消息通知记录服务
 */
public interface BankAccountTradeNoticeService {

    /**
     * 保存交易消息
     * @param notice
     * @return
     */
    int saveTradeNotice(BankAccountTradeNotice notice);

    /**
     * 根据银行编码、消息id查询交易消息
     * @param bankCode
     * @param noticeId
     * @return
     */
    BankAccountTradeNotice findBankAccountTradeNoticeByNoticeId(String bankCode, String noticeId);

    /*
     * @MethodName: 查询规定日期内发生动账通知的账户
     * @Param: [startTime, endTime]
     * @Return: java.util.List<java.lang.String>
     * @Author: Jarvis.li
     * @Date: 2021/10/11
    **/
    List<String> findNoticeAccountNoByTime(Date startTime, Date endTime);

}
