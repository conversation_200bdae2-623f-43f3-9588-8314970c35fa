package com.fenbeitong.dech.manager.notice.service.impl;

import com.fenbeitong.dech.dto.mapper.BankAccountVerifyNoticeMapper;
import com.fenbeitong.dech.dto.zb.BankAccountVerifyNotice;
import com.fenbeitong.dech.manager.notice.service.BankAccountVerifyNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: liuzhitao
 * @Date: 2021/2/27 16:42
 * @Description:打款认证消息服务
 */
@Service
public class BankAccountVerifyNoticeServiceImpl implements BankAccountVerifyNoticeService {

    @Autowired
    BankAccountVerifyNoticeMapper mapper;

    @Override
    public void saveVerifyNotice(BankAccountVerifyNotice reqDto) {
        mapper.insert(reqDto);
    }

    @Override
    public BankAccountVerifyNotice findBankAccountVerifyNoticeByNoticeId(String bankCode, String noticeId) {
        return null;
    }
}
