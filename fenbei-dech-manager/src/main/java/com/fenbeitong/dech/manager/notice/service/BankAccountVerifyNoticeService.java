package com.fenbeitong.dech.manager.notice.service;

import com.fenbeitong.dech.dto.zb.BankAccountVerifyNotice;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 * @Date: 2021/2/27 16:42
 * @Description:打款认证通知服务
 */
public interface BankAccountVerifyNoticeService {

    /**
     * 保存
     */
    void saveVerifyNotice(BankAccountVerifyNotice reqDto);

    /**
     * 根据通知id查询打款认证通知记录
     * @param bankCode
     * @param noticeId
     * @return
     */
    BankAccountVerifyNotice findBankAccountVerifyNoticeByNoticeId(String bankCode, String noticeId);

}
