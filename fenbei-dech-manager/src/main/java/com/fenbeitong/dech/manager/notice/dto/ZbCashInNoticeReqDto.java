package com.fenbeitong.dech.manager.notice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: liuzhitao
 * @Date: 2021/3/2 14:32
 * @Description:外部入金通知请求参数
 */
@Data
public class ZbCashInNoticeReqDto implements Serializable {

    /**
     * 交易流水号  必填:Y
     */
    @JsonProperty(value = "TxnSrlNo")
    private String TxnSrlNo;

    /**
     * 交易日期格式：YYYYMMDD 必填:Y
     */
    @JsonProperty(value = "TxnDt")
    private String TxnDt;

    /**
     * 交易时间戳格式：HHMMSSNNN 必填:Y
     */
    @JsonProperty(value = "TxnTs")
    private String TxnTs;

    /**
     * 事件唯一标示1、通知事件的 ID  2、由系统生成的唯一 ID 3、通知失败重试通知时，ID 不变 必填:Y
     */
    @JsonProperty(value = "EvntId")
    private String EvntId;

    /**
     * 事件类型通知事件类型：transfer_deposit：转账入金 root_transfer_deposit：母户入金 必填:Y
     */
    @JsonProperty(value = "EvenTp")
    private String EvenTp;

    /**
     * 事件内容结构体 通知事件的内容数据 必填:Y
     */
    @JsonProperty(value = "EvnCntStruct")
    private EvnCntStruct EvnCntStruct;

    /**
     * 创建时间格式：RFC3339 必填:Y
     */
    @JsonProperty(value = "CrtTm")
    private String CrtTm;

    @Data
    public static class EvnCntStruct {

        /**
         * 转出方银行名称 必填:Y
         */
        @JsonProperty(value = "PayBankName")
        private String PayBankName;

        /**
         * 虚户 Id入金虚户 ID 必填:N
         */
        @JsonProperty(value = "FctId")
        private String FctId;

        /**
         * 关联账户账号发生入金交易的虚户账簿号或银行账号 必填:Y
         */
        @JsonProperty(value = "RltdAcctNo")
        private String RltdAcctNo;

        /**
         * 金额单位：分。 必填:Y
         */
        @JsonProperty(value = "Amt")
        private String Amt;

        /**
         * 转出方账号转出方银行账号 必填:Y
         */
        @JsonProperty(value = "SwtchOutAcctNo")
        private String SwtchOutAcctNo;

        /**
         * 转出方银行账户名称 必填:Y
         */
        @JsonProperty(value = "PayBnkActNm")
        private String PayBnkActNm;

        /**
         * 转出方银行编号参见[银行编码表] 必填:Y
         */
        @JsonProperty(value = "PayBnkCd")
        private String PayBnkCd;

        /**
         * 转出方银行联行号详细参见[开户银行联行号表] 必填:Y
         */
        @JsonProperty(value = "PayBnkBrnNo")
        private String PayBnkBrnNo;

        /**
         * 附言信息展示在收款银行系统中的附言 必填:N
         */
        @JsonProperty(value = "CmntsInf")
        private String CmntsInf;

        /**
         * 登记时间格式：RFC3339 必填:Y
         */
        @JsonProperty(value = "RgstTm")
        private String RgstTm;

    }


}
