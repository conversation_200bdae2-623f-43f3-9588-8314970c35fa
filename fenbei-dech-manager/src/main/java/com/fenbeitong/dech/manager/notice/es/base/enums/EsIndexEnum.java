package com.fenbeitong.dech.manager.notice.es.base.enums;


import com.fenbeitong.dech.manager.notice.es.client.EsConfig;

public enum EsIndexEnum {

    MATERIAL_BANK("fenbei_bank_record", "doc", "全国银行信息"),
    ;

    private final String index;
    private final String type;
    private final String desc;

    EsIndexEnum(String index, String type, String desc) {
        this.index = index;
        this.type = type;
        this.desc = desc;
    }

    public String getIndex() {
        return index;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 获取带前缀的真实索引名,前缀配置每个环境不一样.
     *
     * @return
     */
    public String getRealIndex() {
        return EsConfig.me().getIndexPrefix() + this.index;
    }

}
