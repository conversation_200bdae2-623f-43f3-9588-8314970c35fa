package com.fenbeitong.dech.cgb.enums;

/**
 * FreezeFlag与广发对应关系
 *
 * <AUTHOR>
 */
public enum CgbFreezeFlagEnum {
    FREEZE("5", "5", "企业买分贝券"),
    UNFREEZE("6", "9", "分贝券退回"),

    UNFREEZE_TO_FBT("7", "6", "分贝券退回分贝通"),
    ;

    private String freezeFlag;
    private String transType;
    private String desc;

    CgbFreezeFlagEnum(String freezeFlag, String transType, String desc) {
        this.freezeFlag = freezeFlag;
        this.transType = transType;
        this.desc = desc;
    }

    public String getFreezeFlag() {
        return freezeFlag;
    }

    public String getDesc() {
        return desc;
    }

    public String getTransType() {
        return transType;
    }

    public static boolean isFreeze(String freezeFlag) {
        return FREEZE.getFreezeFlag().equals(freezeFlag) ? true : false;
    }

    public static boolean isUnfreeze(String freezeFlag) {
        return UNFREEZE.getFreezeFlag().equals(freezeFlag) ? true : false;
    }

    /**
     * 先票分贝券回收,资金是由担保户 -> 收款户
     * 路径只能是6: 营销子账簿→分贝通自营账簿
     * 因此先票回收，不应该从6 转换为9
     * @param freezeFlag
     * @return
     */
    public static boolean isUnfreezeToFbt(String freezeFlag) {
        return UNFREEZE.getFreezeFlag().equals(freezeFlag) ? true : false;
    }

    public static String getTransType(String freezeFlag) {
        if (FREEZE.getFreezeFlag().equals(freezeFlag)) {
            return FREEZE.transType;
        }
        if (UNFREEZE.getFreezeFlag().equals(freezeFlag)) {
            return UNFREEZE.transType;
        }
        if (UNFREEZE_TO_FBT.getFreezeFlag().equals(freezeFlag)){
            return UNFREEZE_TO_FBT.transType;
        }
        return null;
    }
}
