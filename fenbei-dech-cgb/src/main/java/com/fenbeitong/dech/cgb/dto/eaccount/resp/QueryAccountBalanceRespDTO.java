package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
public class QueryAccountBalanceRespDTO extends CgbCommonResponse {
    /**
     * 通知存款余额	String	19	O	17000	单位，分
     */
    private String otherBalance	;
    /**
     * 	二三类户卡号总余额	String	19	O	50000	单位，分
     */
    private String eAccAmt;
    /**
     * 	二三类户卡号冻结余额	String	19	O	10000	单位，分
     */
    private String eAccUnableAmt;
    /**
     * 	二三类户卡号可用余额	String	19	O	40000	单位，分
     */
    private String currentAvaBalance;
    /**
     * 	二三类户卡号编号	String	30	M
     */
    private String vAccountNo;
    /**
     * 	活期余额	String	19	O	11000	单位，分
     */
    private String currentBalance;
    /**
     * 	二三类户卡号	String	30	O	6225687352XXXXXX
     */
    private String accountNo;
    /**
     * 	定期余额	String	19	O	22000	单位，分
     */
    private String fixedBalance;
    /**
     * 	虚账户冻结余额	String	19	M
     */
    private String vAccUnableAmt;
    /**
     * 	虚账户可用余额	String	19	M
     */
    private String vAccAmt;
}
