package com.fenbeitong.dech.cgb.dto;

import lombok.Data;

/**
 * 统一请求头（HTTP 请求头）
 *
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class BaseRequestHttpHeader {

    /**
     * 版本号 匹配接口文档版本
     */
    private String version = "1.0.0";

    /**
     * 商户号
     */
    private String instId;

    /**
     * 商户应用编号
     * 填写在开放平台注册时分配的应用编号
     */
    private String appId;

    /**
     * 产品编码 接口的产品归类
     */
    private String productCode;

    /**
     * 接口编号
     */
    private String tradeCode;

    /**
     * 报文发起时间
     * 格式:yyyyMMddHHmmss，请求发起 时间
     */
    private String requestTime;

    /**
     * 请求流水号
     * 唯一定位一次报文请求，由发起方 生成，应答方原样返回，uuid 生成， 全局唯一
     */
    private String senderSN;

    /**
     * 保留字段
     * productCode=X XX&tradeCode= XXX&„„
     * KV 方式
     */
    private String reserve;
}
