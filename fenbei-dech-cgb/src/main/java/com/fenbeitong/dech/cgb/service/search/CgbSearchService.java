package com.fenbeitong.dech.cgb.service.search;

import com.fenbeitong.dech.cgb.dto.search.req.EndBalanceQueryReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryAccountInfoReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryManagerTransferReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryTransferReqDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.EndBalanceQueryRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryAccountInfoRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryManagerTransferRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryTransferRespDTO;

/**
 * 银行查询
 *
 * <AUTHOR>
 */
public interface CgbSearchService {

    /**
     * 回查交易结果
     *
     * @param req
     * @return
     */
    QueryTransferRespDTO queryTransfer(QueryTransferReqDTO req);

    /**
     * 账簿日终余额查询
     *
     * @param req
     * @return
     */
    EndBalanceQueryRespDTO endBalanceQuery(EndBalanceQueryReqDTO req);

    /**
     * 账簿交易明细查询(分页查询)
     *
     * @param req
     * @return
     */
    QueryAccountInfoRespDTO queryAccountInfo(QueryAccountInfoReqDTO req);

    /**
     * 专户交易明细查询
     *
     * @param req
     * @return
     */
    QueryManagerTransferRespDTO queryManagerTransfer(QueryManagerTransferReqDTO req);
}
