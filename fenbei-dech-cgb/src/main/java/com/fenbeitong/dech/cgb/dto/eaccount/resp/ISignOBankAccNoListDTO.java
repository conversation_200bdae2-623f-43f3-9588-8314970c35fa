package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import lombok.Data;

import java.io.Serializable;
@Data
public class ISignOBankAccNoListDTO implements Serializable {
    /**
     * 	卡类型	String	2	O
     * 	0-贷记卡,1-借记卡；
     */
    private String cardType3;
    /**
     * 	二三类户卡号	String	19	C	6225687352XXXXXX
     */
    private String accountNo;
    /**
     * 	绑定卡卡号	String	19	M	6225XXXXXXXXXXXX
     */
    private String payAccount;
    /**
     * 	绑定卡银行代码	String	10	M	********
     */
    private String bindingCardBankCode;
    /**
     * 	绑定卡银行名称	String	100	M	广发银行股份有限公司
     */
    private String bindingCardBankName;
    /**
     * 	绑定卡预留手机号	boolean	11	M	139XXXXXXXX
     */
    private String bindingCardMobileNo;
    /**
     * 	客户姓名	String	30	M	张三
     */
    private String custName;
    /**
     * 	二三类户卡号编号	String	30	M	6225687352XXXXXX
     */
    private String vAccountNo;
    /**
     * 	资金动账通知手机号	String	11	M	138XXXXXXXX	二三类户开户时登记手机号
     */
    private String mobileNo;
    /**
     * 	绑定关系协议号	String	30	M		商户后续凭该协议号对绑定关系进行修改删除
     */
    private String bindProtocolCode;
    /**
     * 	签约时间	boolean	14	M
     */
    private String signTime;
    /**
     * 	证件类型	int	2	M
     */
    private String certType;
    /**
     * 	身份证号码	String	20	M	44010120501222273X
     */
    private String certNo;
}
