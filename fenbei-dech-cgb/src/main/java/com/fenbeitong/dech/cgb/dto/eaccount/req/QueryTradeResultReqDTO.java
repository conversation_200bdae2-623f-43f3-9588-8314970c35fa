package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * 回查交易结果
 */
@Data
public class QueryTradeResultReqDTO extends CgbCommonRequest {
    /**
     * orFlowNo	原交易流水	String	32	M
     * 回查交易需上送原交易请求流水号
     */
    private String orFlowNo;
    /**
     * 	原交易类型	String	2	M
     * 	回查交易需上送原交易请求类型
     *  1：充值
     *  2：提现
     */
    private String orTransType;
}
