package com.fenbeitong.dech.cgb.service.acctmgr.impl;

import com.fenbeitong.dech.cgb.CgbEAccountClient;
import com.fenbeitong.dech.cgb.dto.BaseHttpHeaderParam;
import com.fenbeitong.dech.cgb.dto.BaseRequestHttpHeader;
import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.CancelAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.ChangeAccountInfoReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.QueryComoanyAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.RegistComoanyAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.RegistManagerAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.ReplaceCardReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.SendVerifyCodeReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.CommonStatusRespDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.QueryComoanyAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.RegistComoanyAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.RegistManagerAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.eaccount.req.FrozenAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.eaccount.resp.FrozenAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.search.req.MerchantAccountBalanceQueryReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryCustAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.MerchantAccountBalanceQueryRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryCustAccountRespDTO;
import com.fenbeitong.dech.cgb.enums.CgbTradeCodeEnum;
import com.fenbeitong.dech.cgb.enums.ResultCodeEnum;
import com.fenbeitong.dech.cgb.service.acctmgr.CgbAccountService;
import com.fenbeitong.dech.common.until.IDGen;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.finhub.framework.core.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * 账户相关
 *
 * <AUTHOR>
 */
@Service
public class CgbAccountServiceImpl implements CgbAccountService {

    @Autowired
    private CgbEAccountClient cgbEAccountClient;

    @Value("${cgb.projectNum}")
    private String projectNum;

    public Boolean sendVerifyCode(SendVerifyCodeReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.SEND_VERIFY_CODE.getCode());
            req.setSendSn(IDGen.genId("MSG"));
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            CgbCommonResponse response = cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), CgbCommonResponse.class);
            if (ResultCodeEnum.isSuccess(response.getErrorCode())) {
                return true;
            }
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("sendVerifyCode error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return false;
    }

    public RegistComoanyAccountRespDTO registComoanyAccount(RegistComoanyAccountReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.REGIST_COMOANY_ACCOUNT.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), RegistComoanyAccountRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("registComoanyAccount error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    public CommonStatusRespDTO changeAccountMobile(ChangeAccountInfoReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.CHANGE_ACCOUNT_MOBILE.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), CommonStatusRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("changeAccountMobile error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    public QueryComoanyAccountRespDTO queryComoanyAccount(QueryComoanyAccountReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_COMOANY_ACCOUNT.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), QueryComoanyAccountRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("queryComoanyAccount error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    public CommonStatusRespDTO cancelAccount(CancelAccountReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.CANCEL_ACCOUNT.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), CommonStatusRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("cancelAccount error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public RegistManagerAccountRespDTO registManagerAccount(RegistManagerAccountReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.REGIST_MANAGER_ACCOUNT.getCode());
            req.setProjectNum(projectNum);
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), RegistManagerAccountRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("registManagerAccount error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public CommonStatusRespDTO replaceCard(ReplaceCardReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode("replaceCard");
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), CommonStatusRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("relationshipBingding error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public QueryCustAccountRespDTO queryCustAccount(QueryCustAccountReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_CUST_ACCOUNT.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), QueryCustAccountRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("queryCustAccount error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public MerchantAccountBalanceQueryRespDTO merchantAccountBalanceQuery(MerchantAccountBalanceQueryReqDTO merchantAccountBalanceQueryReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_MERCHANT_ACCOUNT_BALANCE_QUERY.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(merchantAccountBalanceQueryReqDTO, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), MerchantAccountBalanceQueryRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("queryCustAccount error，req:").append(JsonUtils.toJson(merchantAccountBalanceQueryReqDTO));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public FrozenAccountRespDTO frozenAccount(FrozenAccountReqDTO frozenAccountReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.FROZEN_ACCOUNT.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(frozenAccountReqDTO, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), FrozenAccountRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("frozenAccount error，req:").append(JsonUtils.toJson(frozenAccountReqDTO));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }
}
