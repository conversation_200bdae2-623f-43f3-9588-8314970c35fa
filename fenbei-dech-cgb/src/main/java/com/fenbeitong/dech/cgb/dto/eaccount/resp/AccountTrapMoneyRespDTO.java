package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/14
 */
@Data
public class AccountTrapMoneyRespDTO extends CgbCommonResponse {
    /**
     * 	交易状态	String	2	M		交易的结果以此字段作为判断依据，
     *         90-成功，
     *         99-失败，
     *         33-异常
     */
    private String tranState;
    /**
     * 	流水号	String	36	M		交易流水号(报文头flowId)
     */
    private String flowId;
    /**
     * 	交易金额	String	18	O		单位为分，交易成功时返回
     */
    private String freezeAmount;
    /**
     * 	生效日期	String	8	O		yyyyMMdd，交易成功时返回
     */
    private String startDate;
    /**
     * 	到期时间	String	8	O		yyyyMMdd，交易成功时返回
     */
    private String endDate;
    /**
     * 	圈存编号	String	12	O
     */
    private String freezeNo;
}
