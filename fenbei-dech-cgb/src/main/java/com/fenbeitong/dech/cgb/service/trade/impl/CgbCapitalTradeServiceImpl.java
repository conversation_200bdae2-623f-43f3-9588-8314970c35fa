package com.fenbeitong.dech.cgb.service.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.dech.cgb.CgbEAccountClient;
import com.fenbeitong.dech.cgb.dto.BaseHttpHeaderParam;
import com.fenbeitong.dech.cgb.dto.BaseRequestHttpHeader;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.CommonStatusRespDTO;
import com.fenbeitong.dech.cgb.dto.trade.req.RefundFundReqDTO;
import com.fenbeitong.dech.cgb.dto.trade.req.SingleAdjustAccountsReqDTO;
import com.fenbeitong.dech.cgb.dto.trade.req.WithrawAccountReqDTO;
import com.fenbeitong.dech.cgb.enums.CgbTradeCodeEnum;
import com.fenbeitong.dech.cgb.service.trade.CgbCapitalTradeService;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.finhub.framework.core.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 广发交易相关
 *
 * <AUTHOR>
 */
@Service
public class CgbCapitalTradeServiceImpl implements CgbCapitalTradeService {

    @Autowired
    private CgbEAccountClient cgbEAccountClient;

    @Value("${cgb.projectNum}")
    private String projectNum;

    public CommonStatusRespDTO singleAdjustAccounts(SingleAdjustAccountsReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.SINGLE_ADJUST_ACCOUNTS.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), CommonStatusRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("singleAdjustAccounts error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;

    }

    public CommonStatusRespDTO withrawAccount(WithrawAccountReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.WITHRAW_ACCOUNT.getCode());
            req.setProjectNum(projectNum);
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), CommonStatusRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("withrawAccount error，req:").append(JSONObject.toJSONString(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public CommonStatusRespDTO refundFund(RefundFundReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.REFUND_FUND.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), CommonStatusRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("refundFund error，req:").append(JSONObject.toJSONString(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }

        return null;
    }
}
