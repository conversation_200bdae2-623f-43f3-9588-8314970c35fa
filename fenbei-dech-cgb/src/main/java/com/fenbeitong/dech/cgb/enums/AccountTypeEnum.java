package com.fenbeitong.dech.cgb.enums;

public enum AccountTypeEnum {

    ENTERPRISE_ACCOUNT("02", "企业账簿"),
    ONE_CENT_ACCOUNT("11", "1分钱账簿"),
    SELF_OPERATED_ACCOUNT("13", "自营账簿");

    private String type;

    private String desc;

    AccountTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
