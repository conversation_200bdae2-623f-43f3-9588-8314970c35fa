package com.fenbeitong.dech.cgb.dto.reconciliation;

import lombok.Data;

import java.io.Serializable;

@Data
public class EWMJDKD implements Serializable {

    /**
     * 交易流水
     */
    private String tradeFlowId;

    /**
     * 冲正标识
     */
    private String reversalMark;

    /**
     * 电子账户
     */
    private String accountNo;

    /**
     * 交易金额 元
     */
    private String tradeAmount;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 退货原单号
     */
    private String originalReturnNo;

    /**
     * 来源渠道
     */
    private String sourceChannel;

    /**
     * 交易分类
     */
    private String tradeClass;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 圈存编号
     */
    private String qcNo;


    public EWMJDKD(String tradeFlowId, String reversalMark, String accountNo, String tradeAmount, String tradeType, String tradeTime, String originalReturnNo, String sourceChannel, String tradeClass, String payType, String qcNo) {
        this.tradeFlowId = tradeFlowId;
        this.reversalMark = reversalMark;
        this.accountNo = accountNo;
        this.tradeAmount = tradeAmount;
        this.tradeType = tradeType;
        this.tradeTime = tradeTime;
        this.originalReturnNo = originalReturnNo;
        this.sourceChannel = sourceChannel;
        this.tradeClass = tradeClass;
        this.payType = payType;
        this.qcNo = qcNo;
    }
}
