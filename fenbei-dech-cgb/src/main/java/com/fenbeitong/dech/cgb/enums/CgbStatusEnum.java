package com.fenbeitong.dech.cgb.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;
@Getter
public enum CgbStatusEnum {

    SUCCESS("90", "成功"),
    FAIL("99", "失败"),
    EXCEPTION("33", "异常(处理中断，交易中)");

    private String status;

    private String desc;

    CgbStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static boolean isSuccess(String status) {
        return StringUtils.isNotBlank(status) && SUCCESS.getStatus().equals(status);
    }

    public static boolean isFail(String status) {
        return StringUtils.isNotBlank(status) && FAIL.getStatus().equals(status);
    }

    public static boolean isException(String status) {
        return StringUtils.isNotBlank(status) && EXCEPTION.getStatus().equals(status);
    }
}
