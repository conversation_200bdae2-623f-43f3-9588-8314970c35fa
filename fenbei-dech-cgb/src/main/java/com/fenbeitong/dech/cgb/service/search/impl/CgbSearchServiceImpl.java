package com.fenbeitong.dech.cgb.service.search.impl;

import com.fenbeitong.dech.cgb.CgbEAccountClient;
import com.fenbeitong.dech.cgb.dto.BaseHttpHeaderParam;
import com.fenbeitong.dech.cgb.dto.BaseRequestHttpHeader;
import com.fenbeitong.dech.cgb.dto.search.req.EndBalanceQueryReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryAccountInfoReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryManagerTransferReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryTransferReqDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.EndBalanceQueryRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryAccountInfoRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryManagerTransferRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryTransferRespDTO;
import com.fenbeitong.dech.cgb.enums.CgbTradeCodeEnum;
import com.fenbeitong.dech.cgb.service.search.CgbSearchService;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.finhub.framework.core.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CgbSearchServiceImpl implements CgbSearchService {

    @Autowired
    private CgbEAccountClient cgbEAccountClient;

    public QueryTransferRespDTO queryTransfer(QueryTransferReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_TRANSFER.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), QueryTransferRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("queryTransfer error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
            throw new FinDechException(GlobalExceptionEnum.CGB_INTERFACE_ERROR);
        }
    }

    @Override
    public EndBalanceQueryRespDTO endBalanceQuery(EndBalanceQueryReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.END_BALANCE_QUERY.getCode());
            req.setChannel("330");
            req.setSChannel("330");
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), EndBalanceQueryRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("endBalanceQuery error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public QueryAccountInfoRespDTO queryAccountInfo(QueryAccountInfoReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_ACCOUNT_INFO.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), QueryAccountInfoRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("queryAccountInfo error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }

    @Override
    public QueryManagerTransferRespDTO queryManagerTransfer(QueryManagerTransferReqDTO req) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_MANAGER_TRANSFER.getCode());
            BaseHttpHeaderParam headerParam = cgbEAccountClient.initParam(req, baseRequestHttpHeader);
            return cgbEAccountClient.request(headerParam.getUrl(), headerParam.getBody(), headerParam.getHeader(), headerParam.getSm4Key(), QueryManagerTransferRespDTO.class);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("queryManagerTransfer error，req:").append(JsonUtils.toJson(req));
            sb.append(", error message:").append(e.getMessage());
            FinhubLogger.error(sb.toString(), e);
        }
        return null;
    }
}
