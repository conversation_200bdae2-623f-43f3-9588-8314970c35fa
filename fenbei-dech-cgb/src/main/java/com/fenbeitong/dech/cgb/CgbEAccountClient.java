package com.fenbeitong.dech.cgb;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fenbeitong.dech.cgb.dto.BaseHttpHeaderParam;
import com.fenbeitong.dech.cgb.dto.BaseRequestHttpHeader;
import com.fenbeitong.dech.cgb.dto.BaseResponse;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import com.fenbeitong.dech.cgb.utils.CgbSM4Util;
import com.fenbeitong.dech.cgb.utils.CgbUtil;
import com.fenbeitong.dech.cgb.utils.Md5Util;
import com.fenbeitong.dech.common.until.SpringService;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.finhub.framework.logback.util.EnvUtils;
import com.google.common.collect.Maps;
import com.luastar.swift.base.net.HttpClientUtils;

import cn.hutool.core.util.CharsetUtil;

import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Map;

/**
 * 广发电子账户类接口（虚拟卡模块）
 *
 * <AUTHOR>
 * @date 2022/2/28
 */
@Component
public class CgbEAccountClient {
    // 单位毫秒
    private static final Integer TIMEOUT = 20000;
    private static final ObjectMapper mapper;
    public static final String ENCODING = "UTF-8";

    @Value("${cgb.host}")
    private String cgbHost;

    @Value("${cgb.myPublicKey}")
    private String myPublicKey;

    @Value("${cgb.myPrivateKey}")
    private String myPrivateKey;

    @Value("${cgb.cgbPublicKey}")
    private String cgbPublicKey;

    @Value("${cgb.signType}")
    private String signType;

    @Value("${cgb.encryptType}")
    private String encryptType;

    @Value("${cgb.appId}")
    private String appId;

    @Value("${cgb.instId}")
    private String instId;

    @Value("${cgb.productCode}")
    private String productCode;

    @Value("${cgb.merchantNum}")
    private String merchantNum;

    @Value("${cgb.env}")
    private String cgbEnv;

    @Autowired
    SpringService springService;

    static {
        mapper = new ObjectMapper();
        // 序列化配置
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 反序列化配置
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 初始化请求参数
     */
    public BaseHttpHeaderParam initParam(CgbCommonRequest cgbCommonRequest, BaseRequestHttpHeader baseRequestHttpHeader) throws Exception {

        // 组装请求报文
        Map<String, Object> requestMap = Maps.newHashMap();
        cgbCommonRequest.setMerchantNum(merchantNum);
        Map<String, Object> bodyMap = mapper.convertValue(cgbCommonRequest, new TypeReference<Map<String, Object>>() {});

        baseRequestHttpHeader.setAppId(appId);
        baseRequestHttpHeader.setInstId(instId);
        baseRequestHttpHeader.setSenderSN(cgbCommonRequest.getSendSn());
        baseRequestHttpHeader.setRequestTime(new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
        baseRequestHttpHeader.setProductCode(productCode);

        requestMap.put("Header", baseRequestHttpHeader);
        requestMap.put("Body", bodyMap);
        String reqStr = mapper.writeValueAsString(requestMap);

        FinhubLogger.info("广发银行initParam reqStr:{}",reqStr);

        // 使用己方私钥签名请求报文得到signature
        String signature = CgbUtil.sign(reqStr, myPrivateKey, ENCODING);

        //使用MD5算法加密己方公钥得到certId
        String certId = Md5Util.getMD5(myPublicKey);

        // 获取SM4密钥（16字节），使用广发公钥加密得到encyrptKey
        String sm4Key = RandomStringUtils.random(16, "123457890abcdefghijklmnopqrstuvwxyz").toUpperCase();
        String cgbEncryptKey = CgbUtil.sm2EncryptString(sm4Key, cgbPublicKey, ENCODING);

        // 使用SM4密钥加密请求报文
        byte[] encryptBytes = CgbSM4Util.encryptCBC(reqStr.getBytes(StandardCharsets.UTF_8), sm4Key.getBytes(), sm4Key.getBytes());
        reqStr = Base64.getEncoder().encodeToString(encryptBytes);

        // 组装请求银行参数
        BaseHttpHeaderParam baseHttpHeaderParam = new BaseHttpHeaderParam();
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("certId", certId);
        headerMap.put("signType", signType);
        headerMap.put("encryptType", encryptType);
        headerMap.put("encryptKey", cgbEncryptKey);
        headerMap.put("signature", signature);
        baseHttpHeaderParam.setHeader(headerMap);
        baseHttpHeaderParam.setBody(reqStr);
        String url = cgbHost + "/gateway/API-1.0/" + appId + "/" + baseRequestHttpHeader.getProductCode() + "/" + baseRequestHttpHeader.getTradeCode() + "/1.0.0";
        //切换广发环境地址,
        String activeProfile = springService.getActiveProfile();
        if (cgbEnv.equals("YD01") && EnvUtils.isFat(activeProfile)){
            url = "http://************:18090/gateway/HXZT/API-1.0/" + appId + "/" + baseRequestHttpHeader.getProductCode() + "/" + baseRequestHttpHeader.getTradeCode() + "/1.0.0";
        }
        baseHttpHeaderParam.setUrl(url);
        baseHttpHeaderParam.setSm4Key(sm4Key);
        return baseHttpHeaderParam;
    }

    public <T> T request(String url, String request, Map<String, String> headerers, String encryptKey, Class<T> type) {
        String response = null;
        long end = 0L;
        long start = 0L;
        try {
            start = System.currentTimeMillis();
            response = HttpClientUtils.postBody(url, request, TIMEOUT, CharsetUtil.UTF_8, headerers);
            end = System.currentTimeMillis();
            FinhubLogger.info("广发银行接口:{},耗时{}ms,广发银行initParam reqStr={},response={}",url, end - start, request, response);
            if (response == null) {
                FinhubLogger.info("广发银行接口返回为NULL,request={},response={}", request, response);
                throw new FinDechException(GlobalExceptionEnum.CGB_INTERFACE_NULL);
            }

            // SM4算法解密返回报文
            byte[] decryptBytes = CgbSM4Util.decryptCBC(Base64.getDecoder().decode(response), encryptKey.getBytes(), encryptKey.getBytes());
            response = new String(decryptBytes, StandardCharsets.UTF_8);
            FinhubLogger.info("广发银行接口返回解密信息,response={}", response);
            BaseResponse result = JSON.parseObject(response, BaseResponse.class);
            return JSON.parseObject(result.getBody(), type);
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【广发银行接口请求异常】【耗时{}ms】,exception=", end - start, e);
            throw new FinDechException(GlobalExceptionEnum.CGB_INTERFACE_ERROR);
        }
    }
}
