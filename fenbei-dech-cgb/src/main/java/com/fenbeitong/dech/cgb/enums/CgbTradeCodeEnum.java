package com.fenbeitong.dech.cgb.enums;

public enum CgbTradeCodeEnum {

    SEND_VERIFY_CODE("sendVerifyCode", "发送短信验证码（账薄）"),
    REGIST_COMOANY_ACCOUNT("registComoanyAccount", "注册账簿(企业)"),
    CHANGE_ACCOUNT_MOBILE("changeAccountMobile", "变更账簿信息"),
    QUERY_COMOANY_ACCOUNT("queryComoanyAccount", "查询企业账簿信息"),
    CANCEL_ACCOUNT("cancelAccount", "注销账簿"),
    QUERY_TRANSFER("queryTransfer", "回查交易结果"),
    SINGLE_ADJUST_ACCOUNTS("singleAdjustAccounts", "单笔调账"),
    WITHRAW_ACCOUNT("withrawAccount", "账簿资金提现（单笔）"),
    REFUND_FUND("refundFund","不明资金退款"),
    REGIST_MANAGER_ACCOUNT("registManagerAccount","注册账簿(平台)"),
    END_BALANCE_QUERY("endBalanceQuery","账簿日终余额查询"),
    QUERY_ACCOUNT_INFO("queryAccountInfo","账簿交易明细查询"),
    QUERY_MANAGER_TRANSFER("queryManagerTransfer","专户交易明细查询"),
    QUERY_CUST_ACCOUNT("queryCustAccount","平台账簿信息查询"),

    QUERY_MERCHANT_ACCOUNT_BALANCE_QUERY("merchantAccountBalanceQuery","查询对公户账户余额"),

    FROZEN_ACCOUNT("frozenAccount","账簿冻结/解冻"),

    /**
     * 电子账户类
     */
    QUERY_PROVINCE_CITY("queryProvinceCity","查询省市区"),
    QUERY_OCCUPATION("queryOccupation","查询职业"),
    UPLOAD_CUSTOMER_IMAGE_DATA("uploadCustImageData","上传客户影像资料"),
    SEND_MSG_CODE("sendMsgCode","发送短信验证码"),
    CREATE_ACCOUNT_TYPE_OP("createAccountTypeOP","开立银行账户"),
    QUERY_UPGRADE_ACCOUNT_TYPE_HW("queryUpgradeAccountTypeHW","回查开户结果"),
    QUERY_ACCOUNT_BALANCE("queryAccountBalance","查询二三类户账户余额信息"),
    RELATIONSHIP_BINDING("relationshipBingding","电子账户与企业账簿绑定关系"),
    SET_ACCOUNT_ENTRY_SIGN_HW("setAcountEntrySignHW","设置非绑定入金"),
    HOLD_NO_QUERY("holdNoQuery","查询圈存编号"),
    TRAP("trap","圈存"),
    SOLVE_TRAP("sloveTrap","解圈存"),
    DEPOSIT_AMOUNT_QUERY("depositAmountQuery","查询圈存金额"),
    TRAP_QUERY("trapQuery","回查圈存/解圈存结果"),
    E_ACC_LOGOUT_CHECK("eAccLogoutCheck","销户预检查接口"),
    ACCOUNT_LIFE_CYCLE("accountlifecycle","销户"),

    ACCOUNT_TRAP_MONEY("accountTrapMoney","二类户圈存"),

    BINDING_CARD_TYPE_HW("bindingCardTypeHW","增加账户绑定卡"),

    UNLOCK_BOUND_CARD_TYPE_HW("unlockBoundCardTypeHW","解除账户绑定卡"),

    QUERY_TRAP("queryTrap","二类户圈存回查"),

    RECHARGE("recharge","充值"),

    WITHDRAW("withdraw","提现"),

    QUERY_TRADE_RESULT("queryTradeResult","回查充值提现交易结果"),

    ACC_SYS_CUST_SIGN_AGRT_AUTH("accSys.custSignAgrtAuth","签署协议授权"),

    ACC_SYS_CUST_SIGN_AGRT_AUTH_QUERY("accSys.custSignAgrtAuthQuery","签署协议授权查询"),

    ACC_SYS_CUST_SIGN_AGRT_AUTH_CANCEL("accSys.custSignAgrtAuthCancel","签署协议授权撤销"),

    ACC_SYS_OPEN_ACCT_AGRT_POST_BACK("accSys.openAcctAgrtPostback","开户协议回传"),

    QUERY_CUST_SIGN_INFO("queryCustSignInfo","查询客户签约信息"),
    ;

    private String code;

    private String desc;

    CgbTradeCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
