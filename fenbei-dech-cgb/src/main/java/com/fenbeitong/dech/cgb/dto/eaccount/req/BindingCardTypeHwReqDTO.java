package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
@Data
public class BindingCardTypeHwReqDTO extends CgbCommonRequest {
    /**
     * 绑定卡号（一类户）
     */
    private String bindingCardNo;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 二类户
     */
    private String accountNo;
    /**
     * 手机号
     */
    private String mobileNo;
    /**
     * 手机动态验证码
     */
    private String msgCode;
    /**
     * 手机动态验证码
     */
    private String msgCodeCheckId;
    /**
     * cvn2
     * 可选
     */
    private String cvn2;
    /**
     * 卡有效期
     * 可选
     */
    private String cardExpirationDate;
}
