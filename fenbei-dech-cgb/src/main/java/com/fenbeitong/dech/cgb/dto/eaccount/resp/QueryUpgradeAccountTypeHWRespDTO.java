package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class QueryUpgradeAccountTypeHWRespDTO extends CgbCommonResponse {
    /**
     * 	交易状态	String	2	M	90	处理状态（0-成功，1-失败，2-异常）
     */
    private String tradeStatus;
    /**
     * 	开户类型	String	1	M		2-二类户+三类户 3-三类户
     */
    private String openType;
    /**
     * 	三类户-虚账户	String	32	C		三类户-虚账户
     */
    private String virtAccountNo;
    /**
     * 	二类户	String	32	C		二类户
     */
    private String eAccountNo;
    /**
     * 	开户状态	String	32	M		开户状态
     */
    private String accountStatus;
    /**
     * 	二类户-虚账户	String	32	C		二类户-虚账户
     */
    private String vireAccountNo;
    /**
     * 	三类户	String	32	C
     */
    private String tAccountNo;
    /**
     * 	签约协议号	String	32	M		成功交易时必回
     */
    private String bindProtocolCode;
    /**
     * 	开户/升级操作状态	String	32	M		开户/升级操作状态： SUCCESS – 开户/升级成功 FAIL – 开户/升级失败 PROCESS – 开户/升级正常处理中 返回成功时必须携带
     */
    private String operationStatus;
}
