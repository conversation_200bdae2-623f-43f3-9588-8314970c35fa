package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * 查询客户签约信息
 * 通过本接口查询二三类户绑定卡列表
 */
@Data
public class QueryCustSignInfoReqDTO  extends CgbCommonRequest {
    /**
     * 查询类型
     * 1-按证件查询
     */
    private String queryType;
    /**
     * 01-身份证
     */
    private String certType;
    /**
     * 身份证号码
     * 末位仅支持0-9和大写X。
     */
    private String certNo;
}
