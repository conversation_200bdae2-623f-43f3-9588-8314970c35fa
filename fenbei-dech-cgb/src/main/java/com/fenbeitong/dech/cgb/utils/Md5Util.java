package com.fenbeitong.dech.cgb.utils;

import java.io.File;
import java.io.FileInputStream;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
public class Md5Util {
    public static String getMD5(String fileName) throws Exception {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
//            String path = Md5Util.class.getClassLoader().getResource("").getPath();
            File file = new File(fileName);
            byte[] tmp;
            try (FileInputStream fi = new FileInputStream(file)) {
                byte[] buffer = new byte[8192];
                int length;
                while ((length = fi.read(buffer)) != -1) {
                    md5.update(buffer, 0, length);
                }
            }

            tmp = md5.digest();
            return byte2hex(tmp);
        } catch (Exception e) {
            throw e;
        }
    }

    public static String byte2hex(byte[] b) {
        StringBuffer hs = new StringBuffer();
        String stmp;
        for (int i = 0; i < b.length; i++) {
            stmp = Integer.toHexString(b[i] & 0xFF).toUpperCase();
            if (stmp.length() == 1) {
                hs.append("0").append(stmp);
            } else {
                hs.append(stmp);
            }
        }
        return hs.toString();
    }

    public static String getStringMD5(String str) throws Exception {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(Base64.getDecoder().decode(str));
            byte tmp[] = md5.digest();
            return Md5Util.byte2hex(tmp);
        } catch (Exception e) {
            throw e;
        }
    }
}
