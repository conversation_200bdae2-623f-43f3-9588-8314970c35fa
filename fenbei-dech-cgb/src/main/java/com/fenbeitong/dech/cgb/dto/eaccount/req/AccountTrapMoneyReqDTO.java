package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/14
 */
@Data
public class AccountTrapMoneyReqDTO extends CgbCommonRequest {
    /**
     * 子渠道	String	10	M		上送值跟“merchantNum商户编号”一致
     */
    private String sChannel;
    /**
     * 	账户类型	String	2	M		E-圈存账号送电子账户
     */
    private String tradeAccountType;
    /**
     * 	账号	String	25	M		二类户
     */
    private String accountNo;
    /**
     * 	验密标识	String	1	M
     * 	N-不验密
     * 	Y-验密
     *  行外商户送N
     */
    private String isVerifyPwd;
    /**
     * 	是否需要校验短信验证码标识	String	1	M		N-不校验
     *     Y-校验
     *         如
     */
    private String isVerifySMS;
    /**
     * 	手机号	String	11	C		isVerifySMS为Y时必送，指发送短信验证码的手机号
     */
    private String phoneNumber;
    /**
     * 	短信验证码	String	12	C		sVerifySMS为Y时必送
     */
    private String smsCode;
    /**
     * 	资金来源类型	String	2	M		33-余额支付
     * 2-卡代收支付（指从绑定卡代收到二类户）
     */
    private String incomeType;
    /**
     * 	卡号	String	20	C		资金来源类型为2时上送，账号的绑定卡号
     */
    private String cardNo;
    /**
     * 	交易附言	String	200	O		ncomeType为2时，选送。指送给绑定卡对手行的附言
     */
    private String remark;
    /**
     * 	套餐使用标记	String	1	M		N-不使用套餐
     */
    private String flag;
    /**
     * 	冻结金额	String	50	C		flag为N时必送，单位分。
     */
    private String freezeAmount;
    /**
     * 	冻结周期	String	4	C		flag为N时必送，单位:月，必须为整数。送9999表示长期有效
     */
    private String freezeTime;
    /**
     * 	支付类型	String	2	M		1-圈存;
     */
    private String payType;
    /**
     * 	是否追加圈存	String	1	M		Y-追加圈存；
     *     空或N-不追加圈存；（即生成新的圈存编号）
     */
    private String isAddFreezing;
    /**
     * 	圈存编号	String	12	C		isAddFreezing为Y，必送
     */
    private String freezeNo;
}
