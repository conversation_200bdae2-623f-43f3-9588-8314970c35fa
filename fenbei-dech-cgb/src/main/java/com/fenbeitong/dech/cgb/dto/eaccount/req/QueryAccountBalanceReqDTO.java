package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * 查询二三类户账户余额信息
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
public class QueryAccountBalanceReqDTO extends CgbCommonRequest {
    /**
     * 	账户类型	String	2	M		V：虚账户余额 E：电子账户
     */
    private String accountType;
    /**
     * 	虚账户类型	String	3	O		空/100：默认虚账户（不影响现有流程） 101：红包虚账户
     */
    private String businessType;
    /**
     * 	查询类型	String	1	O		空/0：虚账户+电子账户余额 1：只查虚账户余额
     */
    private String queryType;
    /**
     * 	二三类户卡号	String	30	M	6225687352XXXXXX
     */
    private String subAccNo;
    /**
     * 	渠道	String	5	M
     */
    private String channel;
}
