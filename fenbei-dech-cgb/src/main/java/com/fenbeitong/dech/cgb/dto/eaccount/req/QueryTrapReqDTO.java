package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * 二类户圈存回查
 * <AUTHOR>
 */
@Data
public class QueryTrapReqDTO extends CgbCommonRequest {
    /**
     * 分贝通固定送330
     */
    @JsonProperty("sChannel")
    private String sChannel;

    /**
     * 原交易报文头上送的请求流水号
     */
    private String oriFlowId;
}
