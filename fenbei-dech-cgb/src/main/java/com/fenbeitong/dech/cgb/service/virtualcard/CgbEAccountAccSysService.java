package com.fenbeitong.dech.cgb.service.virtualcard;


import com.fenbeitong.dech.cgb.dto.eaccount.req.*;
import com.fenbeitong.dech.cgb.dto.eaccount.resp.*;

/**
 * 广发电子账户类协议接口（虚拟卡）
 * <AUTHOR>
 * @date 2022/2/28
 */
public interface CgbEAccountAccSysService {
    /**
     * 签署协议授权
     * @param custSignAgrtAuthReqDTO 签署协议授权REQ
     * @return CustSignAgrtAuthRespDTO
     */
    CustSignAgrtAuthRespDTO custSignAgrtAuth(CustSignAgrtAuthReqDTO custSignAgrtAuthReqDTO);

    /**
     * 签署协议授权查询
     * @param custSignAgrtAuthQueryReqDTO
     * @return CustSignAgrtAuthQueryRespDTO
     */
    CustSignAgrtAuthQueryRespDTO custSignAgrtAuthQuery(CustSignAgrtAuthQueryReqDTO custSignAgrtAuthQueryReqDTO);

    /**
     * 签署协议授权撤销
     * @param custSignAgrtAuthCancelReqDTO
     * @return
     */
    CustSignAgrtAuthCancelRespDTO custSignAgrtAuthCancel(CustSignAgrtAuthCancelReqDTO custSignAgrtAuthCancelReqDTO);

    /**
     * 开户协议回传
     * @param openAcctAgrtPostbackReqDTO
     * @return
     */
    OpenAcctAgrtPostbackRespDTO openAcctAgrtPostback(OpenAcctAgrtPostbackReqDTO openAcctAgrtPostbackReqDTO);

    QueryCustSignInfoRespDTO queryCustSignInfo(QueryCustSignInfoReqDTO queryCustSignInfoReqDTO);
}
