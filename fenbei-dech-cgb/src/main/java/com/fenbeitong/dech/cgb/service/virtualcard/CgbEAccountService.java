package com.fenbeitong.dech.cgb.service.virtualcard;

import com.fenbeitong.dech.cgb.dto.eaccount.req.*;
import com.fenbeitong.dech.cgb.dto.eaccount.resp.*;

/**
 * 广发电子账户类接口（虚拟卡）
 * <AUTHOR>
 * @date 2022/2/28
 */
public interface CgbEAccountService {

    /**
     * 上传客户影像资料
     * @param uploadCustImageDataRequestDTO 影像资料
     * @return UploadCustImageDataResponseDTO
     */
    UploadCustImageDataRespDTO uploadCustImageData(UploadCustImageDataReqDTO uploadCustImageDataRequestDTO);

    /**
     * 发送短信验证码
     * @param sendMsgCodeReqDTO
     * @return SendMsgCodeRespDTO
     */
    SendMsgCodeRespDTO sendMsgCode(SendMsgCodeReqDTO sendMsgCodeReqDTO);

    /**
     * 开立银行账户
     * @param createAccountTypeOPReqDTO 开立银行账户
     * @return CreateAccountTypeOPRespDTO
     */
    CreateAccountTypeOPRespDTO createAccountTypeOP(CreateAccountTypeOPReqDTO createAccountTypeOPReqDTO);

    /**
     * 回查二三类户开户结果。
     * @param queryUpgradeAccountTypeHW 回查二三类户结果
     * @return QueryUpgradeAccountTypeHWRespDTO
     */
    QueryUpgradeAccountTypeHWRespDTO queryUpgradeAccountTypeHW(QueryUpgradeAccountTypeHWReqDTO queryUpgradeAccountTypeHW);

    /**
     * 查询账户余额
     * @param queryAccountBalanceReqDTO 查询账户余额
     * @return QueryAccountBalanceRespDTO
     */
    QueryAccountBalanceRespDTO queryAccountBalance(QueryAccountBalanceReqDTO queryAccountBalanceReqDTO);

    /**
     * 设置非绑定入金
     * @param setAcountEntrySignHWReqDTO 非绑定入金
     * @return SetAcountEntrySignHWRespDTO
     */
    SetAcountEntrySignHWRespDTO setAcountEntrySignHW(SetAccountEntrySignHWReqDTO setAcountEntrySignHWReqDTO);

    /**
     * 圈存
     * @param trapReqDTO 圈存请求DTO
     * @return TrapRespDTO
     */
    TrapRespDTO trap(TrapReqDTO trapReqDTO);

    /**
     * 解圈存
     * 通过本接口完成资金解圈存（回收资金）。资金链路：II类户→III类户→分贝通对公户。
     * 如解圈存资金超2000元，需要拆分多笔小于2000元交易完成解圈存。
     */
    SloveTrapRespDTO sloveTrap(SloveTrapReqDTO sloveTrapReqDTO);

    /**
     * 将二三类户与企业账簿做绑定。
     * @param bindAccountReqDTO 绑定账户请求
     */
    BindAccountRespDTO bindAccount(BindAccountReqDTO bindAccountReqDTO);

    /**
     * 查询省市区
     */
    QueryProvinceCityRespDTO queryProvinceCity(QueryProvinceCityReqDTO QueryProvinceCityReqDTO);
    /**
     * 2.2 查询职业
     * @param cgbQueryOccupationReqDTO
     * @return
     */
    QueryOccupationRespDTO queryOccupation(QueryOccupationReqDTO cgbQueryOccupationReqDTO);

    /**
     * 查询圈存编号
     */
    HoldNoQueryRespDTO holdNoQuery(HoldNoQueryReqDTO holdNoQueryReqDTO);

    /**
     * 查询圈存金额
     * @param depositAmountQueryReqDTO
     * @return
     */
    DepositAmountQueryRespDTO depositAmountQuery(DepositAmountQueryReqDTO depositAmountQueryReqDTO);

    /**
     * 回查圈存/解圈存结果
     * @param trapQueryReqDTO
     * @return
     */
    TrapQueryRespDTO trapQuery(TrapQueryReqDTO trapQueryReqDTO);

    /**
     * 销户
     */
    EAccLogoutCheckRespDTO eAccLogoutCheck(EAccLogoutCheckReqDTO eAccLogoutCheckReqDTO);

    /**
     * 销户
     * @return
     */
    AccountLifeCycleRespDTO accountLifeCycle(AccountLifeCycleReqDTO cgbAccountLifeCycleReqDTO);

    /**
     * 二类户圈存
     */
    AccountTrapMoneyRespDTO accountTrapMoney(AccountTrapMoneyReqDTO cgbAccountTrapMoneyReqDTO);

    /**
     * 增加账户绑定卡
     * @param bindingCardTypeHwReqDTO
     */
    BindingCardTypeHwRespDTO bindingCardTypeHW(BindingCardTypeHwReqDTO bindingCardTypeHwReqDTO);

    /**
     * 解除账户绑定卡
     * @param cgbUnlockBoundCardTypeHWReqDTO 解绑DTO
     * @return
     */
    UnlockBoundCardTypeHWRespDTO unlockBoundCardTypeHW(UnlockBoundCardTypeHWReqDTO cgbUnlockBoundCardTypeHWReqDTO);

    /**
     * 当二类户圈存接口【accountTrapMoney】请求超时，商户可通过本接口根据原交易请求流水号回查原交易结果。
     */
    QueryTrapRespDTO queryTrap(QueryTrapReqDTO queryTrapReqDTO);

    /**
     * 接口描述：本接口作用为从绑定银行卡充值到二类户可用余额。
     * @param rechargeReqDTO 充值DTO
     * @return
     */
    RechargeRespDTO recharge(RechargeReqDTO rechargeReqDTO);

    /**
     * 接口描述：本接口作用为从二类户可用余额提现到绑定银行卡。
     * @param withdrawReqDTO 提现DTO
     * @return
     */
    WithdrawRespDTO withdraw(WithdrawReqDTO withdrawReqDTO);

    /**
     * 回查充值提现交易结果 queryTradeResult
     * @param queryTradeResultReqDTO 查询交易结果DTO
     * @return QueryTradeResultRespDTO
     */
    QueryTradeResultRespDTO queryTradeResult(QueryTradeResultReqDTO queryTradeResultReqDTO);
}
