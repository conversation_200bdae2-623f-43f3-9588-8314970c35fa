package com.fenbeitong.dech.cgb.utils;

import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;

public class FinhubExceptionUtil {

    public static FinhubException exceptionFrom (GlobalExceptionEnum globalCoreResponseCode) {
        return new FinhubException(globalCoreResponseCode.getCode(), globalCoreResponseCode.getType(), globalCoreResponseCode.getMsg());
    }
}
