package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Data
public class HoldNoQueryRespDTO extends CgbCommonResponse {
    /**
     * 圈存编号
     */
    private List<HoldNoQueryDTO> holdList;
    /**
     * Y – 末页
     * 最后一页时需输出 Y，否则为N
     */
    private String lastPageFlag;
    /**
     * 商户编号
     */
    private String merchantNum;
    /**
     * 当前页号
     */
    private String pageNum;
    /**
     * 最后一页时返回实际记录行数，其他页都是满页行数
     */
    private String returnCount;
    /**
     * 总条数
     */
    private String turnPageTotalNum;
    /**
     * 总页数
     */
    private String turnPageTotalPage;
}
