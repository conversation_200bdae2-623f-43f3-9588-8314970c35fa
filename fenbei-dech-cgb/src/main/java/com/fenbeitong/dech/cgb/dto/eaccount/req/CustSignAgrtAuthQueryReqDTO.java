package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

@Data
public class CustSignAgrtAuthQueryReqDTO extends CgbCommonRequest {
    /**
     * 	姓名	String	150	M
     */
    private String clientName;
    /**
     * 	证件类型	String	5	M
     */
    private String certType;
    /**
     * 	证件号码	String	20	M
     */
    private String certNo;
    /**
     * 	业务类型	String	5	M		01-个人信息授权书
     */
    private String bussiType;
    /**
     * 	操作类型	String	5	M		01-用户确认
     */
    private String operateType;
    /**
     * 	账户产品码	String	10	M		YKB-易快报专用户
     */
    private String accProdCode;
    /**
     * 	业务场景码	String	10	M		5655-合思易商卡
     */
    private String bussiScenCode;
}
