package com.fenbeitong.dech.cgb.service.virtualcard.impl;

import com.fenbeitong.dech.cgb.CgbEAccountClient;
import com.fenbeitong.dech.cgb.dto.BaseHttpHeaderParam;
import com.fenbeitong.dech.cgb.dto.BaseRequestHttpHeader;
import com.fenbeitong.dech.cgb.dto.eaccount.req.*;
import com.fenbeitong.dech.cgb.dto.eaccount.resp.*;
import com.fenbeitong.dech.cgb.enums.CgbTradeCodeEnum;
import com.fenbeitong.dech.cgb.service.virtualcard.CgbEAccountAccSysService;
import com.fenbeitong.dech.cgb.utils.FinhubExceptionUtil;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CgbEAccountAccSysServiceImpl implements CgbEAccountAccSysService {
    @Autowired
    private CgbEAccountClient cgbEAccountClient;
    @Override
    public CustSignAgrtAuthRespDTO custSignAgrtAuth(CustSignAgrtAuthReqDTO custSignAgrtAuthReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.ACC_SYS_CUST_SIGN_AGRT_AUTH.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(custSignAgrtAuthReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(), CustSignAgrtAuthRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("custSignAgrtAuth error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_ACC_SYS_CUST_SIGN_AGRT_AUTH_ERROR);
        }
    }

    @Override
    public CustSignAgrtAuthQueryRespDTO custSignAgrtAuthQuery(CustSignAgrtAuthQueryReqDTO custSignAgrtAuthQueryReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.ACC_SYS_CUST_SIGN_AGRT_AUTH_QUERY.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(custSignAgrtAuthQueryReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(), CustSignAgrtAuthQueryRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("custSignAgrtAuthQuery error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_ACC_SYS_CUST_SIGN_AGRT_AUTH_QUERY_ERROR);
        }
    }

    @Override
    public CustSignAgrtAuthCancelRespDTO custSignAgrtAuthCancel(CustSignAgrtAuthCancelReqDTO custSignAgrtAuthCancelReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.ACC_SYS_CUST_SIGN_AGRT_AUTH_CANCEL.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(custSignAgrtAuthCancelReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(), CustSignAgrtAuthCancelRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("custSignAgrtAuthCancel error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_ACC_SYS_CUST_SIGN_AGRT_AUTH_QUERY_ERROR);
        }
    }

    @Override
    public OpenAcctAgrtPostbackRespDTO openAcctAgrtPostback(OpenAcctAgrtPostbackReqDTO openAcctAgrtPostbackReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.ACC_SYS_OPEN_ACCT_AGRT_POST_BACK.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(openAcctAgrtPostbackReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(), OpenAcctAgrtPostbackRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("openAcctAgrtPostback error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_ACC_SYS_OPEN_ACCT_AGRT_POST_BACK_ERROR);
        }
    }

    @Override
    public QueryCustSignInfoRespDTO queryCustSignInfo(QueryCustSignInfoReqDTO queryCustSignInfoReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_CUST_SIGN_INFO.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(queryCustSignInfoReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(), QueryCustSignInfoRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryCustSignInfo error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_QUERY_SIGN_INFO_ERROR);
        }
    }
}
