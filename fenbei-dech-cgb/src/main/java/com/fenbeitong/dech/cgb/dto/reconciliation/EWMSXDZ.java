package com.fenbeitong.dech.cgb.dto.reconciliation;

import lombok.Data;

import java.io.Serializable;

@Data
public class EWMSXDZ implements Serializable {

    /**
     * 银联交易流水
     */
    private String tradeFlowId;

    /**
     * 账号
     */
    private String accountNo;

    /**
     * 交易金额
     */
    private String tradeAmount;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 原银联交易流水
     */
    private String originalTradeFlowId;

    /**
     * 圈存编号
     */
    private String qcNo;


    public EWMSXDZ(String tradeFlowId, String accountNo, String tradeAmount, String tradeType, String tradeTime, String originalTradeFlowId, String qcNo) {
        this.tradeFlowId = tradeFlowId;
        this.accountNo = accountNo;
        this.tradeAmount = tradeAmount;
        this.tradeType = tradeType;
        this.tradeTime = tradeTime;
        this.originalTradeFlowId = originalTradeFlowId;
        this.qcNo = qcNo;
    }
}
