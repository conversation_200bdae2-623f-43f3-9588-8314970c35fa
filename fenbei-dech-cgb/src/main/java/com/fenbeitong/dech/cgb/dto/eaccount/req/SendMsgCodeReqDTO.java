package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * 发送短信验证码
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class SendMsgCodeReqDTO extends CgbCommonRequest {
    /**
     * 手机号
     */
    private String mobileNo;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 填写短信验证码中的业务描述，如第一个参数已满足业务描述长度，则本参数可为空。
     */
    private String msgContentPart1;
    /**
     * 填写短信验证码中的业务描述，如第一个参数已满足业务描述长度，则本参数可为空。
     */
    private String msgContentPart2;
    /**
     * 填写短信验证码中的业务描述，如第一、二个参数已满足业务描述长度，则本参数可为空。
     */
    private String msgContentPart3;
    /**
     * 短信业务代码
     * 默认“M7”
     */
    @JsonProperty("SMSID")
    private String smsId;
}
