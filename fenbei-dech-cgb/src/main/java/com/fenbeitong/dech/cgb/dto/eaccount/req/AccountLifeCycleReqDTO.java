package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Data
public class AccountLifeCycleReqDTO extends CgbCommonRequest {
    /**
     * 	电子账号	String	30	M
     */
    private String accountNo;
    /**
     * 	交易类型	String	2	M
     * 	CANCEL：账户销户
     */
    private String transType;
    /**
     * 	人脸照片	String	500	M
     * 	transType为CANCEL时必填
     *  用户核验照片，deflate压缩算法之后再Base64
     */
    private String faceImageContent;
    /**
     * 	短信验证码流水	String	36	M
     */
    private String codeSeq;
    /**
     * 	短信验证码	String	8	M
     */
    private String msgCode;
}
