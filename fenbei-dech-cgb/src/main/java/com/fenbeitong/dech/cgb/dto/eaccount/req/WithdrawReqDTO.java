package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

@Data
public class WithdrawReqDTO extends CgbCommonRequest {
    /**
     * 	发起方流水	String	30	C		前端发起唯一交易流水
     */
    private String sendFlowNo;
    /**
     * 	发送日期	String	8	M
     */
    private String sendDate;
    /**
     * 	付款账户	String	25	M		二类户账号
     */
    private String priAccNo;
    /**
     * 	收款账户	String	25	M		绑定一类户卡号
     */
    private String subAccNo;
    /**
     * 	付款金额	String	21	M	2000	单位为 分
     */
    private String payAmount;
    /**
     * 	币种	String	3	M	01	01-人民币
     */
    private String currencyType;
    /**
     * 	业务代码	String	6	C		默认为空
     */
    private String businessCode;
    /**
     * 	附言	String	200	O		如有附言，可填写
     */
    private String explain;
    /**
     * 	提现类型	String	5	C	0	0-实时提现
     */
    private String settleType;
    /**
     * 	收款账户类型	String	1	M		0-电子账户
     */
    private String accountType;
    /**
     * 	到账类型	String	1	M		0：实时入账
     */
    private String incomeType;
    /**
     * 	交易类型	String	2	M
     * 	01：无需验密
     *  02：必须验密[新增]
     */
    private String transType;
    /**
     * 	交易密码	String	512	C		transType为02必送
     */
    private String password;
    /**
     * 	虚账户出账业务类型	String	3	C		送空
     */
    private String businessType;
    /**
     * 	设备ID	String	64	O
     * 	如能获取必须上送，获取不到则送空。
     * 	送一个能代表设备唯一性的值
     */
    private String deviceId;
    /**
     * 	设备标识(银联)	String	64	O		如能获取必须上送，获取不到则送空。
     *     桌面系统采集硬盘序列号、安卓系统采集IMEI、IOS系统采集IDFV
     *     ipVersion	IP版本号	String	5	O		如能获取必须上送，获取不到则送空。
     *         04 ：IPV4 ；
     *         06：IPV6
     */
    private String deviceBankId;
    /**
     * 	内网IP地址	String	64	O		如能获取必须上送，获取不到则送空。
     *     IPV4：***.***.***.*** （格式要求：点分十进制，例如： ***********）
     *     IPV6 ：****:****:****:****:****:****:****:**** （格式要求：冒号分十六进制，例如：AD80:0000:0000:0000:ABAA:00C2:0002） 获取本机所在局域网内的的ip地址，当不在局域网时该值应该同外网IP地址，或可不传
     */
    private String interIpAddress;
    /**
     * 	用户访问的IP地址	String	64	O
     * 	如能获取必须上送，获取不到则送空。
     *     格式同内网IP，该值取客户端对应暴露给互联网的ip地址，例如所在局域网对外显示的一个IP地址。如: ************如: ************
     */
    private String ipAddress;
    /**
     * 	MAC地址	String	30	O
     * 	如能获取必须上送，获取不到则送空。
     *     设备的mac地址，如: 00-50-56-BD-53-77
     */
    private String mac;
    /**
     * 	LBS 信息	String	30	O
     * 	如能获取必须上送，获取不到则送空。
     *     经纬度，格式为纬度/经度，+表示北纬、东经，-表示南纬、西经。举例：+37.12/-121.23或者+37/-121
     */
    private String lbsInfo;
    /**
     * 	设备SIM卡号码	String	30	O
     * 	如能获取必须上送，获取不到则送空。
     *     采集客户使用手机发起交易时所用设备的手机号 格式要求：遵守E.164要求 例如：国家代码手机号码 +14168362570、+86137xxxxxxxx
     */
    private String simCardNumber;
    /**
     * 	设备SIM卡数量	String	4	O
     * 	如能获取必须上送，获取不到则送空。
     *         0：未插SIM卡 ；
     *         1：1张SIM卡 ；
     *         2：2张SIM卡；
     */
    private String simCardNum;
    /**
     * 	设备类型	String	12	O
     * 	如能获取必须上送，获取不到则送空。
     *         01：手机 ；
     *         02：平板 ；
     *         03：手表 ；
     *         04：PC ；
     *         05：公众号；
     *         06：其他；
     */
    private String deviceType;
    /**
     * 	设备型号名称	String	30	O
     * 	如能获取必须上送，获取不到则送空。
     * 	以本地取得的设备型号名称为准，无需转义。如iPhone，Sagit( Sagit(小米6) ，MT –TL00（华为Mate7）等，如是电脑，尽量采集系统型号
     */
    private String deviceName;

}
