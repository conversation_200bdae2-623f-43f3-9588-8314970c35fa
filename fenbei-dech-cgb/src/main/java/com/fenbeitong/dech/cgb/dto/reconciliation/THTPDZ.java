package com.fenbeitong.dech.cgb.dto.reconciliation;

import lombok.Data;

import java.io.Serializable;

@Data
public class THTPDZ implements Serializable {
    /**
     * 退票日期
     */
    private String refundTime;

    /**
     * 原交易流水号
     */
    private String originalTradeFlowId;

    /**
     * 付款方名称
     */
    private String payAccountName;

    /**
     * 付款方账号
     */
    private String payAccountNo;

    /**
     * 收款方名称
     */
    private String receiverAccountName;

    /**
     * 收款方账号
     */
    private String receiverAccountNo;

    /**
     * 交易金额
     */
    private String tradeAmount;

    public THTPDZ(String refundTime, String originalTradeFlowId, String payAccountName, String payAccountNo, String receiverAccountName, String receiverAccountNo, String tradeAmount) {
        this.refundTime = refundTime;
        this.originalTradeFlowId = originalTradeFlowId;
        this.payAccountName = payAccountName;
        this.payAccountNo = payAccountNo;
        this.receiverAccountName = receiverAccountName;
        this.receiverAccountNo = receiverAccountNo;
        this.tradeAmount = tradeAmount;
    }
}
