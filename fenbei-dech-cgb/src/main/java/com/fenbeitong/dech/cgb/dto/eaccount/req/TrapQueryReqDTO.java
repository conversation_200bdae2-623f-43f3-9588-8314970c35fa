package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Data
public class TrapQueryReqDTO extends CgbCommonRequest {
    /**
     * 分贝通固定送330
     */
    private String channel;
    /**
     * 分贝通固定送330
     */
    @JsonProperty("sChannel")
    private String sChannel;
    /**
     * 	原交易请求流水	String	32	M		原交易报文头请求流水
     */
    @JsonProperty("oriFlowId")
    private String oriFlowId;
}
