package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * 上传客户影像资料
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class UploadCustImageDataReqDTO extends CgbCommonRequest {

    /**
     * 图片类型1（身份证正面）
     */
    private String imageType1;
    /**
     * 影像类型
     * 可选
     * 01,02,03,04,05 jpg,png,jpeg,bmp,zip
     */
    private String imageType3;
    /**
     * 请求类型
     * 可选:开户时为空，其他情况下默认1
     */
    private String reqType;
    /**
     * 文件内容2（身份证反面)
     * 必填
     * 最大2048
     * 需使用DEFLATE压缩算法后,Base64编码的方式传输；
     */
    private String fileContent2;
    /**
     * 证件类型
     * 可选
     * 默认填01:身份证
     */
    private String certType;
    /**
     * 文件内容1（身份证正面）
     * 必传
     * 需使用DEFLATE压缩算法后,Base64编码的方式传输；
     */
    private String fileContent1;
    /**
     * 证件号
     */
    private String certNo;
    /**
     * 图片类型2（身份证反面）
     */
    private String imageType2;
    /**
     * 文件内容3（人像图片）
     */
    private String fileContent3;
}
