package com.fenbeitong.dech.cgb.dto.reconciliation;

import lombok.Data;

import java.io.Serializable;

@Data
public class ZBZDRZDZ implements Serializable {

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 交易流水号
     */
    private String tradeFlowNo;

    /**
     * 付款方名称
     */
    private String payAccountName;

    /**
     * 付款方账号
     */
    private String payAccountNo;

    /**
     * 付款方开户行
     */
    private String payAccountBank;

    /**
     * dd
     */
    private String dd;

    /**
     * 收款方名称
     */
    private String receiverAccountName;

    /**
     * 收款方开户行
     */
    private String receiverAccountBank;

    /**
     * 交易金额
     */
    private String tradeAmount;


    public ZBZDRZDZ(String tradeTime, String tradeFlowNo, String payAccountName, String payAccountNo, String payAccountBank, String receiverAccountName, String dd, String receiverAccountBank, String tradeAmount) {
        this.tradeTime = tradeTime;
        this.tradeFlowNo = tradeFlowNo;
        this.payAccountName = payAccountName;
        this.payAccountNo = payAccountNo;
        this.payAccountBank = payAccountBank;
        this.receiverAccountName = receiverAccountName;
        this.dd = dd;
        this.receiverAccountBank = receiverAccountBank;
        this.tradeAmount = tradeAmount;
    }
}
