package com.fenbeitong.dech.cgb.dto;

import lombok.Data;

/**
 * 广发Http请求头
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class BaseHttpHeader {
    /**
     * 公钥证书ID
     * 必传
     * 备注:非证书则使用公钥MD5 值
     */
    private String certId;
    /**
     * 请求报文签名值
     * 必传
     */
    private String signature;
    /**
     * 签名方法(SM2)
     * 必传
     */
    private String signType = "SM2";
    /**
     * 可选
     * 全报文加密时必填，支持国密
     * SM4 非对称加密算法
     */
    private String encryptType = "SM4";
    /**
     * 加密密钥
     * 全报文加密时
     * 必填，加密密钥动态生成，使用对方公用加密后填充到此域
     */
    private String encryptKey;

    /**
     * 商户应用编号
     * 可选
     * 填写在开放平台注册时分配的应用编号
     */
    private String appId;

    /**
     * requestBody
     */
    private String data;
}
