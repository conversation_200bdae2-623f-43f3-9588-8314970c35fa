package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Data
public class AccountLifeCycleRespDTO extends CgbCommonResponse {
    /**
     * 	二类户销户交易状态	String	2	C
     * 	33-销户成功
     * 33-销户失败
     * 02-销户异常
     */
    private String transStatus;
    /**
     * 	二类户提现交易状态	String	2	C		90-提现成功
     * 99-提现失败
     * 33-提现异常
     */
    private String withdrawStatus;
    /**
     * 	三类户或影子户销户交易状态	String	2	C		33-销户成功
     * 33-销户失败
     * 02-销户异常
     */
    private String transStatus1;
    /**
     * 	三类户或影子户提现交易状态	String	2	C		90-提现成功
     * 99-提现失败
     * 33-提现异常
     */
    private String withdrawStatus1;
}
