package com.fenbeitong.dech.cgb.service.acctmgr;

import com.fenbeitong.dech.cgb.dto.acctmgr.req.CancelAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.ChangeAccountInfoReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.QueryComoanyAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.RegistComoanyAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.RegistManagerAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.ReplaceCardReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.req.SendVerifyCodeReqDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.CommonStatusRespDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.QueryComoanyAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.RegistComoanyAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.acctmgr.resp.RegistManagerAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.eaccount.req.FrozenAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.eaccount.resp.FrozenAccountRespDTO;
import com.fenbeitong.dech.cgb.dto.search.req.MerchantAccountBalanceQueryReqDTO;
import com.fenbeitong.dech.cgb.dto.search.req.QueryCustAccountReqDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.MerchantAccountBalanceQueryRespDTO;
import com.fenbeitong.dech.cgb.dto.search.resp.QueryCustAccountRespDTO;

public interface CgbAccountService {

    /**
     * 发送短信验证码（账薄）
     *
     * @param sendVerifyCodeReqDTO
     * @return
     */
    Boolean sendVerifyCode(SendVerifyCodeReqDTO sendVerifyCodeReqDTO);

    /**
     * 注册账簿(企业)
     *
     * @param req
     * @return
     */
    RegistComoanyAccountRespDTO registComoanyAccount(RegistComoanyAccountReqDTO req);

    /**
     * 变更账簿信息
     *
     * @param req
     * @return
     */
    CommonStatusRespDTO changeAccountMobile(ChangeAccountInfoReqDTO req);

    /**
     * 查询企业账簿信息
     *
     * @param req
     * @return
     */
    QueryComoanyAccountRespDTO queryComoanyAccount(QueryComoanyAccountReqDTO req);

    /**
     * 注销账簿
     *
     * @param req
     * @return
     */
    CommonStatusRespDTO cancelAccount(CancelAccountReqDTO req);

    /**
     * 注册账薄（平台）
     *
     * @param req
     * @return
     */
    RegistManagerAccountRespDTO registManagerAccount(RegistManagerAccountReqDTO req);

    /**
     * 换卡（企业账簿）
     *
     * @param req
     * @return
     */
    CommonStatusRespDTO replaceCard(ReplaceCardReqDTO req);

    /**
     * 平台账簿信息查询
     *
     * @param req
     * @return
     */
    QueryCustAccountRespDTO queryCustAccount(QueryCustAccountReqDTO req);

    /**
     * 接口描述：查询实体专用对公户余额信息。
     * @param merchantAccountBalanceQueryReqDTO
     * @return
     */
    MerchantAccountBalanceQueryRespDTO merchantAccountBalanceQuery(MerchantAccountBalanceQueryReqDTO merchantAccountBalanceQueryReqDTO);

    /**
     * 通过本接口可以冻结/解冻供应商/采购商的企业账簿状态。当账簿处于冻结状态时，不允许做交易。
     * @param frozenAccountReqDTO
     * @return
     */
    FrozenAccountRespDTO frozenAccount(FrozenAccountReqDTO frozenAccountReqDTO);
}
