package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Data
public class HoldNoQueryReqDTO extends CgbCommonRequest {
    /**
     * 分贝通固定送330
     */
    private String channel;
    /**
     * 分贝通固定送330
     */
    @JsonProperty("sChannel")
    private String sChannel;
    /**
     * 	电子账号	String	30	M
     */
    private String accountNo;
    /**
     * 	每页行数	String	5	O		最大为10，不输入则默认最大值10。
     */
    private String turnPageShowNum;
    /**
     * 	查询页码	String	5	O
     * 	首次查询时送0，翻页时送需要查询的具体页码。比如当前是第2页，上翻则送1，下翻则送3。
     */
    private String turnPageBeginPage;
}
