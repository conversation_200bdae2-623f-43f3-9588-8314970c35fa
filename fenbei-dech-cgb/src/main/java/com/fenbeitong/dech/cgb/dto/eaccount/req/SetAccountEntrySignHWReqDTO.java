package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
public class SetAccountEntrySignHWReqDTO extends CgbCommonRequest {
    /**
     * 电子账户	String	32	M		三类户
     */
    @JsonProperty("eAccountNo")
    private String eAccountNo;
    /**
     * 渠道流水	String	32	M		渠道流水
     */
    private String channelSeq;
    /**
     * 时间	String	14	M		时间
     */
    private String transTime;
    /**
     * 入账标识	String	1	O		代收标识：空-充值2-不充值
     */
    private String incomeFlag;
}
