package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
public class SloveTrapReqDTO extends CgbCommonRequest {
    /**
     * 付款账户
     * 二类户
     */
    private String payAccount;
    /**
     * 付款账户名
     */
    private String payAccountName;
    /**
     * 收款账户
     *  对公虚账户
     */
    private String recAccount;
    /**
     * 收款账户名
     */
    private String recAccountName;
    /**
     * 金额
     * 单位：分
     */
    private String payAmount;
    /**
     * 需要解圈存的圈存编号
     */
    private String holdNo;
    /**
     * 需要恢复哪个账簿台账，则上送哪个账簿ID
     */
    @JsonProperty("custAccno")
    private String custAccno;
}
