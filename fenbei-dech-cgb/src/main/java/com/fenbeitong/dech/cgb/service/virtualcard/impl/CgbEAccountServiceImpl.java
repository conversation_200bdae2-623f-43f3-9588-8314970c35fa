package com.fenbeitong.dech.cgb.service.virtualcard.impl;

import com.fenbeitong.dech.cgb.CgbEAccountClient;
import com.fenbeitong.dech.cgb.dto.BaseHttpHeaderParam;
import com.fenbeitong.dech.cgb.dto.BaseRequestHttpHeader;
import com.fenbeitong.dech.cgb.dto.eaccount.req.*;
import com.fenbeitong.dech.cgb.dto.eaccount.resp.*;
import com.fenbeitong.dech.cgb.enums.CgbTradeCodeEnum;
import com.fenbeitong.dech.cgb.service.virtualcard.CgbEAccountService;
import com.fenbeitong.dech.cgb.utils.FinhubExceptionUtil;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 广发电子账户类接口（虚拟卡）
 * <AUTHOR>
 * @date 2022/2/28
 */
@Service
public class CgbEAccountServiceImpl implements CgbEAccountService {
    @Autowired
    private CgbEAccountClient cgbEAccountClient;

    @Override
    public UploadCustImageDataRespDTO uploadCustImageData(UploadCustImageDataReqDTO uploadCustImageDataRequestDTO) {

        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.UPLOAD_CUSTOMER_IMAGE_DATA.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(uploadCustImageDataRequestDTO,baseRequestHttpHeader);
            //影像接口需要
            baseHttpHeaderParam.setUrl(baseHttpHeaderParam.getUrl()+ "/apiUnionPipeline");
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(),UploadCustImageDataRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("uploadCustImageData error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_CREATE_UPLOAD_IMAGE_ERROR);
        }
    }

    @Override
    public SendMsgCodeRespDTO sendMsgCode(SendMsgCodeReqDTO sendMsgCodeReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.SEND_MSG_CODE.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(sendMsgCodeReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),SendMsgCodeRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("uploadCustImageData error",e);
        }
        return null;
    }

    @Override
    public CreateAccountTypeOPRespDTO createAccountTypeOP(CreateAccountTypeOPReqDTO createAccountTypeOPReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.CREATE_ACCOUNT_TYPE_OP.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(createAccountTypeOPReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),CreateAccountTypeOPRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("uploadCustImageData error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_CREATE_ACCOUNT_ERROR);
        }
    }

    @Override
    public QueryUpgradeAccountTypeHWRespDTO queryUpgradeAccountTypeHW(QueryUpgradeAccountTypeHWReqDTO queryUpgradeAccountTypeHW) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_UPGRADE_ACCOUNT_TYPE_HW.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(queryUpgradeAccountTypeHW,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(),QueryUpgradeAccountTypeHWRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryUpgradeAccountTypeHW error",e);
        }
        //TODO
        return null;
    }

    @Override
    public QueryAccountBalanceRespDTO queryAccountBalance(QueryAccountBalanceReqDTO queryAccountBalanceReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_ACCOUNT_BALANCE.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(queryAccountBalanceReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(),QueryAccountBalanceRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryUpgradeAccountTypeHW error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_QUERY_ACCOUNT_BALANCE_ERROR);
        }
    }

    @Override
    public SetAcountEntrySignHWRespDTO setAcountEntrySignHW(SetAccountEntrySignHWReqDTO setAcountEntrySignHWReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.SET_ACCOUNT_ENTRY_SIGN_HW.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(setAcountEntrySignHWReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),SetAcountEntrySignHWRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryUpgradeAccountTypeHW error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_SET_ACCOUNT_ENTRY_SIGN_HW_ERROR);
        }
    }

    @Override
    public TrapRespDTO trap(TrapReqDTO trapReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.TRAP.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(trapReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(),TrapRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("trap error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_TRAP_ERROR);
        }
    }

    @Override
    public SloveTrapRespDTO sloveTrap(SloveTrapReqDTO sloveTrapReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.SOLVE_TRAP.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(sloveTrapReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(),baseHttpHeaderParam.getSm4Key(),SloveTrapRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryUpgradeAccountTypeHW error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_SOLVE_TRAP_ERROR);
        }

    }

    @Override
    public BindAccountRespDTO bindAccount(BindAccountReqDTO bindAccountReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.RELATIONSHIP_BINDING.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(bindAccountReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),BindAccountRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryUpgradeAccountTypeHW error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_BIND_ACCOUNT_ERROR);
        }
    }

    @Override
    public QueryProvinceCityRespDTO queryProvinceCity(QueryProvinceCityReqDTO queryProvinceCityReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_PROVINCE_CITY.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(queryProvinceCityReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),QueryProvinceCityRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryProvinceCity error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_QUERY_PROVINCE_ERROR);
        }
    }

    @Override
    public QueryOccupationRespDTO queryOccupation(QueryOccupationReqDTO cgbQueryOccupationReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_OCCUPATION.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(cgbQueryOccupationReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),QueryOccupationRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryOccupation error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_QUERY_OCCUPATION_ERROR);
        }
    }

    @Override
    public HoldNoQueryRespDTO holdNoQuery(HoldNoQueryReqDTO holdNoQueryReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.HOLD_NO_QUERY.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(holdNoQueryReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),HoldNoQueryRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("holdNoQuery error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_HOLD_NO_QUERY_ERROR);
        }
    }

    @Override
    public DepositAmountQueryRespDTO depositAmountQuery(DepositAmountQueryReqDTO depositAmountQueryReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.DEPOSIT_AMOUNT_QUERY.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(depositAmountQueryReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),DepositAmountQueryRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("depositAmountQuery error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_DEPOSIT_AMOUNT_QUERY_ERROR);
        }
    }

    @Override
    public TrapQueryRespDTO trapQuery(TrapQueryReqDTO trapQueryReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.TRAP_QUERY.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(trapQueryReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),TrapQueryRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("trapQuery error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_TRAP_QUERY_ERROR);
        }
    }

    @Override
    public EAccLogoutCheckRespDTO eAccLogoutCheck(EAccLogoutCheckReqDTO eAccLogoutCheckReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.E_ACC_LOGOUT_CHECK.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(eAccLogoutCheckReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),EAccLogoutCheckRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("eAccLogoutCheck error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_E_ACC_LOGOUT_CHECK_ERROR);
        }
    }

    @Override
    public AccountLifeCycleRespDTO accountLifeCycle(AccountLifeCycleReqDTO accountLifeCycleReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.ACCOUNT_LIFE_CYCLE.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(accountLifeCycleReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),AccountLifeCycleRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("accountLifeCycle error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_ACCOUNT_LIFE_CYCLE_ERROR);
        }
    }

    @Override
    public AccountTrapMoneyRespDTO accountTrapMoney(AccountTrapMoneyReqDTO accountTrapMoneyReqDTO) {
        try {
            accountTrapMoneyReqDTO.setTradeAccountType("E");
            accountTrapMoneyReqDTO.setIsVerifyPwd("N");
            accountTrapMoneyReqDTO.setIsVerifySMS("N");
            //资金来源类型  1-余额支付。——分贝通只存在余额支付的场景，如要充值，则先调用【recharge充值】接口单独完成。 2-卡代收支付（指从绑定卡代收到二类户）
            accountTrapMoneyReqDTO.setIncomeType("1");
            accountTrapMoneyReqDTO.setFlag("N");
            accountTrapMoneyReqDTO.setFreezeTime("9999");
            accountTrapMoneyReqDTO.setPayType("1");
            accountTrapMoneyReqDTO.setIsAddFreezing("Y");
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.ACCOUNT_TRAP_MONEY.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(accountTrapMoneyReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),AccountTrapMoneyRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("accountTrapMoney error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_ACCOUNT_TRAP_MONEY_ERROR);
        }
    }

    @Override
    public BindingCardTypeHwRespDTO bindingCardTypeHW(BindingCardTypeHwReqDTO bindingCardTypeHwReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.BINDING_CARD_TYPE_HW.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(bindingCardTypeHwReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),BindingCardTypeHwRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("bindingCardTypeHW error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_BINDING_CARD_TYPE_ERROR);
        }
    }

    @Override
    public UnlockBoundCardTypeHWRespDTO unlockBoundCardTypeHW(UnlockBoundCardTypeHWReqDTO cgbUnlockBoundCardTypeHWReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.UNLOCK_BOUND_CARD_TYPE_HW.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(cgbUnlockBoundCardTypeHWReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),UnlockBoundCardTypeHWRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("unlockBoundCardTypeHW error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_UNLOCK_BOUND_CARD_TYPE_ERROR);
        }
    }

    @Override
    public QueryTrapRespDTO queryTrap(QueryTrapReqDTO queryTrapReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_TRAP.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(queryTrapReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),QueryTrapRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryTrap error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_QUERY_TRAP_ERROR);
        }
    }

    @Override
    public RechargeRespDTO recharge(RechargeReqDTO rechargeReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.RECHARGE.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(rechargeReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),RechargeRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("recharge error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_RECHARGE_ERROR);
        }
    }

    @Override
    public WithdrawRespDTO withdraw(WithdrawReqDTO withdrawReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.WITHDRAW.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(withdrawReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),WithdrawRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("withdraw error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_WITHDRAW_ERROR);
        }
    }

    @Override
    public QueryTradeResultRespDTO queryTradeResult(QueryTradeResultReqDTO queryTradeResultReqDTO) {
        try {
            BaseRequestHttpHeader baseRequestHttpHeader = new BaseRequestHttpHeader();
            baseRequestHttpHeader.setTradeCode(CgbTradeCodeEnum.QUERY_TRADE_RESULT.getCode());
            BaseHttpHeaderParam baseHttpHeaderParam = cgbEAccountClient.initParam(queryTradeResultReqDTO,baseRequestHttpHeader);
            return cgbEAccountClient.request(baseHttpHeaderParam.getUrl(),baseHttpHeaderParam.getBody(),baseHttpHeaderParam.getHeader(), baseHttpHeaderParam.getSm4Key(),QueryTradeResultRespDTO.class);
        }catch (Exception e){
            FinhubLogger.error("queryTradeResult error",e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalExceptionEnum.CGB_QUERY_TRADE_RESULT_ERROR);
        }
    }
}
