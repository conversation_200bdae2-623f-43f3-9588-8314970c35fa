package com.fenbeitong.dech.cgb.dto.reconciliation;

import lombok.Data;

import java.io.Serializable;

@Data
public class QCDZ implements Serializable {

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 交易类型 01-新增圈存；02-追加圈存；03-解圈存；
     */
    private String tradeType;

    /**
     * 圈存编号
     */
    private String qcNo;

    /**
     * 付款方名称
     */
    private String payAccountName;

    /**
     * 付款方账号
     */
    private String payAccountNo;

    /**
     * 付款方开户行
     */
    private String payAccountBank;

    /**
     * 收款方名称
     */
    private String receiverAccountName;

    /**
     * 收款方账号
     */
    private String receiverAccountNo;

    /**
     * 收款方开户行
     */
    private String receiverAccountBank;

    /**
     * 交易金额
     */
    private String tradeAmount;

    /**
     * 交易状态
     */
    private String tradeStatus;

    public QCDZ(String tradeTime, String requestNo, String tradeType, String qcNo, String payAccountName, String payAccountNo, String payAccountBank, String receiverAccountName, String receiverAccountNo, String receiverAccountBank, String tradeAmount, String tradeStatus) {
        this.tradeTime = tradeTime;
        this.requestNo = requestNo;
        this.tradeType = tradeType;
        this.qcNo = qcNo;
        this.payAccountName = payAccountName;
        this.payAccountNo = payAccountNo;
        this.payAccountBank = payAccountBank;
        this.receiverAccountName = receiverAccountName;
        this.receiverAccountNo = receiverAccountNo;
        this.receiverAccountBank = receiverAccountBank;
        this.tradeAmount = tradeAmount;
        this.tradeStatus = tradeStatus;
    }
}
