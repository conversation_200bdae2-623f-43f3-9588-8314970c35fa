package com.fenbeitong.dech.cgb.enums;

import com.google.common.collect.Lists;

import java.util.List;

public enum ReconciliationDocTypeEnum {
    RZYRDZ("RZYRDZ", "EFFM.COPC.", "账簿日终余额对账文件", "余额"),
    ZBTZDZ("ZBTZDZ", "EFFM.COPC.", "账簿调账对账文件", "交易"),
    ZBTXDZ("ZBTXDZ", "EFFM.COPC.", "账簿提现对账文件", "提现"),
    ZBBMZJTKDZ("ZBBMZJTKDZ", "EFFM.COPC.", "账簿不明资金退款对账文件", "不明资金退款"),
    ZBZDRZDZ("ZBZDRZDZ", "EFFM.COPC.", "账簿自动入账对账文件", "充值、不明资金入账"),
    THTPDZ("THTPDZ", "EFFM.COPC.", "他行退票对账文件", "退汇"),
    QCDZ("QCDZ", "EFFM.COPC.", "圈存和解圈存对账文件", "虚拟卡额度发放、回收"),
    CZDZ("CZDZ", "EFFM.COPC.", "充值对账文件", "错花还款充值"),
    TXDZ("TXDZ", "EFFM.COPC.", "提现对账文件", "错花还款退款"),
    ELHQCDZ("ELHQCDZ", "EFFM.COPC.", "二类户圈存对账文件", "错花还款圈存"),
    EWMJDKDZ("EWMJDKDZ", "EFFM.IPBC.", "扫码付对账文件", "虚拟卡扫码支付"),
    KJZFDZ("KJZFDZ", "EFFM.COPC.", "快捷支付对账文件", "虚拟卡快捷支付"),
    ;

    private String fileType;

    private String filePrefix;

    private String cgbDesc;

    private String fbtDesc;

    ReconciliationDocTypeEnum(String fileType, String filePrefix, String cgbDesc, String fbtDesc) {
        this.fileType = fileType;
        this.filePrefix = filePrefix;
        this.cgbDesc = cgbDesc;
        this.fbtDesc = fbtDesc;
    }

    public String getFileType() {
        return fileType;
    }


    public String getCgbDesc() {
        return cgbDesc;
    }

    public String getFbtDesc() {
        return fbtDesc;
    }

    public static List<ReconciliationDocTypeEnum> needReconciliationList() {
        List<ReconciliationDocTypeEnum> enums = Lists.newArrayList(RZYRDZ, ZBTZDZ, ZBTXDZ, ZBBMZJTKDZ, ZBZDRZDZ, THTPDZ, QCDZ, CZDZ, TXDZ, ELHQCDZ, EWMJDKDZ, KJZFDZ);
        return enums;
    }

    public static ReconciliationDocTypeEnum getFileTypeEnum(String fileType) {
        for (ReconciliationDocTypeEnum value : ReconciliationDocTypeEnum.values()) {
            if (value.getFileType().equals(fileType)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isCZDZ(String fileType) {
        return CZDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isTXDZ(String fileType) {
        return TXDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isELHQCDZ(String fileType) {
        return ELHQCDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isEWMJDKDZ(String fileType) {
        return EWMJDKDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isKJZFDZ(String fileType) {
        return KJZFDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isRZYRDZ(String fileType) {
        return RZYRDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isZBTZDZ(String fileType) {
        return ZBTZDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isZBTXDZ(String fileType) {
        return ZBTXDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isZBBMZJTKDZ(String fileType) {
        return ZBBMZJTKDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isZBZDRZDZ(String fileType) {
        return ZBZDRZDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isTHTPDZ(String fileType) {
        return THTPDZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isQCDZ(String fileType) {
        return QCDZ.getFileType().equals(fileType) ? true : false;
    }

    public String getFilePrefix() {
        return filePrefix;
    }
}
