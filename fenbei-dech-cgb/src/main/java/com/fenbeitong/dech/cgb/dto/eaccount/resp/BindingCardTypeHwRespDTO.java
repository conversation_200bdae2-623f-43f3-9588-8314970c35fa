package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * 增加账户绑定卡
 * <AUTHOR>
 * @date 2022/4/21
 */
@Data
public class BindingCardTypeHwRespDTO extends CgbCommonResponse {
    /**
     * 商户渠道标识
     */
    private String merchantNum;
    /**
     * 交易状态
     */
    private String tradeStatus;
    /**
     * 绑定关系协议号
     * 商户后续凭该协议号对绑定关系进行修改删除
     */
    private String bindProtocolCode;
}
