package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class CreateAccountTypeOPRespDTO extends CgbCommonResponse {
    /**
     * 	二类户-虚账户	String	30	O		二类户对应的虚账户
     */
    private String vireAccountNo;
    /**
     * 	三类户-虚账户	String	1	M		三类户对应的虚账户
     */
    private String virtAccountNo;
    /**
     * 	签约类型	String	1	M	1	签约类型：0-充值签约，1-提现签约,2-充值提现签约
     */
    private String signType;
    /**
     * 开户方式	String	1	C	1	2-二类户+三类户，3-三类户
     */
    private String openType;
    /**
     * 	三类户	String	32	C
     */
    private String tAccountNo;
    /**
     * 	二类户	String	32	C
     */
    private String eAccountNo;
    /**
     * 	签约协议号	String	32	C
     */
    private String protocolCode;
    /**
     * 	开户结果	String	2	M		0：成功 1：失败  2：异常
     */
    private String result;
    /**
     * 	首笔入金状态	String		M
     * 	OPENED-首笔入金失败
     * 	OPENING-首笔入金异常
     * 	ACTIVE-首笔入金成功
     * 	针对OPENED 和OPENING，调用【setAcountEntrySignHW】接口，上送“incomeFlag”字段要送空，需要银行充值后再设置非绑定入金标识
     */
    private String status;
}
