package com.fenbeitong.dech.cgb.service.trade;

import com.fenbeitong.dech.cgb.dto.acctmgr.resp.CommonStatusRespDTO;
import com.fenbeitong.dech.cgb.dto.trade.req.RefundFundReqDTO;
import com.fenbeitong.dech.cgb.dto.trade.req.SingleAdjustAccountsReqDTO;
import com.fenbeitong.dech.cgb.dto.trade.req.WithrawAccountReqDTO;

public interface CgbCapitalTradeService {

    /**
     * 单笔调账
     *
     * @param req
     * @return
     */
    CommonStatusRespDTO singleAdjustAccounts(SingleAdjustAccountsReqDTO req);

    /**
     * 账簿资金提现(单笔)
     *
     * @param req
     * @return
     */
    CommonStatusRespDTO withrawAccount(WithrawAccountReqDTO req);

    /**
     * 不明资金退款（原路退回）
     *
     * @param req
     * @return
     */
    CommonStatusRespDTO refundFund(RefundFundReqDTO req);
}
