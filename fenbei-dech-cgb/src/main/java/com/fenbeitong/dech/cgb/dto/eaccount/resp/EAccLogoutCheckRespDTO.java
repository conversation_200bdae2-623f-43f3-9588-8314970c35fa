package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Data
public class EAccLogoutCheckRespDTO extends CgbCommonResponse {
    /**
     * 	总状态
     * 	可选
     * 	33-成功，
     *  33-失败，
     *  2-异常
     */
    private String result;
    /**
     * 	是否允许销户
     * 	可选
     * 	false-不允许；
     *  true-允许
     */
    private String  isAllowedCancel;
    /**
     * 	销户原因错误信息
     * 	必选
     * 	销户原因错误信息（错误信息拼接，以逗号分隔）
     */
    private String reasonMsg;
}
