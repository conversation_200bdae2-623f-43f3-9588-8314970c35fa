package com.fenbeitong.dech.cgb.utils;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
public class CgbUtil {
    public static String sign(String srcString, String pvkName, String encoding) throws Exception {
//        String path = CgbUtil.class.getClassLoader().getResource("").getPath();
        byte[] pvkBytes = read(pvkName);
        if (pvkBytes.length > 32) {
            pvkBytes = CgbSM2Util.getPrivateKey(pvkBytes);
        }
        byte[] signedBytes = CgbSM2Util.sign(pvkBytes, srcString.getBytes(encoding));
        return Base64.getEncoder().encodeToString(signedBytes);
    }

    public static boolean veryfySign(String srcString, String respSignature, String pukName, String encoding) throws Exception {
        byte[] pukBytes = read(pukName);
        if (pukBytes.length > 64) {
            pukBytes = CgbSM2Util.getPublicKey(pukBytes);
        }
        return CgbSM2Util.verifySign(pukBytes, srcString.getBytes(encoding), Base64.getDecoder().decode(respSignature));
    }

    public static String sm2EncryptString(String srcString, String pukName, String encoding) throws Exception {
//        String path = CgbUtil.class.getClassLoader().getResource("").getPath();
        byte[] pukBytes = read(pukName);
        if (pukBytes.length > 64) {
            pukBytes = CgbSM2Util.getPublicKey(pukBytes);
        }
        byte[] encryptBytes = CgbSM2Util.encrypt(pukBytes, srcString.getBytes(encoding));
        return Base64.getEncoder().encodeToString(encryptBytes);
    }

    public static byte[] getKey(String srcString, String pvkName, String encoding) throws Exception {
        byte[] pvkBytes = read(pvkName);
        if (pvkBytes.length > 32) {
            pvkBytes = CgbSM2Util.getPrivateKey(pvkBytes);
        }

        byte[] encryptBytes = CgbSM2Util.decrypt(pvkBytes, Base64.getDecoder().decode(read(srcString)));
        return encryptBytes;
    }

    public static String decryptKeyFileBySM2(String srcString, String pvkName) throws Exception {
        return new String(getKey(srcString, pvkName, null));
    }

    public static byte[] read(String filePath) throws IOException {
        if (filePath == null) {
            throw new IllegalArgumentException("Illegal Argument: filePath");
        } else {
            FileInputStream fileInputStream = null;

            byte[] out;
            try {
                fileInputStream = new FileInputStream(filePath);
                out = new byte[fileInputStream.available()];
                byte[] buffer = new byte[0x10000];

                int rLength;
                for (int offset = 0; (rLength = fileInputStream.read(buffer, 0, buffer.length)) != -1; offset += rLength) {
                    System.arraycopy(buffer, 0, out, offset, rLength);
                }
            } finally {
                if (fileInputStream != null) {
                    try {
                        fileInputStream.close();
                    } catch (Exception var11) {
                    }
                }

            }

            return out;
        }
    }

    /**
     * 使用SM2公钥加密SM4密钥
     *
     * @param sm4Key
     * @return
     * @throws Exception
     */
    public static String sm2EncryptString(String sm4Key, String pukStr) throws Exception {
        byte[] pukBytes = Base64.getDecoder().decode(pukStr);
        if (pukBytes.length > 64) {
            pukBytes = CgbSM2Util.getPublicKey(pukBytes);
        }
        byte[] encryptBytes = CgbSM2Util.encrypt(pukBytes, sm4Key.getBytes());
        return Base64.getEncoder().encodeToString(encryptBytes);
    }

    /**
     * SM2私钥签名请求报文
     *
     * @param reqStr
     * @return
     * @throws Exception
     */
    public static String signData(String reqStr, String pvkStr) throws Exception {
        byte[] pvkBytes = Base64.getDecoder().decode(pvkStr);
        if (pvkBytes.length > 32) {
            pvkBytes = CgbSM2Util.getPrivateKey(pvkBytes);
        }
        byte[] signedBytes = CgbSM2Util.sign(pvkBytes, reqStr.getBytes());
        return Base64.getEncoder().encodeToString(signedBytes);
    }
}
