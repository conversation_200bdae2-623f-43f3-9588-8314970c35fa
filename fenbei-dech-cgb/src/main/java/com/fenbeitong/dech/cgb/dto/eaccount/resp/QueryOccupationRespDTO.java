package com.fenbeitong.dech.cgb.dto.eaccount.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Data
public class QueryOccupationRespDTO extends CgbCommonResponse {
    /**
     * 一级目录	String	100	M
     * 10000-党的机关、国家机关、群众团体和社会组织、企事业单位负责人|
     * 20000-专业技术人员|
     * 30000-办事人员和有关人员|
     * 40000-社会生产服务和生活服务人员|
     * 50000-农、林、牧、渔、水利生产及辅助人员|
     * 60000-生产、运输设备操作人员及有关人员|
     * 70000-军人|
     * 80000-无职业/自由职业者
     */
    @JsonProperty("oLIST1")
    private String oList1;
    /**
     * 	二级目录	String	100	M
     * 	10100-党政机关负责人|
     * 	10200-民主党派和工商联负责人|
     * 	10300-社会团体或组织负责人|
     * 	10400-基层群众自治组织负责人|
     * 	10500-企事业单位负责人|
     * 	20100-科研/工程技术人员|
     * 	20200-计算机/网络技术人员|
     * 	20300-医护专业人员|
     * 	20400-经济和金融专业人员（银行、保险、证券、会计、审计等专业人员）|
     * 	20500-司法专业人员（如律师、法官、检察官等）|
     * 	20600-宗教职业者（如牧师等）|
     * 	20700-教学人员（如教师等）|
     * 	20800-文学艺术体育专业人员（如编导、演员、运动员等）|
     * 	20900-新闻出版、文化专业人员（如记者、编辑、翻译、文化设计等）|
     * 	30100-办事人员（如公务员等）|
     * 	30200-安全和消防人员（如人民警察、保卫人员、消防应急救援人员等）|
     * 	30300-基层自治组织 （包括行业协会、居民委员会）人员|
     * 	30400-外国驻华机构/大使馆/国际组织驻华机构工作人员|
     * 	30500-驻外机构人员|
     * 	40100-批发与零售|
     * 	40200-交通运输仓储和邮政（如快递服务人员、司机、空乘、火车服务人员等）|
     * 	40300-住宿和餐饮|
     * 	40400-信息传输软件和信息服务|
     * 	40500-典当、拍卖、金属、艺术品交易人员|
     * 	40600-房地产、租赁、商务服务等|
     * 	40700-居民服务（如保姆、美容、保健、水电煤石化等服务人员等）|
     * 	40800-文化、体育和娱乐服务人员|
     * 	40900-健康服务人员|
     * 	49900-其他社会生产和生活服务人员（如客服）|
     * 	50100-农、林、牧、渔生产及辅助人员|
     * 	50200-水利设备管理养护人员|
     * 	60100-农副产品、食品、烟草业生产加工人员|
     * 	60200-纺织、木材、纸品、印刷业、文娱、橡胶、塑料、用品生产加工人员|
     * 	60300-石化医药、化学品生产加工人员|
     * 	60400-金属、矿物采掘、机械生产加工人员|
     * 	60500-汽车、铁路、船舶、航空、电子仪器等制造加工人员|
     * 	60600-计算机通信和其他电子设备制造人员|
     * 	60700-废弃资源综合利用加工人员|
     * 	60800-电力、热力、气体、水生产和输配人员|
     * 	60900-建筑施工、运输设备操作输配人员等|
     * 	70100-军人|
     * 	70200-武警|
     * 	80100-学龄前儿童|
     * 	80200-在校学生|
     * 	80300-离退休人员|
     * 	80400-个体户|
     * 	80500-全职家庭主妇|
     * 	80600-自由职业（如网店店主、微商经营者、网络主播、博主等）
     */
    @JsonProperty("oLIST2")
    private String oList2;
}
