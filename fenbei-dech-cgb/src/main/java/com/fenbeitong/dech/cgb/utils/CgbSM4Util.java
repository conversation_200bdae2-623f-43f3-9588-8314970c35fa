package com.fenbeitong.dech.cgb.utils;

import org.bouncycastle.crypto.BlockCipher;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.params.KeyParameter;

import java.security.SecureRandom;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
public class CgbSM4Util {

    private static final int SM4_ENCRYPT = 1;
    private static final int SM4_DECRYPT = 0;
    public static final int SM4_PKCS8PADDING = 1;
    public static final int SM4_NOPADDING = 0;
    public static final int SM4_KEY_128 = 128;

    public static byte[] generateKey(int keySize) {
        byte[] key = new byte[keySize / 8];
        SecureRandom sr = new SecureRandom();
        sr.nextBytes(key);

        return key;
    }

    public static byte[] encryptECB(byte[] data, byte[] key) {
        return encryptECB(data, key, SM4_PKCS8PADDING);
    }

    public static byte[] decryptECB(byte[] cipher, byte[] key) {
        return decryptECB(cipher, key, SM4_PKCS8PADDING);
    }

    public static byte[] encryptCBC(byte[] data, byte[] key, byte[] iv) {
        return encryptCBC(data, key, iv, SM4_PKCS8PADDING);
    }

    public static byte[] decryptCBC(byte[] cipher, byte[] key, byte[] iv) {
        return decryptCBC(cipher, key, iv, SM4_PKCS8PADDING);
    }

    public static byte[] encryptECB(byte[] data, byte[] key, int paddingMode) {
        BlockCipher engine = new SM4Engine();
        engine.init(true, new KeyParameter(key));
        if (paddingMode == SM4_PKCS8PADDING) {
            data = padding(data, SM4_ENCRYPT);
        } else {
            data = data.clone();
        }
        int length = data.length;
        for (int i = 0; length > 0; length -= 16, i += 16) {
            engine.processBlock(data, i, data, i);
        }
        return data;
    }

    public static byte[] decryptECB(byte[] cipher, byte[] key, int paddingMode) {
        BlockCipher engine = new SM4Engine();
        engine.init(false, new KeyParameter(key));
        int length = cipher.length;
        byte[] tmp = new byte[cipher.length];
        for (int i = 0; length > 0; length -= 16, i += 16) {
            engine.processBlock(cipher, i, tmp, i);
        }
        byte[] plain = null;
        if (paddingMode == SM4_PKCS8PADDING) {
            plain = padding(tmp, SM4_DECRYPT);
        } else {
            plain = tmp;
        }
        return plain;
    }


    public static byte[] encryptCBC(byte[] data, byte[] key, byte[] iv, int paddingMode) {
        BlockCipher engine = new SM4Engine();
        engine.init(true, new KeyParameter(key));
        if (paddingMode == SM4_PKCS8PADDING) {
            data = padding(data, SM4_ENCRYPT);
        } else {
            data = data.clone();
        }
        int length = data.length;
        iv = iv.clone();
        for (int i = 0; length > 0; length -= 16, i += 16) {

            for (int j = 0; j < 16; j++) {
                data[i + j] = ((byte) (data[i + j] ^ iv[j]));
            }
            engine.processBlock(data, i, data, i);
            System.arraycopy(data, i, iv, 0, 16);
        }
        return data;
    }

    public static byte[] decryptCBC(byte[] cipher, byte[] key, byte[] iv, int paddingMode) {
        BlockCipher engine = new SM4Engine();
        engine.init(false, new KeyParameter(key));
        int length = cipher.length;
        byte[] plain = new byte[cipher.length];
        iv = iv.clone();
        for (int i = 0; length > 0; length -= 16, i += 16) {

            engine.processBlock(cipher, i, plain, i);
            for (int j = 0; j < 16; j++) {
                plain[j + i] = ((byte) (plain[i + j] ^ iv[j]));
            }
            System.arraycopy(cipher, i, iv, 0, 16);
        }

        byte[] res = null;
        if (paddingMode == SM4_PKCS8PADDING) {
            res = padding(plain, SM4_DECRYPT);
        } else {
            res = plain;
        }
        return res;
    }

    private static byte[] padding(byte[] input, int mode) {
        if (input == null) {
            return null;
        }

        byte[] ret = (byte[]) null;
        if (mode == SM4_ENCRYPT) {
            int p = 16 - input.length % 16;
            ret = new byte[input.length + p];
            System.arraycopy(input, 0, ret, 0, input.length);
            for (int i = 0; i < p; i++) {
                ret[input.length + i] = (byte) p;
            }
        } else {
            int p = input[input.length - 1];
            ret = new byte[input.length - p];
            System.arraycopy(input, 0, ret, 0, input.length - p);
        }
        return ret;
    }
}
