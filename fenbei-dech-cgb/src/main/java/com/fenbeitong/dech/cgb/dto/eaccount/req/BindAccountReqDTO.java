package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
public class BindAccountReqDTO extends CgbCommonRequest {
    /**
     * 	电子账号	String	30	M
     */
    private String accountNo;
    /**
     * 	账簿ID	String	32	M
     */
    private String custAccno;
    /**
     * 	交易类型	String	2	M		01-绑定；02-解绑；
     */
    private String transType;
    /**
     * 分贝通固定送330
     */
    private String channel;
    /**
     * 分贝通固定送330
     */
    @JsonProperty("sChannel")
    private String sChannel;

}
