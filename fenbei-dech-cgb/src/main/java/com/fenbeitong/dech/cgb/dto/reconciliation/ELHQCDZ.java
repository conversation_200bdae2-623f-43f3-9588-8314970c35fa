package com.fenbeitong.dech.cgb.dto.reconciliation;

import lombok.Data;

import java.io.Serializable;

@Data
public class ELHQCDZ implements Serializable {

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 付款方名称
     */
    private String payAccountName;

    /**
     * 付款方账号
     */
    private String payAccountNo;

    /**
     * 付款方开户行
     */
    private String payAccountBank;

    /**
     * 交易金额
     */
    private String tradeAmount;

    /**
     * 交易状态
     */
    private String tradeStatus;

    public ELHQCDZ(String tradeTime, String requestNo, String tradeType, String payAccountName, String payAccountNo, String payAccountBank, String tradeAmount, String tradeStatus) {
        this.tradeTime = tradeTime;
        this.requestNo = requestNo;
        this.tradeType = tradeType;
        this.payAccountName = payAccountName;
        this.payAccountNo = payAccountNo;
        this.payAccountBank = payAccountBank;
        this.tradeAmount = tradeAmount;
        this.tradeStatus = tradeStatus;
    }
}
