package com.fenbeitong.dech.cgb.dto.eaccount.req;

import com.fenbeitong.dech.cgb.dto.CgbCommonRequest;
import lombok.Data;

/**
 * 开立银行账户
 * 接口描述：开户前先调用【上传客户影像资料 uploadCustImageData】上传身份证正反面影像，再【sendMsgCode发送短信验证码】获取短信验证码，最后调用本接口提交开户请求。本接口支持二+三类户同时开户、或单独开三类户。
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class CreateAccountTypeOPReqDTO extends CgbCommonRequest {
    /**
     * 	市
     * 	10
     * 	可选
     * 	上送对应市中文名称
     */
    private String city;
    /**
     * 	区
     * 	10
     * 	可选
     * 	上送对应区中文名称
     */
    private String cnty;
    /**
     * 	省
     * 	10
     * 	必填
     * 	上送省代码值
     */
    private String province;
    /**
     * 	卡号	String	32	M		绑定卡号（一类户）
     */
    private String cardNo;
    /**
     * 	证件号	String	18	M		证件号(X结尾时转成大写)
     */
    private String certNo;
    /**
     * 	姓名	String	60	M
     */
    private String realName;
    /**
     * 	手机号	String	11	M
     */
    private String mobileNo;
    /**
     * 	开户类型	String	1	M	2	2-二类户+三类户
     */
    private String openType;
    /**
     * 	地址	String	100	O
     */
    private String address;
    /**
     * 	职业	String	10	O
     */
    private String profession;
    /**
     * 证件有效期	String	8	O		证件有效期（yyyyMMdd）
     *
     */
    private String validDays;
    /**
     * 	手机动态验证码	String	6	M		手机动态验证码
     */
    private String verifyCode;
    /**
     * 	手机动态验证码流水号	String	16	M		手机动态验证码流水号
     */
    private String codeSeq;
    /**
     * 	影像编号	String	32	O
     */
    private String yxNO;
    /**
     * 	支持签约多张卡标志	String	1	C		0：不支持多张卡 1：支持多张卡
     */
    private String nFlag;
    /**
     * 	CVN2	String	3	C
     */
    private String cvn2;
    /**
     * 	卡有效期	String	4	C
     */
    private String expired;
    /**
     * 	认证通道	String	1	O	0	空或不传：原有通道 0、CUPS通道，暂不支持 1、中国银联新认证通道
     */
    private String modelType;
    /**
     * 	是否OCR	String	1	C	0	是否OCR，yxNO传入时必传： 1-是OCR 0-未OCR
     */
    private String isOCR;
    /**
     * 	渠道流水	String	32	M
     */
    private String channelSeq;
    /**
     * 	IP版本号	String	5	O		ip4, ip6
     */
    private String ipVersion;
    /**
     * 	内网IP地址	String	64	O		内网IP地址,本机的ip地址，如:***********
     */
    private String interIpAddress;
    /**
     * 	LBS 信息	String	30	O		经纬度，格式为纬度/经度，+表示北纬、东经，-表示南纬、西经。举例：+37.12/-121.23或者+37/-121
     */
    private String lbsInfo;
    /**
     * 	设备SIM卡号码	String	30	O		交易报文采集持卡人使用手机支付时所用设备的手机号 格式要求：遵守E.164要求 例如：国家代码-手机号码 +14168362570、+86137xxxxxxxx
     */
    private String simCardNumber;
    /**
     * 	设备SIM卡数量	String	4	O		0：未插SIM卡 1：1张SIM卡 2：2张SIM卡 其他取值保留使用
     */
    private String simCardNum;
    /**
     * 	设备类型	String	12	O		1：手机 ：手机 2：平板 ：平板 3：手表 ：手表 4：PC 其他 取值保留使用 取值保留使用 取值保留使用
     */
    private String deviceType;
    /**
     * 	设备型号名称	String	30	O		如 iPhone iPhone ，Sagit( Sagit( 小 米 6) ，MT -TL00 （华为 （华为 Mate7 Mate7Mate7 ）等
     */
    private String deviceName;
    /**
     * 	用户访问的IP地址	String	64	O		外网出口ip地址，如: ************
     */
    private String ipAddress;
    /**
     * 	设备标识(银联)	String	64	O		桌面系统采集硬盘序列号、安卓系统采集IMEI、IOS系统采集IDFV
     */
    private String deviceBankId;
    /**
     * 	设备ID	String	100	O		设备指纹采集的设备ID
     */
    private String deviceId;
    /**
     * 	MAC地址	String	30	O		Mac地址,设备的mac地址: 00-50-56-BD-53-77
     */
    private String clientMAC;
    /**
     * 	账务行所	String	20	C
     */
    private String accOpenNode;
}
