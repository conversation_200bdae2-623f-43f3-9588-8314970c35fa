package com.fenbeitong.dech.cgb.enums;

import org.apache.commons.lang3.StringUtils;

public enum ResultCodeEnum {

    SUCCESS("0000", "成功"),
    SUCCESS2("0", "成功"),
    ILLEGAL_PARAM("0001", "非法参数"),
    MESSAGE_FORMAT_ERROR("0002", "报文格式错误"),
    INTERFACE_NOT_DEFINED("0003", "接口未定义"),
    INTERFACE_NOT_AVAILABLE("0004", "接口不可用"),
    MERCHANT_NOT_AVAILABLE("0005", "商户不可用"),
    MERCHANT_APP_NOT_AVAILABLE("0006", "商户APP 不可用"),
    INTERFACE_NOT_SUBSCRIBE("0007", "接口未订阅"),
    MESSAGE_DECRYPT_ERROR("0008", "报文解密错误"),
    SIGN_ERROR("0009", "签名错误"),
    FIELD_DECRYPT_ERROR("0010", "字段解密失败"),
    REQUIRED_PARAM_MISS("0011", "必填参数缺失"),
    API_VISITS_TOO_MANY("0012", "当前系统访问量过大，请稍后再试"),
    MERCHANT_APP_TO_SYSTEM_VISITS_TOO_MANY("0013", "该业务访问量过大，请稍后再试"),
    MERCHANT_APP_TO_API_VISITS_TOO_MANY("0014", "当前应用访问量过大，请稍后再试"),
    UNKNOWN_SYSTEM_EXCEPTION("9000", "未知系统异常"),
    GWLN_EXCEPTION("GWLN2000", "主机通信异常"),

    EBLN0000("EBLN0000", "TODO待确认"),

    //虚拟卡-start
    //
    FM7930102("FM7930102", "尊敬的客户：由于您的身份证影像未能通过审核导致上传失败。您可重新上传有效的身份证影像继续办理，感谢您的配合！"),
    //createAccountTypeOP
    FM333701("FM333701", "开户类型有误"),
    FM7830111("FM7830111", "尊敬的客户：由于您的身份证影像未能通过审核导致上传失败。您可重新上传有效的身份证影像继续办理，感谢您的配合！"),
    FM7012205("FM7012205", "证件号或手机号已被注册");

    private String code;

    private String desc;

    ResultCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isSuccess(String code) {
        if (StringUtils.isNotBlank(code)) {
            if (SUCCESS.getCode().equals(code) || SUCCESS2.getCode().equals(code)) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }
}
