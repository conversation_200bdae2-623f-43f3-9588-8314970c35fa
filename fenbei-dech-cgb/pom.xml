<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fenbei-dech</artifactId>
        <groupId>com.fenbeitong</groupId>
        <version>5.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <version>${dech.version}</version>
    <artifactId>fenbei-dech-cgb</artifactId>

    <!--    跳过deploy到远程仓库-->
    <properties>
        <skip-maven-deploy>true</skip-maven-deploy>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-dech-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-dech-dto</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${zb.3rd.bcprov.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.fenbeitong</groupId>-->
<!--            <artifactId>finhub-dubbo</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.luastar</groupId>-->
<!--            <artifactId>swift-tools</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.commons</groupId>-->
<!--            <artifactId>commons-lang3</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>commons-logging</groupId>-->
<!--            <artifactId>commons-logging</artifactId>-->
<!--            <version>1.2</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.logging.log4j</groupId>-->
<!--            <artifactId>log4j-api</artifactId>-->
<!--            <version>2.11.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.logging.log4j</groupId>-->
<!--            <artifactId>log4j-core</artifactId>-->
<!--            <version>2.11.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.slf4j</groupId>-->
<!--            <artifactId>slf4j-log4j12</artifactId>-->
<!--            <version>1.7.25</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-configuration-processor</artifactId>-->
<!--            <optional>true</optional>-->
<!--        </dependency>-->
    </dependencies>
</project>
