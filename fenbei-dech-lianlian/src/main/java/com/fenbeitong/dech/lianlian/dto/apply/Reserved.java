package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class Reserved implements Serializable {
    @J<PERSON>NField(name = "frequency")
    private String frequency;

    @JSONField(name = "max_amt")
    private String maxAmt;

    @JSONField(name = "valid_month")
    private String validMonth;

    @JSONField(name = "time_unit")
    private String timeUnit;

    @JSONField(name = "remark")
    private String remark;
}
