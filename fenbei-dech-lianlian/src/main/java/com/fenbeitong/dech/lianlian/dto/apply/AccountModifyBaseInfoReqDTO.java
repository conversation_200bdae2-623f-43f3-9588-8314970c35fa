package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 基本信息变更
 */
@Data
public class AccountModifyBaseInfoReqDTO implements Serializable {
    /**
     * 请求单号
     */
    @JSONField(name = "txn_seqno")
    private String txnSeqno;
    /**
     * 结果通知地址
     */
    @JSONField(name = "notify_url")
    private String notifyUrl;
    /**
     * 商户号
     */
    @JSONField(name = "mch_id")
    private String mchid;
    /**
     * 主体信息
     */
    @JSONField(name = "subject_info")
    private SubjectInfo subjectInfo;

    /**
     * 经营信息
     */
    @JSONField(name = "business_info")
    private BusinessInfo businessInfo;

    /**
     * 特许资质
     */
    @JSONField(name = "business_qualifications")
    private List<BusinessQualification> businessQualifications;

    /**
     * 协议信息
     */
    @JSONField(name = "protocol_info")
    private ProtocolInfo protocolInfo;
    /**
     * 干系人信息
     */
    @JSONField(name = "related_personnel")
    private RelatedPersonnel relatedPersonnel;

}
