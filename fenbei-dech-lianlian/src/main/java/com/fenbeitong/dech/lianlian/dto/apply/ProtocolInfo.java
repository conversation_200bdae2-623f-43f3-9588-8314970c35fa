package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class ProtocolInfo implements Serializable {
    @JSONField(name = "sign_person")
    private String signPerson;

    @JSONField(name = "sign_method")
    private String signMethod;

    @JSONField(name = "procotol_doc")
    private String procotolDoc;

    @JSONField(name = "confirm_method")
    private String confirmMethod;

    @JSONField(name = "authorization_doc")
    private String authorizationDoc;
}
