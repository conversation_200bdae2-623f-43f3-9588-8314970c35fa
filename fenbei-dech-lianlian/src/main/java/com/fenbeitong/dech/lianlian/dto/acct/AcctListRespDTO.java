package com.fenbeitong.dech.lianlian.dto.acct;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.BaseRespDTO;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/27
 */
@Data
public class AcctListRespDTO extends BaseRespDTO {

	/**
	 * 交易结果代码 0000代表受理成功
	 */
	@JSONField(name = "ret_code")
	private String retCode;
	
	/**
	 * 交易结果描述
	 */
	@JSONField(name = "ret_msg")
	private String retMsg;
	
	private List<AcctDetailRespDTO> list;
}
