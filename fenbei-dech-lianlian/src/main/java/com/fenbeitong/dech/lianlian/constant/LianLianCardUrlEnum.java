package com.fenbeitong.dech.lianlian.constant;

public enum LianLianCardUrlEnum {
    CREATE_USER("/corp/v1/user/batch/push","商务卡-用户推送"),
    CARD_APPLY("/card/v1/account/apply","商务卡-卡申请"),

    CARD_MODIFY("/card/v1/account/modify","商务卡-卡信息变更"),

    CARD_BALANCE_ADJUST("/card/v1/account/balance/adjust","商务卡-卡余额调整"),
    CARD_DETAIL("/card/v1/account/detail","商务卡-卡详情"),

    CARD_LIMIT_MODIFY("/card/v1/account/modify/limit","商务卡-卡限额修改"),

    CARD_TEMPLATE("/card/v1/account/template","模版列表"),
    /**
     * 商务卡安全码为用户隐私数据，企业应当在每次用户获取时实时查询，不能将数据落库！
     * 仅支持VCC及TOKEN支付的卡查询
     */
    VCC2("/card/v1/account/security","卡安全码查询"),

    //实体卡申请
    PHYSICAL_CARD_APPLY("/card/v1/physcard/order/create","商务卡-卡申请(实体卡)"),

    PHYSICAL_CARD_APPLY_ORDER_QUERY("/card/v1/physcard/order/query","实体卡订单查询"),

    PHYSICAL_CARD_ACTIVE("/card/v1/physcard/active","实体卡激活"),

    PHYSICAL_CARD_ORDER_DETAIL("/card/v1/physcard/order/items","实体卡订单明细"),

    PHYSICAL_CARD_RESET_PING("/card/v1/physcard/resetPin","实体卡重设密码"),

    COMMON_CAPTCHA_APPLY("/captcha/v1/apply","验证码申请")
    ;
    private final String url;
    private final String desc;

    LianLianCardUrlEnum(String url,String desc){
        this.url = url;
        this.desc = desc;
    }

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }
}
