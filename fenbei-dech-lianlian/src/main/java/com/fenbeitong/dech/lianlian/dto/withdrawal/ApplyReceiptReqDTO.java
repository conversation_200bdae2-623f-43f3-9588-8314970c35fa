package com.fenbeitong.dech.lianlian.dto.withdrawal;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyReceiptReqDTO {

	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 商户提现单号
	 */
	@JSONField(name = "out_order_no")
	private String outOrderNo;
	
	/**
	 * 系统充值单号
	 */
	@JSONField(name = "order_no")
	private String orderNo;
	
	/**
	 * 商户回单申请流水
	 */
	@JSONField(name = "receipt_seqno")
	private String receiptSeqno;
	
}
