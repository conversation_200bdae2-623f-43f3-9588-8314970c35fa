package com.fenbeitong.dech.lianlian;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.fenbeitong.dech.lianlian.constant.LianLianAccountUrlEnum;
import com.fenbeitong.dech.lianlian.constant.LianlianPayContants;
import com.fenbeitong.dech.lianlian.util.CipherException;
import com.fenbeitong.dech.lianlian.util.RSA;
import com.fenbeitong.dech.lianlian.util.ResponseHttpCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.SignatureException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * <AUTHOR>
 * @date 2024年6月21日
*/
@Component
public class LianlianPayClient implements InitializingBean {

    @NacosValue("${lianlian.max.idle:5}")
    private int maxIdleConns;

    @NacosValue("${lianlian.keep.alive:5}")
    private int keepAliveDuration;

    @NacosValue("${lianlian.timeout.read:10}")
    private int readTimeout;

    @NacosValue("${lianlian.timeout.write:10}")
    private int writeTimeout;

    @NacosValue("${lianlian.timeout.conn:10}")
    private int connectTimeout;

    @NacosValue("${lianlian.key.private}")
    private String privateKey;

    @NacosValue("${lianlian.key.public}")
    private String publicKey;

    @NacosValue("${lianlian.host}")
    private String lianlianHost;
    @NacosValue("${lianlian.file.host}")
    private String lianlianFileHost;
    @NacosValue("${lianlian.apply.host}")
    private String lianlianApplyHost;


    @NacosValue("${lianlian.spno}")
    private String spNo;
    private OkHttpClient client;

    MediaType mediaType = MediaType.parse("application/json; charset=utf-8");

    private static final long REQUEST_TIME_THRESHOLD = TimeUnit.MINUTES.toSeconds(10);

    @Override
    public void afterPropertiesSet() throws Exception {
        ConnectionPool pool = new ConnectionPool(maxIdleConns, keepAliveDuration, TimeUnit.MINUTES);
        client = new OkHttpClient().newBuilder()
            .readTimeout(readTimeout, TimeUnit.SECONDS)
            .writeTimeout(writeTimeout, TimeUnit.SECONDS)
            .connectTimeout(connectTimeout, TimeUnit.SECONDS)
            .connectionPool(pool)
            .build();

    }

    /**
     *
     * @param <T>
     * @param requestBody
     * @return
     */
    public <T> T post(String uri, String requestBody, TypeReference<T> type, Map<String,String> headerParam) {
        try {
            FinhubLogger.info("【对接连连】uri->{},参数：{}", uri, requestBody);
            Request request = buildRequest(LianlianPayContants.POST, uri, requestBody, null,headerParam);
            return responseHandler(request, uri, type);
        } catch (Exception e) {
            FinhubLogger.error("【对接连连】请求：{}时异常，参数：{}", requestBody, uri, e);
            throw new FinhubException(); // TODO
        }
    }


    private Request buildRequest(String method, String uri, String requestBody, String queryString, Map<String,String> headerParam) throws CipherException {
		FinhubLogger.info("Request     >> " + requestBody);

        String signHeader = buildSignHeader(requestBody);
        String url = lianlianHost + uri;
        if (uri.equals(LianLianAccountUrlEnum.FILE_V1_UPLOAD_FILE.getUrl()) || uri.equals(LianLianAccountUrlEnum.PROTOCOL_DOWNLOAD.getUrl())){
            url = lianlianFileHost + uri;
        }
        if (uri.equals(LianLianAccountUrlEnum.CUSTOMER_ACCESS_APPLY.getUrl())
            ||uri.equals(LianLianAccountUrlEnum.CUSTOMER_ACCESS_QUERY.getUrl())
            ||uri.equals(LianLianAccountUrlEnum.CUSTOMER_MODIFY_PRODUCT_INFO.getUrl())
        ){
            url = lianlianApplyHost + uri;
        }
        if ("602303290000064192".equals(spNo)){
            if (uri.equals(LianLianAccountUrlEnum.FILE_V1_UPLOAD_FILE.getUrl()) ){
                url = lianlianFileHost + LianLianAccountUrlEnum.FILE_V1_UPLOAD_FILE_FAT.getUrl();
            }
            if (uri.equals(LianLianAccountUrlEnum.CUSTOMER_ACCESS_APPLY.getUrl())){
                url = lianlianApplyHost + LianLianAccountUrlEnum.CUSTOMER_ACCESS_APPLY_FAT.getUrl();
            }
            if (uri.equals(LianLianAccountUrlEnum.CUSTOMER_MODIFY_PRODUCT_INFO.getUrl())){
//                url = lianlianApplyHost + LianLianAccountUrlEnum.CUSTOMER_MODIFY_PRODUCT_INFO.getUrl();
                url = "https://test2.lianlianpay-inc.com/mpay-openapi/v1/customer/modify/productInfo";
            }
            if (uri.equals(LianLianAccountUrlEnum.CUSTOMER_MODIFY_BASE_INFO.getUrl())){
                url = "https://test2.lianlianpay-inc.com/mpay-openapi/v2/customer/modify/baseInfo";
            }
            if (uri.equals(LianLianAccountUrlEnum.CUSTOMER_ACCESS_QUERY.getUrl())){
                url = lianlianApplyHost + LianLianAccountUrlEnum.CUSTOMER_ACCESS_QUERY_FAT.getUrl();
            }
            if (uri.equals(LianLianAccountUrlEnum.PROTOCOL_DOWNLOAD.getUrl())){
                url = lianlianApplyHost + LianLianAccountUrlEnum.PROTOCOL_DOWNLOAD.getUrl();
                url = url.replace("mpay-openapi","merchant");
            }
        }
        if (StringUtils.isNotBlank(queryString)) {
            url += ("?" + queryString);
        }

        Request.Builder builder = new Request.Builder()
            .url(url)
            .addHeader(LianlianPayContants.SIGNATURE_TYPE_KEY, LianlianPayContants.SIGNATURE_TYPE_VALUE)
            .addHeader(LianlianPayContants.SIGNATURE_DATA_KEY, signHeader)
            .addHeader("sp_no",spNo)
            .addHeader("timestamp",timestamp())
            .addHeader("nonce", RandomUtil.randomNumbers(11));
        //协议下载不传,传了会验签
        if (!uri.equals(LianLianAccountUrlEnum.PROTOCOL_DOWNLOAD.getUrl())) {
            if (headerParam != null && headerParam.containsKey("mch_id") && headerParam.get("mch_id") != null) {
                builder.addHeader("mch_id", headerParam.get("mch_id"));
            }
        }
        if (StringUtils.equalsIgnoreCase(LianlianPayContants.POST, method)) {
            RequestBody body = RequestBody.create(mediaType, requestBody);
            builder.post(body);
        } else if (StringUtils.equalsIgnoreCase(LianlianPayContants.GET, method)) {
            builder.get();
        }

        return builder.build();
    }

    private String timestamp(){
       long timestamp = System.currentTimeMillis();
       Date date = new Date(timestamp);
       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return simpleDateFormat.format(date);
    }

    private StringBuilder buildSignBuilder(String method, String uri, String requestBody, String queryString) throws UnsupportedEncodingException {
        StringBuilder signBuilder = new StringBuilder();
        signBuilder.append(method)
                .append(LianlianPayContants.CONCAT_STR).append(uri)
                .append(LianlianPayContants.CONCAT_STR).append(LianlianPayContants.epoch)
                .append(LianlianPayContants.CONCAT_STR).append(requestBody);
        if (StringUtils.isNotBlank(queryString)) {
            signBuilder.append(LianlianPayContants.CONCAT_STR).append(URLEncoder.encode(queryString, LianlianPayContants.ENCODE));
        }
        return signBuilder;
    }

    /**
     * Signature-Data	是	签名值RSA签名，见安全签名机制
     * Signature-Data = Base64(RSA(MD5(完整JSON报文)))
     */
    private String buildSignHeader(String signBuilder) throws CipherException {
//        FinhubLogger.info("JSON报文: " + signBuilder);
        String md5Value = MD5.create().digestHex(signBuilder);
//        FinhubLogger.info("1.获取请求完整JSON报文，使用MD5计算出报文摘要值（全小写）: " + md5Value);
        String sign = RSA.sign(RSA.Mode.MD5withRSA, md5Value, privateKey);

//        FinhubLogger.info("2.将该摘要值按照指定的签名算法使用交易发送方的签名私钥进行签名，将签名值使用Base64转码得到签名串signature: " + sign);
//        FinhubLogger.info("     2.1: privateKey: " + privateKey);
//        FinhubLogger.info("     2.2: 签名模式: " + RSA.Mode.MD5withRSA.name());
//        String signature = Base64.encode(sign.getBytes());
//        FinhubLogger.info("3.将签名方式和签名值设置在http header中，并自定义如下对应属性：" + signature);
//        FinhubLogger.info("SIGN        >> " + signature);
        return sign;
    }
//	private String buildSignHeader(StringBuilder signBuilder) throws CipherException {
//		String sign = RSA.sign(RSA.Mode.SHA256withRSA, signBuilder.toString(), privateKey);
//		FinhubLogger.info("SIGN        >> " + sign);
//
//		String signHeader = String.format("t=%s,v=%s", LianlianPayContants.epoch, sign);
//		FinhubLogger.info("Sign Header >> " + signHeader);
//		return signHeader;
//	}

    public <T> T get(String uri, String queryString, TypeReference<T> type,String mchId, Map<String,String> headerParam) {
        try {
            FinhubLogger.info("【对接连连】uri->{},参数：{}", uri, queryString);
            Request request = buildRequest(LianlianPayContants.GET, uri, "", queryString,headerParam);
            return responseHandler(request, uri, type);
        } catch (Exception e) {
            FinhubLogger.error("【对接连连】请求：{}时异常，参数：{}", queryString, uri, e);
            throw new FinhubException(); // TODO
        }
    }

    /**
     * 处理响应结果 Processing response results.
     * @param uri
     * @throws Exception
     */
    private  <T> T responseHandler(Request request, String uri, TypeReference<T> type) throws Exception {
        FinhubLogger.info("\n request method : {} \n request url: {} \n request headers: {} \n request body: {}",
            request.method(), URLEncoder.encode(request.url().toString()), request.headers(), request.body());
        try (Response response = client.newCall(request).execute()) {
            int responseCode = response.code();
            String responseBody = response.body().string();
            FinhubLogger.info("【对接连连】Request uri >> {} HTTP code>>{} result >> {}", uri, responseCode, responseBody);

            if (ResponseHttpCode.OK == responseCode && verifyResponse(response, responseBody)) {
                FinhubLogger.info("The signature is passed.");
                return JSON.parseObject(responseBody, type);
            }
        } catch (Exception e) {
            throw e;
        }

        return null;
    }

    /**
     * 验签，检验返回的真实性
     * Check the signature and verify the authenticity of the return.
     *
     * @param response
     * @param responseBody
     * @return
     */
    protected boolean verifyResponse(Response response, String responseBody) {
        try {
//            String llpSignHeader = response.header(LianlianPayContants.SIGNATURE_HEADER);
//            if (StringUtils.isBlank(llpSignHeader)) {
//            	return true;
//            }
//            llpSignHeader = llpSignHeader.trim();
//            String[] arr = llpSignHeader.split(",");
//            String responseEpoch = arr[0].substring("t=".length());
//            String responseSign = arr[1].substring("v=".length());
//            // 确认epoch时间
//            String epoch = validEpoch(responseEpoch);
//            return RSA.verify(RSA.Mode.SHA256withRSA, epoch + LianlianPayContants.CONCAT_STR + responseBody, responseSign, publicKey);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * epoch时间校验，与当前时间误差10分钟内
     * Epoch time verification, within 10 minutes from the current time.
     *
     * @param epoch
     * @return
     * @throws SignatureException
     */
    private String validEpoch(String epoch) throws SignatureException {
        try {
            long t = Long.parseLong(epoch);
            if (Math.abs(System.currentTimeMillis() / 1000 - t) > REQUEST_TIME_THRESHOLD) {
                throw new SignatureException("Invalid signature time.");
            }
            return epoch;
        } catch (Exception e) {
            throw new SignatureException("Invalid signature time.");
        }
    }

}
