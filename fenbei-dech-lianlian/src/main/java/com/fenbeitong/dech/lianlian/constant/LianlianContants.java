package com.fenbeitong.dech.lianlian.constant;

/**  
 * @Description: 
 * <AUTHOR>
 * @date 2024年6月21日 
*/
public interface LianlianContants {
	/**
     * 沙盒环境host
     * Sandbox environment host.
     */
    String SAND_BOX_HOST = "https://test-global-api.lianlianpay-inc.com";

    /**
     * 线上环境host
     * Online environment host.
     */
    String ONLINE_HOST = "https://global-api.lianlianpay.com";

    /**
     * 编码格式 Coding format
     */
    String ENCODE = "UTF-8";

    /**
     * 连接字符串 Concat string
     */
    String CONCAT_STR = "&";

    /**
     * 授权头 Authorization header
     */
    String AUTHORIZATION = "Authorization";

    /**
     * 签名头 Signature header
     */
    String SIGNATURE_HEADER = "LLPAY-Signature";

    /**
     * 连连提供的developId
     * DEVELOP_ID, Provided by LianLian
     */
    String DEVELOP_ID = "48dU96IgR349BfY9hJyq8Z1ad";

    /**
     * 连连提供的token
     * TOKEN, Provided by <PERSON><PERSON><PERSON><PERSON>
     */
    String TOKEN = "OapZWvNz3ZxJB0yX24nvirN0k9yv5Vma";

    /**
     * Basic 认证
     */
    String BASIC_TOKEN = "Basic ";

    /**
     * Bearer 认证 Basic authentication
     */
    String BEARER_TOKEN = "Bearer <<accessToken>>";


    String epoch = System.currentTimeMillis() / 1000 + "";

    String POST = "POST";

    String GET = "GET";
    
    String USER_AUTH = "/st-user/v1/stapi/userAuth";
    
    String QUERY_USER_AUTH = "/st-user/v1/stapi/userAuthList";
    
    String QUERY_ACCT_BALANCE = "/st-user/v1/stapi/userAccountQuery";
    
    String QUERY_ACCT_FLOW = "/st-user/v1/stapi/userAccountFlowQuery";
    
    String QUERY_PAYIN_LIST = "/st-user/v1/stapi/queryEntryList";
    
    String QUERY_PAYIN_DETAIL = "/st-user/v1/stapi/queryEntry";
    
    String QUERY_VCC_ACCT_BALANCE = "/st-user/v1/stapi/vcc/account";
    
    String APPLY_VCC_CARD = "/st-user/v1/stapi/vcc/card";
    
    String QUERY_VCC_CARD = "/st-user/v1/stapi/vcc/card/detail";
    
    String QUERY_VCC_CVV = "/st-user/v1/stapi/card/cvv2";
    
    String QUERY_VCC_CARD_LIST = "/st-user/v1/stapi/vcc/card/list";
    
    String UPDATE_VCC_CARD = "/st-user/v1/stapi/vcc/card/update";
    
    String CANCEL_VCC_CARD = "/st-user/v1/stapi/vcc/card/block";
    
    String FEEZE_VCC_CARD = "/st-user/v1/stapi/vcc/card/freeze";
    
    String UNFEEZE_VCC_CARD = "/st-user/v1/stapi/vcc/card/unfreeze";
    
    String QUERY_VCC_TRANSACTION = "/st-user/v1/stapi/vcc/transactions/topup";
    
    String QUERY_VCC_TX_AUTH = "/st-user/v1/stapi/vcc/transactions/auth";
    
    String QUERY_VCC_BILL_LIST = "/st-user/v1/stapi/vcc/transactions/settle";
    
    String UPDATE_VCC_TX_LIMIT = "/st-user/v1/stapi/vcc/card/limit/update";
    
    String QUERY_VCC_TX_LIMIT = "/st-user/v1/stapi/vcc/card/limit/view";
    
    String UPDATE_VCC_TX_SCENARIO = "/st-user/v1/stapi/vcc/card/scenario/update";
    
    String QUERY_VCC_TX_SCENARIO = "/st-user/v1/stapi/vcc/card/scenario/view";
    
    // -----------以下是新服务------------
    
    String WITHDRAWAL = "/account/v1/withdrawal/apply";
    
//    String SMS_VERIFY = "/trade/smsverify";
    
    String SMS_VERIFY = "/account/v1/withdrawal/verify";
    
//    String DOWNLOAD_RECEIPT = "/mch/v1/receipt/apply";
    
    String DOWNLOAD_RECEIPT = "/account/v1/receipt/apply";
    
//    String QUERY_RECEIPT_PROGRESS = "/mch/v1/receipt/query";
    
    String QUERY_RECEIPT_PROGRESS = "/account/v1/receipt/query";
    
    String TRADE_DETAIL = "/account/v1/serial";
    
    String QUERY_ACCT_DETAIL = "/account/v1/detail";
    
    String QUERY_ACCT_LIST = "/account/v1/list";
    
}
