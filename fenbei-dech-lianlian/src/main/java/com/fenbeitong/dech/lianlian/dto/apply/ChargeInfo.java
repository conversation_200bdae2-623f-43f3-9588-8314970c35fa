package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class ChargeInfo implements Serializable {
    @JSONField(name = "card_type")
    private String cardType;

    @JSONField(name = "bank_code")
    private String bankCode;

    @JSONField(name = "pay_type")
    private String payType;

    @JSONField(name = "rate")
    private String rate;

    @JSONField(name = "down_limit")
    private String downLimit;

    @JSONField(name = "up_limit")
    private String upLimit;
}
