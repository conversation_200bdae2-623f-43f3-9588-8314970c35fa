package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WebInfo implements Serializable {
    @JSONField(name = "icp_type")
    private String icpType;

    @JSONField(name = "domain")
    private String domain;

    @JSONField(name = "icp_license_num")
    private String icpLicenseNum;

    @JSONField(name = "icp_subject_name")
    private String icpSubjectName;

    @JSONField(name = "authority_doc")
    private List<String> authorityDoc;

    @JSONField(name = "other_files")
    private List<String> otherFiles;
}
