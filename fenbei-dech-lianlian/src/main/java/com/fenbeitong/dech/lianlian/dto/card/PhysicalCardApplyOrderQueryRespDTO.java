package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class PhysicalCardApplyOrderQueryRespDTO extends LianlianCardBaseRespDTO {

    @JSONField(name ="out_batch_no")
    private String outBatchNo;
    @JSONField(name ="order_status")
    private String orderStatus;
    @JSONField(name ="fail_code")
    private String failCode;
    @JSONField(name ="fail_reason")
    private String failReason;
    /**
     * 商务卡唯一编号
     */
    @JSONField(name ="delivery_logistics")
    private PhysicalCardDeliveryLogisticsAddress deliveryLogistics;

    @JSONField(name ="shipping_address")
    private PhysicalCardApplyShippingAddress shippingAddress;
}
