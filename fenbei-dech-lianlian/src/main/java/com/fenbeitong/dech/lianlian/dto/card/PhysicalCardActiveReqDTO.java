package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体卡对象
 */
@Data
public class PhysicalCardActiveReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     商务卡编号
     */
    @JSONField(name = "account_no")
    private String accountNo;
    /**
     * 调用方需要base64 encode
     */
    @JSONField(name = "security_code")
    private String securityCode;
    /**
     * 密码
     * 必需
     * 调用方需要加密传输
     * 避免使用：一样的数字，手机号某部分数字，卡号后四位
     */
    @JSONField(name = "pin")
    private String pin;
    /**
     * 验证码授权令牌
     */
    @JSONField(name = "token")
    private String token;

    /**
     * 验证码
     */
    @JSONField(name = "verify_code")
    private String verifyCode;



}
