package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UBOInfos implements Serializable {
    @JSONField(name = "identifying_method")
    private String identifyingMethod;

    @JSONField(name = "prove_files")
    private List<String> proveFiles;

    @JSONField(name = "ubo_list")
    private List<Ubo> uboList;
}
