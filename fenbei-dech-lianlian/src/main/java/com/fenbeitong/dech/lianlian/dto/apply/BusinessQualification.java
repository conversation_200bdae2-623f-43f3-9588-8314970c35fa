package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BusinessQualification implements Serializable {
    @JSONField(name = "qualification_name")
    private String qualificationName;
    @JSONField(name = "qualification_doc")
    private List<String> qualificationDoc;
    @JSONField(name = "validity_start_time")
    private String validityStartTime;
    @JSONField(name = "validity_end_time")
    private String validityEndTime;
}
