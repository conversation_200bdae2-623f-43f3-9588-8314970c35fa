package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class IdentityInfo implements Serializable {
    @JSONField(name = "operators_idcard")
    private String operatorsIdcard;

    @JSONField(name = "phone")
    private String phone;

    @JSONField(name = "age")
    private String age;

    @JSONField(name = "id_info")
    private IdInfo idInfo;

    @JSONField(name = "address_info")
    private AddressInfo addressInfo;
}
