package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体卡对象
 */
@Data
public class PhysicalCardResetPinReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     商务卡唯一编号
     */
    @JSONField(name = "account_no")
    private String accountNo;

    /**
     新密码
     */
    @JSONField(name = "new_pin")
    private String newPin;

    /**
     验证码授权令牌
     */
    @JSONField(name = "token")
    private String token;

    /**
     验证码
     */
    @JSONField(name = "verify_code")
    private String verifyCode;







}
