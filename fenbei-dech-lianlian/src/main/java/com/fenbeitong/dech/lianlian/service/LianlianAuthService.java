package com.fenbeitong.dech.lianlian.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fenbeitong.dech.lianlian.LianlianClient;
import com.fenbeitong.dech.lianlian.constant.LianlianContants;
import com.fenbeitong.dech.lianlian.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class LianlianAuthService {
    @Autowired
    private LianlianClient lianlianClient;

    public UserAuthRespDTO userAuth(UserAuthReqDTO request) {
        if (request.getMockFlag()){
            String data =  mockData(LianlianContants.USER_AUTH,0);
            return JSON.parseObject(data, new TypeReference<LianlianBaseRespDTO<UserAuthRespDTO>>() {}).getData();
        }
        LianlianBaseRespDTO<UserAuthRespDTO> resp = lianlianClient.post(LianlianContants.USER_AUTH, JSON.toJSONString(request), new TypeReference<LianlianBaseRespDTO<UserAuthRespDTO>>() {});
        if (Objects.isNull(resp) || Objects.isNull(resp.getData())) {
            return null;
        }

        return resp.getData();
    }

    public UserAuthListRespDTO queryUserAuth(UserAuthListReqDTO request) {
        if (request.getMockFlag()){
            String data =  mockData(LianlianContants.QUERY_USER_AUTH, request.getMockCount());
            return JSON.parseObject(data, new TypeReference<LianlianBaseRespDTO<UserAuthListRespDTO>>() {}).getData();
        }
        LianlianBaseRespDTO<UserAuthListRespDTO> resp = lianlianClient.post(LianlianContants.QUERY_USER_AUTH, JSON.toJSONString(request), new TypeReference<LianlianBaseRespDTO<UserAuthListRespDTO>>() {});
        if (Objects.isNull(resp) || Objects.isNull(resp.getData())) {
            return null;
        }

        return resp.getData();
    }

    public String mockData(String interfaceName,Integer dataCount){
        if (LianlianContants.USER_AUTH.equals(interfaceName)){
            return "{\n" +
                "        \"code\":\"SUCCESS\",\n" +
                "        \"data\":{\n" +
                "                \"thirdUserId\":\"20240520001\",\n" +
                "                \"bindingStatus\":\"N\",\n" +
                "                \"returnUrl\":\"https://global.lianlianpay-inc.com?token=1f89231a935341a\"\n" +
                "        },\n" +
                "        \"success\":true,\n" +
                "        \"message\":\"success\"\n" +
                "}";
        }
        if (LianlianContants.QUERY_USER_AUTH.equals(interfaceName)){
            if (dataCount == 0){
                return "{\n" +
                    "        \"code\":\"SUCCESS\",\n" +
                    "        \"data\":{\n" +
                    "                \"pageList\":[],\n" +
                    "                \"totalCount\":0\n" +
                    "        },\n" +
                    "        \"success\":true,\n" +
                    "        \"message\":\"success\"\n" +
                    "}";
            }
            return "{\n" +
                "        \"code\":\"SUCCESS\",\n" +
                "        \"data\":{\n" +
                "                \"pageList\":[\n" +
                "                        {\n" +
                "                                \"thirdUserId\":\"thirdUserId1715776598834\",\n" +
                "                                \"kycStatus\":\"0\",\n" +
                "                                \"bindingStatus\":\"Y\",\n" +
                "                                \"userId\":\"ssoUserId\"\n" +
                "                        }\n" +
                "                ],\n" +
                "                \"totalCount\":1\n" +
                "        },\n" +
                "        \"success\":true,\n" +
                "        \"message\":\"success\"\n" +
                "}";
        }
        return null;
    }
}

