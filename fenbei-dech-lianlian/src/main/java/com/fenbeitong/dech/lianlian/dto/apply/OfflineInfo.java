package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OfflineInfo implements Serializable {
    @JSONField(name = "operators_shop_photo")
    private List<String> operatorsShopPhoto;

    @JSONField(name = "door_head_photo")
    private List<String> doorHeadPhoto;

    @JSONField(name = "business_interior_photos")
    private List<String> businessInteriorPhotos;

    @JSONField(name = "cashier_photo")
    private List<String> cashierPhoto;

    @JSONField(name = "special_files")
    private List<String> specialFiles;
}
