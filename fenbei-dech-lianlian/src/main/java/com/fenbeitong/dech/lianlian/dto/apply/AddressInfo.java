package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class AddressInfo implements Serializable {
    @JSONField(name = "nationality")
    private String nationality;

    @JSONField(name = "province")
    private String province;

    @JSONField(name = "city")
    private String city;

    @JSONField(name = "area")
    private String area;

    @JSONField(name = "address")
    private String address;

    @JSONField(name = "longitude")
    private String longitude;

    @JSONField(name = "latitude")
    private String latitude;

}
