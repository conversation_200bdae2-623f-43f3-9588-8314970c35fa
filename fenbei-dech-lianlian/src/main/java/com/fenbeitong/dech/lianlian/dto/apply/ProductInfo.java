package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProductInfo implements Serializable {
    @JSONField(name = "product_code")
    private String productCode;
    @JSONField(name = "pay_type")
    private String payType;
    @JSONField(name = "vcc_biz_scene")
    private String vccBizScene;

    @JSONField(name = "wx_alipay_info")
    private WxAlipayInfo wxAlipayInfo;

    @JSONField(name = "reserved")
    private Reserved reserved;

    @JSONField(name = "charge_infos")
    private List<ChargeInfo> chargeInfos;

    @JSONField(name = "charge_template_id")
    private String chargeTemplateId;

}
