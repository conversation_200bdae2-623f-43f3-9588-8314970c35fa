package com.fenbeitong.dech.lianlian.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/06/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class QueryAcctChargeDetailReqDTO implements Serializable {

	/**
	 * 加款唯一单号
	 */
	private String id;
	
	/**
	 * 平台三方用户号
	 */
	private String thirdUserId;
}
