package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class PhysicalCardApplyShippingAddress implements Serializable {

    @JSONField(name = "province")
    private String province;

    @JSONField(name = "city")
    private String city;

    @JSONField(name = "district")
    private String district;

    @JSONField(name = "address")
    private String address;

    @JSONField(name = "receiver")
    private String receiver;

    @JSONField(name = "contact_mobile")
    private String contactMobile;

    @JSONField(name = "post_code")
    private String postCode;

}
