package com.fenbeitong.dech.lianlian.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fenbeitong.dech.lianlian.LianlianPayClient;
import com.fenbeitong.dech.lianlian.constant.LianLianAccountUrlEnum;
import com.fenbeitong.dech.lianlian.dto.apply.*;
import com.fenbeitong.dech.lianlian.dto.card.LianlianCardBaseRespDTO;
import com.fenbeitong.dech.lianlian.dto.file.FileUploadReqDTO;
import com.fenbeitong.dech.lianlian.dto.file.FileUploadRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class LianLianAccountApplyService {
    @Autowired
    private LianlianPayClient lianlianPayClient;


    public FileUploadRespDTO upload(FileUploadReqDTO uploadFileReqDTO){
        String requestData = JSON.toJSONString(uploadFileReqDTO);
        return lianlianPayClient.post(LianLianAccountUrlEnum.FILE_V1_UPLOAD_FILE.getUrl(),requestData, new TypeReference<FileUploadRespDTO>() {}, null);
    }
    /**
     * 进件
     */
    public LianlianCardBaseRespDTO apply(AccountApplyReqDTO accountApplyReqDTO){
        String requestData = JSON.toJSONString(accountApplyReqDTO);
        return lianlianPayClient.post(LianLianAccountUrlEnum.CUSTOMER_ACCESS_APPLY.getUrl(),requestData, new TypeReference<LianlianCardBaseRespDTO>() {}, null);
    }


    /**
     * sp/v1/customer/access/query
     * 进件结果查询
     */
    public AccountApplyResultRespDTO applyResultQuery(AccountApplyResultReqDTO accountApplyResultReqDTO){
        String requestData = JSON.toJSONString(accountApplyResultReqDTO);
        return lianlianPayClient.post(LianLianAccountUrlEnum.CUSTOMER_ACCESS_QUERY.getUrl(),requestData, new TypeReference<AccountApplyResultRespDTO>() {}, null);
    }


    /**
     * 基本信息变更
     */
    public LianlianCardBaseRespDTO modifyBaseInfo(AccountModifyBaseInfoReqDTO accountModifyBaseInfoReqDTO){
        String requestData = JSON.toJSONString(accountModifyBaseInfoReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",accountModifyBaseInfoReqDTO.getMchid());
        return lianlianPayClient.post(LianLianAccountUrlEnum.CUSTOMER_MODIFY_BASE_INFO.getUrl(),requestData, new TypeReference<LianlianCardBaseRespDTO>() {}, map);
    }



    /**
     * 产品信息变更
     */
    public LianlianCardBaseRespDTO modifyProductInfo(AccountModifyProductInfoReqDTO accountModifyProductInfoReqDTO){
        String requestData = JSON.toJSONString(accountModifyProductInfoReqDTO);
        return lianlianPayClient.post(LianLianAccountUrlEnum.CUSTOMER_MODIFY_PRODUCT_INFO.getUrl(),requestData, new TypeReference<LianlianCardBaseRespDTO>() {}, null);
    }


    /**
     * /account/v1/active/apply
     * 账户激活申请
     */
    public AccountActiveApplyRespDTO activeApply(AccountActiveApplyReqDTO accountActiveApplyReqDTO){
        String requestData = JSON.toJSONString(accountActiveApplyReqDTO);
        return lianlianPayClient.post(LianLianAccountUrlEnum.ACCOUNT_ACTIVE_APPLY.getUrl(),requestData, new TypeReference<AccountActiveApplyRespDTO>() {}, null);
    }

    /**
     * 账户激活申请
     */
    public LianlianCardBaseRespDTO activeVerify(AccountActiveVerifyReqDTO accountApplyResultReqDTO){
        String requestData = JSON.toJSONString(accountApplyResultReqDTO);
        return lianlianPayClient.post(LianLianAccountUrlEnum.ACCOUNT_ACTIVE_VERIFY.getUrl(),requestData, new TypeReference<LianlianCardBaseRespDTO>() {}, null);
    }

    public ProtocolDownloadRespDTO protocolDownload(ProtocolDownloadReqDTO protocolDownloadReqDTO){
        String requestData = JSON.toJSONString(protocolDownloadReqDTO);
        return lianlianPayClient.post(LianLianAccountUrlEnum.PROTOCOL_DOWNLOAD.getUrl(),requestData, new TypeReference<ProtocolDownloadRespDTO>() {}, null);
    }
}
