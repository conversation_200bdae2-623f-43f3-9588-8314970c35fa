package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class ContactInfo implements Serializable {
    @JSONField(name = "contact_type")
    private String contactType;

    @JSONField(name = "contact_name")
    private String contactName;

    @JSONField(name = "contact_phone")
    private String contactPhone;

    @JSONField(name = "contact_email")
    private String contactEmail;
}
