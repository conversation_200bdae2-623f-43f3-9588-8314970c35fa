package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class PhysicalCardApplyRespDTO extends LianlianCardBaseRespDTO {
    @JSONField(name ="out_batch_no")
    private String outBatchNo;
    /**
     * 商务卡唯一编号
     */
    @JSONField(name ="card_list")
    private List<PhysicalCardListResp> cardLists;
}
