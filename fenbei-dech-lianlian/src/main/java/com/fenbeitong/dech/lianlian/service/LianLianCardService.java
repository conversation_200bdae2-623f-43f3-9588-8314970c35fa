package com.fenbeitong.dech.lianlian.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fenbeitong.dech.lianlian.LianlianPayClient;
import com.fenbeitong.dech.lianlian.constant.LianLianCardUrlEnum;
import com.fenbeitong.dech.lianlian.dto.card.*;
import com.fenbeitong.dech.lianlian.dto.template.TemplateReqDTO;
import com.fenbeitong.dech.lianlian.dto.template.TemplateRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class LianLianCardService {
    @Autowired
    private LianlianPayClient lianlianPayClient;

    public CreateUserRespDTO createUser(CreateUserReqDTO createUserReqDTO){
        String requestData = JSON.toJSONString(createUserReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",createUserReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.CREATE_USER.getUrl(),requestData, new TypeReference<CreateUserRespDTO>() {}, map);
    }
    /**
     * /card/v1/account/apply
     * 根据企业站创建的卡模板来申请的商务卡，卡申请通过后，会通过“卡状态变更通知”接口返回商务卡信息。
     */
    public PhysicalCardApplyRespDTO applyPhysicalCard(PhysicalCardApplyReqDTO physicalCardApplyReqDTO){
        String requestData = JSON.toJSONString(physicalCardApplyReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",physicalCardApplyReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.PHYSICAL_CARD_APPLY.getUrl(),requestData, new TypeReference<PhysicalCardApplyRespDTO>() {}, map);
    }

    public PhysicalCardApplyOrderQueryRespDTO physicalCardApplyOrderQuery(PhysicalCardApplyOrderQueryReqDTO physicalCardApplyOrderQueryReqDTO){
        String requestData = JSON.toJSONString(physicalCardApplyOrderQueryReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",physicalCardApplyOrderQueryReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.PHYSICAL_CARD_APPLY_ORDER_QUERY.getUrl(),requestData, new TypeReference<PhysicalCardApplyOrderQueryRespDTO>() {}, map);
    }

    public PhysicalCardActiveRespDTO applyPhysicalCardActive(PhysicalCardActiveReqDTO physicalCardActiveReqDTO){
        String requestData = JSON.toJSONString(physicalCardActiveReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",physicalCardActiveReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.PHYSICAL_CARD_ACTIVE.getUrl(),requestData, new TypeReference<PhysicalCardActiveRespDTO>() {}, map);
    }

    public PhysicalCardOrderDetailRespDTO physicalCardOrderDetail(PhysicalCardOrderDetailReqDTO physicalCardActiveReqDTO){
        String requestData = JSON.toJSONString(physicalCardActiveReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",physicalCardActiveReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.PHYSICAL_CARD_ORDER_DETAIL.getUrl(),requestData, new TypeReference<PhysicalCardOrderDetailRespDTO>() {}, map);
    }

    public PhysicalCardResetPinRespDTO applyPhysicalCardResetPin(PhysicalCardResetPinReqDTO physicalCardResetPinReqDTO){
        String requestData = JSON.toJSONString(physicalCardResetPinReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",physicalCardResetPinReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.PHYSICAL_CARD_RESET_PING.getUrl(),requestData, new TypeReference<PhysicalCardResetPinRespDTO>() {}, map);
    }

    public CaptchaApplyRespDTO captchaApply(CaptchaApplyReqDTO captchaApplyReqDTO){
        String requestData = JSON.toJSONString(captchaApplyReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",captchaApplyReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.COMMON_CAPTCHA_APPLY.getUrl(),requestData, new TypeReference<CaptchaApplyRespDTO>() {}, map);
    }


    public CardModifyRespDTO modify(CardModifyReqDTO cardModifyReqDTO){
        String requestData = JSON.toJSONString(cardModifyReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",cardModifyReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.CARD_MODIFY.getUrl(),requestData, new TypeReference<CardModifyRespDTO>() {}, map);
    }

    /**
     * 调整商务卡余额，余额不可调整为负数
     */
    public CardBalanceAdjustRespDTO balanceAdjust(CardBalanceAdjustReqDTO cardBalanceAdjustReqDTO){
        String requestData = JSON.toJSONString(cardBalanceAdjustReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",cardBalanceAdjustReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.CARD_BALANCE_ADJUST.getUrl(),requestData, new TypeReference<CardBalanceAdjustRespDTO>() {}, map);
    }

    public CardDetailRespDTO detail(CardDetailReqDTO cardDetailReqDTO){
        String requestData = JSON.toJSONString(cardDetailReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",cardDetailReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.CARD_DETAIL.getUrl(),requestData, new TypeReference<CardDetailRespDTO>() {}, map);
    }

    public CardModifyLimitRespDTO modifyLimit(CardModifyLimitReqDTO cardModifyLimitReqDTO){
        String requestData = JSON.toJSONString(cardModifyLimitReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",cardModifyLimitReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.CARD_LIMIT_MODIFY.getUrl(),requestData, new TypeReference<CardModifyLimitRespDTO>() {},map);
    }


    public TemplateRespDTO template(TemplateReqDTO templateReqDTO){
        String requestData = JSON.toJSONString(templateReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",templateReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.CARD_TEMPLATE.getUrl(),requestData, new TypeReference<TemplateRespDTO>() {}, map);
    }

    public CardVccQueryRespDTO queryVccInfo(CardVccQueryReqDTO cardVccQueryReqDTO){
        String requestData = JSON.toJSONString(cardVccQueryReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",cardVccQueryReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.VCC2.getUrl(),requestData, new TypeReference<CardVccQueryRespDTO>() {}, map);
    }


    /**
     * /card/v1/account/apply
     * 根据企业站创建的卡模板来申请的商务卡，卡申请通过后，会通过“卡状态变更通知”接口返回商务卡信息。
     */
    public CardApplyRespDTO apply(CardApplyReqDTO cardApplyReqDTO){
        String requestData = JSON.toJSONString(cardApplyReqDTO);
        Map<String,String> map = new HashMap<>();
        map.put("mch_id",cardApplyReqDTO.getMchId());
        return lianlianPayClient.post(LianLianCardUrlEnum.CARD_APPLY.getUrl(),requestData, new TypeReference<CardApplyRespDTO>() {}, map);
    }

}
