package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SalesInfos implements Serializable {
    @JSONField(name = "offline_info")
    private List<OfflineInfo> offlineInfo;

    @JSONField(name = "web_info")
    private List<WebInfo> webInfo;

    @JSONField(name = "app_info")
    private List<AppInfo> appInfo;
}
