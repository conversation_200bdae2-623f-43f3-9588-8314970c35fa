package com.fenbeitong.dech.lianlian.dto.template;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class TemplateInfo implements Serializable {

    @J<PERSON>NField(name = "template_id")
    private String templateId;

    @JSONField(name = "template_name")
    private String templateName;

    @JSONField(name = "template_quota")
    private String templateQuota;

    @JSONField(name = "enterprise_id")
    private String enterpriseId;

    @JSONField(name = "enterprise_name")
    private String enterpriseName;

    @J<PERSON>NField(name = "accept_scope")
    private String acceptScope;

    @JSONField(name = "organizational_scope")
    private String organizationalScope;

    @JSONField(name = "consume_rule")
    private ConsumeRule consumeRule;

    @JSONField(name = "provide_rule")
    private ProvideRule provideRule;

    @JSONField(name = "quota_recovery")
    private String quotaRecovery;

    @JSONField(name = "template_type")
    private String templateType;

    @JSONField(name = "currency")
    private String currency;

}
