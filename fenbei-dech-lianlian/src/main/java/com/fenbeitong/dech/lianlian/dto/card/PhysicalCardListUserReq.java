package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class PhysicalCardListUserReq implements Serializable {

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "phone")
    private String phone;

    /**
     * fail_list
     */
    @JSONField(name = "email")
    private String email;
}
