package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 产品信息变更
 */
@Data
public class AccountModifyProductInfoReqDTO implements Serializable {
    /**
     * 请求单号
     */
    @JSONField(name = "txn_seqno")
    private String txnSeqno;
    /**
     * 结果通知地址
     */
    @JSONField(name = "notify_url")
    private String notifyUrl;
    /**
     * 商户号
     */
    @JSONField(name = "mch_id")
    private String mchid;
    /**
     * 产品信息，产品信息
     */
    @JSONField(name = "product_infos")
    private List<ProductInfo> productInfos;
    /**
     * 协议信息，协议信息
     */
    @JSONField(name = "protocol_info")
    private ProtocolInfo protocolInfo;
    /**
     * 经营场景信息，目前只支持全量修改（全量修改包括新增或编辑经营场景）
     */
    @JSONField(name = "sales_infos")
    private SalesInfos salesInfos;
    /**
     * 结算信息，结算信息 只支持修改不支持新增多个结算卡
     */
    @JSONField(name = "settle_info")
    private SettleInfo settleInfo;

}
