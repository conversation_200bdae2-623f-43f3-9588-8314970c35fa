package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SettleMaterials implements Serializable {
    @J<PERSON><PERSON>ield(name = "open_permit")
    private String openPermit;

    @JSONField(name = "settle_id_card")
    private String settleIdCard;

    @JSONField(name = "bank_card_front")
    private String bankCardFront;

    @JSONField(name = "bank_card_back")
    private String bankCardBack;

    @JSONField(name = "other_file")
    private List<String> otherFile;
}
