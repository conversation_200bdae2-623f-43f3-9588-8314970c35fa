package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class AccountActiveApplyReqDTO implements Serializable {
    /**
     * 商户号
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     * 银行账户号 加款充值的银行账户号
     */
    @JSONField(name = "bank_account_no")
    private String bankAccountNo;
    /**
     out_order_no
     string
     商户请求唯一编号
     必需
     >= 1 字符
     <= 32 字符
     */
    @JSONField(name = "out_order_no")
    private String outOrderNo;
}
