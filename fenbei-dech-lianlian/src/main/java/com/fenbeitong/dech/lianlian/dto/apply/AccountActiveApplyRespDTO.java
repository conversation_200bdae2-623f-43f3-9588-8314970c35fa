package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.card.LianlianCardBaseRespDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class AccountActiveApplyRespDTO extends LianlianCardBaseRespDTO {
    /**
     * 授权令牌。有效期10分钟。需要在账户激活验证接口上送改字段
     */
    @JSONField(name = "token")
    private String token;
}
