package com.fenbeitong.dech.lianlian.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fenbeitong.dech.lianlian.LianlianPayClient;
import com.fenbeitong.dech.lianlian.constant.LianlianContants;
import com.fenbeitong.dech.lianlian.dto.withdrawal.ApplyReceiptReqDTO;
import com.fenbeitong.dech.lianlian.dto.withdrawal.ApplyReceiptRespDTO;
import com.fenbeitong.dech.lianlian.dto.withdrawal.SmsVerifyRequestDTO;
import com.fenbeitong.dech.lianlian.dto.withdrawal.SmsVerifyResponseDTO;
import com.fenbeitong.dech.lianlian.dto.withdrawal.TradeDetailReqDTO;
import com.fenbeitong.dech.lianlian.dto.withdrawal.TradeDetailRespDTO;
import com.fenbeitong.dech.lianlian.dto.withdrawal.WithdrawalRequestDTO;
import com.fenbeitong.dech.lianlian.dto.withdrawal.WithdrawalResponseDTO;

/**
 * 提现
 * <AUTHOR>
 * @date 2024/08/09
 */
@Component
public class LianlianClientTradeService {

	@Autowired
    private LianlianPayClient lianlianPayClient;
	
	/**
	 * 提现申请
	 * @param request
	 * @return
	 */
	public WithdrawalResponseDTO withdrawal(WithdrawalRequestDTO request) {
		Map<String,String> header = new HashMap<>();
        return lianlianPayClient.post(LianlianContants.WITHDRAWAL, JSON.toJSONString(request), new TypeReference<WithdrawalResponseDTO>() {}, header);
	}
	
	/**
	 * 验证短信验证码
	 * @param request
	 * @return
	 */
	public SmsVerifyResponseDTO verifySmsCode(SmsVerifyRequestDTO request) {
		Map<String,String> header = new HashMap<>();
        return lianlianPayClient.post(LianlianContants.SMS_VERIFY, JSON.toJSONString(request), new TypeReference<SmsVerifyResponseDTO>() {}, header);
	}
	
	/**
	 * 查询资金明细
	 * @param request
	 * @return
	 */
	public TradeDetailRespDTO queryTradeDetails(TradeDetailReqDTO request) {
		Map<String,String> header = new HashMap<>();
        return lianlianPayClient.post(LianlianContants.TRADE_DETAIL, JSON.toJSONString(request), new TypeReference<TradeDetailRespDTO>() {}, header);
	}
	
	
//	@PostConstruct
//	private void init() {
//		String seq = ObjectId.get().toString();
//		applyReceipt(ApplyReceiptReqDTO.builder().mchId("402409040000095540").orderNo("202409050000527175").receiptSeqno(seq).build());
//		queryReceipt(ApplyReceiptReqDTO.builder().mchId("402409040000095540").receiptSeqno("66d9756fc34ce9262fa80daa").build());
//	}
	
	/**
	 * 申请下载电子回单
	 * @param request
	 * @return
	 */
	public ApplyReceiptRespDTO applyReceipt(ApplyReceiptReqDTO request) {
		Map<String,String> header = new HashMap<>();
        return lianlianPayClient.post(LianlianContants.DOWNLOAD_RECEIPT, JSON.toJSONString(request), new TypeReference<ApplyReceiptRespDTO>() {}, header);
	}
	
	/**
	 * 查询电子回单
	 * @param request
	 * @return
	 */
	public ApplyReceiptRespDTO queryReceipt(ApplyReceiptReqDTO request) {
		Map<String,String> header = new HashMap<>();
        return lianlianPayClient.post(LianlianContants.QUERY_RECEIPT_PROGRESS, JSON.toJSONString(request), new TypeReference<ApplyReceiptRespDTO>() {}, header);
	}
}
