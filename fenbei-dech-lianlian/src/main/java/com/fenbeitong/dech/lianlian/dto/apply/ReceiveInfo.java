package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class ReceiveInfo implements Serializable {
    @JSONField(name = "recipients")
    private String recipients;

    @JSONField(name = "recipients_phone")
    private String recipientsPhone;

    @JSONField(name = "recipients_email")
    private String recipientsEmail;

    @JSONField(name = "address_info")
    private AddressInfo addressInfo;
}
