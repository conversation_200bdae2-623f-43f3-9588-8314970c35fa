package com.fenbeitong.dech.lianlian.dto.withdrawal;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/16
 */
@Data
public class TradeDetailItemDTO {

	/**
	 * 账户号
	 */
	@JSONField(name = "account_no")
	private String accountNo;
	
	/**
	 * 出入账标识 DEBIT 出账、CREDIT 入账
	 */
	@JSONField(name = "flag_dc")
	private String flagDc;
	
	/**
	 * 业务类型 CONSUME 消费、 REFUND 退款、 REVERSE 撤销、 ADJUST 调账、 CHARGE 充值、 WITHDRAWAL提现
	 */
	@JSONField(name = "biz_type")
	private String bizType;
	
	/**
	 * 资金流水号
	 */
	@JSONField(name = "serial_id")
	private String serialId;
	
	/**
	 * 交易时间 格式：yyyy-MM-dd HH:mm:ss
	 */
	@JSONField(name = "txn_time")
	private String txnTime;
	
	/**
	 * 交易发生额 单位：元，保留2位小数
	 */
	private String amt;
	
	/**
	 * 交易后余额 单位：元，保留2位小数
	 */
	@JSONField(name = "amt_bal")
	private String amtBal;
	
	/**
	 * 资金流水备注
	 */
	private String memo;
	
	
}
