package com.fenbeitong.dech.lianlian.dto.template;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProvideRule implements Serializable {
    @JSONField(name = "provide_method")
    private String provideMethod;

    @JSONField(name = "provide_owner")
    private String provideOwner;

    @JSONField(name = "card_bin")
    private List<String> cardBin;

}
