package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class PhysicalCardDeliveryLogisticsAddress implements Serializable {

    @JSONField(name = "logistics_company")
    private String logisticsCompany;

    @JSONField(name = "logistics_no")
    private String logisticsNo;

    @JSONField(name = "logistics_deliver_time")
    private String logisticsDeliverTime;
}
