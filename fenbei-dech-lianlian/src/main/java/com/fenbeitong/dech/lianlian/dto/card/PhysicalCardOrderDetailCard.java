package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class PhysicalCardOrderDetailCard implements Serializable {

    @JSONField(name = "account_no")
    private String accountNo;

    @JSONField(name = "account_name")
    private String accountName;

    @JSONField(name = "out_order_no")
    private String outOrderNo;

    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "card_holder")
    private String cardHolder;

    @JSONField(name = "vcc_biz_scene")
    private String vccBizScene;

    @JSONField(name = "currency")
    private String currency;


    @JSONField(name = "apply_status")
    private String applyStatus;

    /**
     single_limit
     string
     单笔限额
     可选
     单位：元，不设置默认取模版配置 ，最大300000
     */
    @JSONField(name = "single_limit")
    private String singleLimit;
    /**
     single_day_limit
     string
     单日交易限额
     可选
     单位：元，不设置默认取模版配置 ，最大********
     */
    @JSONField(name = "single_day_limit")
    private String singleDayLimit;
    /**
     single_month_limit
     string
     单月交易限额
     可选
     单位：元，不设置默认取模版配置 ，最大********
     */
    @JSONField(name = "single_month_limit")
    private String singleMonthLimit;
    /**
     remark
     string
     备注
     可选
     <= 256 字符
     */
    private String remark;
}
