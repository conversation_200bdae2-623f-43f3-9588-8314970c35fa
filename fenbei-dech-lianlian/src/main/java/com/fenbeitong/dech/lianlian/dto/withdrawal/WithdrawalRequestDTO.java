package com.fenbeitong.dech.lianlian.dto.withdrawal;

import java.math.BigDecimal;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WithdrawalRequestDTO {

	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 商户提现单号
	 */
	@JSONField(name = "out_order_no")
	private String outOrderNo;
	
	/**
	 * 该笔订单的资金总额，单位为RMB-元。 大于0的数字，精确到小数点后两位
	 */
	@JSONField(name = "order_amount")
	private BigDecimal orderAmount;
		
	/**
	 * 付款方信息
	 */
//	@JSONField(name = "payer_info")
//	private PayerInfo payerInfo;
	
	/**
	 * 账户号
	 */
	@JSONField(name = "account_no")
	private String accountNo;
	
	/**
	 * 收款方信息
	 */
	@JSONField(name = "payee_info")
	private PayeeInfo payeeInfo;
	
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class PayerInfo {
		/**
		 * 账户号
		 */
		@JSONField(name = "account_no")
		private String accountNo;
	}
	
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class PayeeInfo {
		/**
		 * 绑定银行账号 银行卡号必须在系统侧绑定过
		 */
		@JSONField(name = "linked_acctno")
		private String linkedAcctno;
		
		/**
		 * 交易附言 提现交易附言，单笔金额大于等于5w必须提供 <= 16 字符
		 */
		private String postscript;
	}
}
