package com.fenbeitong.dech.lianlian.dto;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/08/15
 */
public abstract class BaseRespDTO {

	public abstract String getRetCode();
	
	protected static final String SUCCESS_CODE = "0000";
	
	public boolean isSuccess() {
		return Optional.ofNullable(getRetCode()).filter(code -> StringUtils.equals(SUCCESS_CODE, code)).isPresent();
	}
}
