package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class ShareholderInfo implements Serializable {
    @J<PERSON><PERSON>ield(name = "id_type")
    private String idType;

    @JSONField(name = "id_name")
    private String idName;

    @JSONField(name = "id_no")
    private String idNo;

    @JSONField(name = "id_front")
    private String idFront;

    @JSONField(name = "id_back")
    private String idBack;

    @J<PERSON>NField(name = "validity_start_time")
    private String validityStartTime;

    @JSONField(name = "validity_end_time")
    private String validityEndTime;
}
