package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppInfo implements Serializable {
    @J<PERSON><PERSON>ield(name = "scene_type")
    private String sceneType;

    @JSONField(name = "application_name")
    private String applicationName;

    @JSONField(name = "application_identification")
    private String applicationIdentification;

    @JSONField(name = "application_desc")
    private String applicationDesc;

    @JSONField(name = "other_files")
    private List<String> otherFiles;
}
