package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserList implements Serializable {

    /**
     * fail_list
     */
    @JSONField(name = "user_id")
    private String userId;
    /**
     * fail_list
     */
    @JSONField(name = "name")
    private String name;
    /**
     * fail_list
     */
    @JSONField(name = "phone")
    private String phone;
    /**
     * fail_list
     */
    @JSONField(name = "email")
    private String email;
    /**
     * fail_list
     */
    @JSONField(name = "enable")
    private String enable;
    /**
     * fail_list
     */
    @JSONField(name = "cert_type")
    private String certType;
    /**
     * fail_list
     */
    @JSONField(name = "cert_no")
    private String certNo;
    /**
     * fail_list
     */
    @JSONField(name = "sex")
    private String sex;
    /**
     * fail_list
     */
    @JSONField(name = "rank")
    private String rank;
    /**
     * fail_list
     */
    @JSONField(name = "work_code")
    private String workCode;
    /**
     * fail_list
     */
    @JSONField(name = "hiring_date")
    private String hiringDate;
    /**
     * fail_list
     */
    @JSONField(name = "department_ids")
    private List<String> departmentIds;
    /**
     * fail_list
     */
    @JSONField(name = "leader_user_id")
    private String leaderUserId;
    /**
     * fail_list
     */
    @JSONField(name = "enterprise_id")
    private String enterpriseId;

}
