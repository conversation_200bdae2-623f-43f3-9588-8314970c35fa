package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CardBalanceAdjustReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     string
     商务卡唯一编号
     <= 32 字符
     */
    @JSONField(name = "account_no")
    private String accountNo;
    /**
        out_order_no
    string
        商户请求唯一编号
    必需
>= 1 字符
<= 32 字符
     */
    @JSONField(name = "out_order_no")
    private String outOrderNo;
    /**
     变更类型
     必需
     <= 16 字符
     枚举值:
         INCREASE 增加额度
         REDUCE 减少额度
     */
    @JSONField(name = "adjust_type")
    private String adjustType;
    /**
     变更金额
     必需
     变更金额，单位为RMB-元。
     大于0的数字，精确到小数点后两位。
     如：49.65
     <= 20 字符
     */
    @JSONField(name = "adjust_amount")
    private String adjustAmount;
    /**
     变更说明
     */
    @JSONField(name = "adjust_memo")
    private String adjustMemo;
}
