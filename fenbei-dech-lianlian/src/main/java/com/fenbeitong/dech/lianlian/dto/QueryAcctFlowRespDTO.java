package com.fenbeitong.dech.lianlian.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2024/06/28
 */

@Data
@SuppressWarnings("serial")
public class QueryAcctFlowRespDTO implements Serializable {

	/**
	 * 平台三方用户号
	 */
	private String thirdUserId;
	
	/**
	 * 连连内部账⼾ID
	 */
	private String accountNo;
	
	/**
	 * 交易时间，格式⽰例：2023-04-10 11:14
	 */
	private String applicationTime;
	
	/**
	 * 交易类型:充值、提现、收款、付款 RECHARGE, WITHDRAW, COLLECTION, PAYMENT
	 */
	private String transType;
	
	/**
	 * 交易对⼿名称 实际打款⼈名称
	 */
	private String counterpartyName;
	
	/**
	 * 交易对⼿账号 境外客⼾汇款可能会存在没有账号的情况 连内部客⼾转账，交易对⼿账号为连连内⼾
	 */
	private String counterpartyNo;
	
	/**
	 * 交易⾦额
	 */
	private String amount;
	
	private String currency;
	
	/**
	 * 交易后账⼾余额
	 */
	private String balance;
	
	/**
	 * 交易摘要、备注
	 */
	private String memo;
	
	/**
	 * 记录总条数
	 */
	private int count;
	
	/**
	 * 分⻚查询的最新记录位置，需要在下次查 设置到cursor⼊参中
	 */
	private String endCursor;
}
