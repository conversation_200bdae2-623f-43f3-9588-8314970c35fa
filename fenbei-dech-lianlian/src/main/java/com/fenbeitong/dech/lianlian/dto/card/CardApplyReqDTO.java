package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonProperty;

import java.io.Serializable;

@Data
public class CardApplyReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
        out_order_no
    string
        商户请求唯一编号
    必需
>= 1 字符
<= 32 字符
     */
    @JSONField(name = "out_order_no")
    private String outOrderNo;
    /**
        user_id
    string
        商户侧用户唯一编号
    必需
<= 64 字符
     */
    @JSONField(name = "user_id")
    private String userId;
    /**
        template_id
    string
        商务卡模版id
    必需
<= 19 字符
     */
    @JSONField(name = "template_id")
    private String templateId;
    /**
        currency
    string
        卡币种
    必需
    枚举值:
    CNY
     */
    private String currency;
    /**
        expire_time
    string
        卡有效结束时间
    必需
    正则匹配:
    yyyy-MM-dd
     */
    @JSONField(name = "expire_time")
    private String expireTime;
    /**
        apply_amount
    string
        申请金额
    可选
    申请金额，单位为RMB-元。大于0的数字，精确到小数点后两位。如：49.65如果不传，默认选择模版配置的金额
        最大金额99999999
    */
    @JSONField(name = "apply_amount")
    private String applyAmount;
    /**
    single_limit
        string
    单笔限额
        可选
    单位：元，不设置默认取模版配置 ，最大300000
    */
    @JSONField(name = "single_limit")
    private String singleLimit;
    /**
        single_day_limit
    string
        单日交易限额
    可选
    单位：元，不设置默认取模版配置 ，最大99999999
    */
    @JSONField(name = "single_day_limit")
    private String singleDayLimit;
    /**
        single_month_limit
    string
        单月交易限额
    可选
    单位：元，不设置默认取模版配置 ，最大99999999
    */
    @JSONField(name = "single_month_limit")
    private String singleMonthLimit;
    /**
        enterprise_id
    string
        公司主体id
    可选
        费用承担主体,当使用连连费控系统时需要

<= 64 字符
     */
    @JSONField(name = "enterprise_id")
    private String enterpriseId;
    /**
        remark
    string
        备注
    可选
<= 256 字符
     */
    private String remark;
}
