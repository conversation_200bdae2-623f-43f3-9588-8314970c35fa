package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.card.LianlianCardBaseRespDTO;
import lombok.Data;

@Data
public class ProtocolDownloadRespDTO extends LianlianCardBaseRespDTO {
    /**
     * 文件名称
     */
    @JSONField(name = "file_name")
    private String fileName;
    /**
     * 密码
     */
    @JSONField(name = "body")
    private String body;
}
