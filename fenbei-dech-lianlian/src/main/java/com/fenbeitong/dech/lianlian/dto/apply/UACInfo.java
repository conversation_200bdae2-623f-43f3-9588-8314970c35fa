package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class UACInfo implements Serializable {
    @JSONField(name = "is_identity")
    private String isIdentity;

    @JSONField(name = "id_info")
    private IdInfo idInfo;

    @JSONField(name = "address_info")
    private AddressInfo addressInfo;

}
