package com.fenbeitong.dech.lianlian.dto.file;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.apply.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FileUploadReqDTO implements Serializable {

    @JSONField(name = "txn_time")

    private String txnTime;
    @JSONField(name = "txn_seqno")
    private String txnSeqno;

    @JSONField(name = "user_no")
    private String userNo;

    @JSONField(name = "file_type")
    private String fileType;

    @JSONField(name = "file_context")
    private String fileContext;

    @JSONField(name = "business_info")
    private String businessInfo;

    @JSONField(name = "timestamp")
    private String timestamp;
    /**
     * 内容类型,
     * UBO_IMAGE 受益所有人文件;
     * USER_IMAGE 用户相关影印文件;
     * JIEHUIBAO_MATERIALS 结汇宝证明材料;
     * SUPPLEMENT_CSV 补单对账CSV文件 ;
     * TRADE_MATERIAL 贸易材料
     */
    @JSONField(name = "context_type")
    private String contextType;
}
