package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class PhysicalCardListResp implements Serializable {
    /**
     * fail_list
     */
    @J<PERSON>NField(name = "account_no")
    private String accountNo;
    /**
     * fail_list
     */
    @JSONField(name = "account_name")
    private String accountName;

    @JSONField(name = "out_order_no")
    private String outOrderNo;

    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "expire_time")
    private String expireTime;

    @JSONField(name = "currency")
    private String currency;

    @JSONField(name = "vcc_biz_scene")
    private String vccBizScene;
}
