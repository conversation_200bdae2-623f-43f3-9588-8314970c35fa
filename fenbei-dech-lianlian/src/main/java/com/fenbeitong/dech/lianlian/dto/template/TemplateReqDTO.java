package com.fenbeitong.dech.lianlian.dto.template;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class TemplateReqDTO implements Serializable {
    @JSONField(name = "mch_id")
    private String mchId;

    @JSONField(name = "template_type")
    private String templateType;

    @JSONField(name = "template_id")
    private String templateId;

    @JSONField(name = "accept_scope")
    private String acceptScope;

    @JSONField(name = "quota_recovery")
    private String quotaRecovery;

}
