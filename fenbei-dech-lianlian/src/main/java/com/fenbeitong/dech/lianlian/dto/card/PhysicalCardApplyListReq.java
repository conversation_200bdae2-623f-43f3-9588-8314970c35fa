package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PhysicalCardApplyListReq implements Serializable {

    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "user_info")
    private PhysicalCardListUserReq userInfo;

    @JSONField(name = "out_order_no")
    private String outOrderNo;

    @JSONField(name = "card_holder")
    private String cardHolder;

    @JSONField(name = "expire_time")
    private String expireTime;

    @JSONField(name = "vcc_biz_scene")
    private String vccBizScene;

    @JSONField(name = "account_name")
    private String accountName;

    /**
     apply_amount
     string
     申请金额
     可选
     申请金额，单位为RMB-元。大于0的数字，精确到小数点后两位。如：49.65如果不传，默认选择模版配置的金额
     最大金额********
     */
    @JSONField(name = "apply_amount")
    private String applyAmount;

    /**
     single_limit
     string
     单笔限额
     可选
     单位：元，不设置默认取模版配置 ，最大300000
     */
    @JSONField(name = "single_limit")
    private String singleLimit;
    /**
     single_day_limit
     string
     单日交易限额
     可选
     单位：元，不设置默认取模版配置 ，最大********
     */
    @JSONField(name = "single_day_limit")
    private String singleDayLimit;
    /**
     single_month_limit
     string
     单月交易限额
     可选
     单位：元，不设置默认取模版配置 ，最大********
     */
    @JSONField(name = "single_month_limit")
    private String singleMonthLimit;
    /**
     remark
     string
     备注
     可选
     <= 256 字符
     */
    private String remark;
}
