package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class Ubo implements Serializable {
    @JSONField(name = "special_approve")
    private String specialApprove;

    @JSONField(name = "id_info")
    private IdInfo idInfo;

    @JSONField(name = "address_info")
    private AddressInfo addressInfo;
}
