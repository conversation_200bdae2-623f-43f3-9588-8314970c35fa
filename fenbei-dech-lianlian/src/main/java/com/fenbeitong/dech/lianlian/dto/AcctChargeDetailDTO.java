 package com.fenbeitong.dech.lianlian.dto;

import java.io.Serializable;

import lombok.Data;

@Data
@SuppressWarnings("serial")
public class AcctChargeDetailDTO implements Serializable {

	/**
	 * 加款唯一单号
	 */
	private String id;
	
	/**
	 * 平台三方用户号
	 */
	private String thirdUserId;
	
	/**
	 * 连连中台用户号
	 */
	private String userId;
	
	private String accountNo;
	
	/**
	 * 交易时间，格式示例：2023-04-10 11:14:53
	 */
	private String applicationTime;
	
	/**
	 * RECHARGE("RECHARGE", "充值"), COLLECTION("COLLECTION", "收款"); 打款人名称和kyc名称一样是充值，打款人名称和kyc名称不一样是收款
	 */
	private String transType;
	
	/**
	 * 交易对⼿名称 实际打款⼈名称
	 */
	private String counterpartyName;
	
	/**
	 * 交易对⼿账号 境外客⼾汇款可能会存在没有账号的情况 连内部客⼾转账，交易对⼿账号为连连内⼾
	 */
	private String counterpartyNo;
	
	/**
	 * 交易对手银行名称
	 */
	private String counterpartyBankName;
	
	/**
	 * 交易⾦额
	 */
	private String amount;
	
	private String currency;
		
	/**
	 * 交易摘要、备注
	 */
	private String memo;
}
