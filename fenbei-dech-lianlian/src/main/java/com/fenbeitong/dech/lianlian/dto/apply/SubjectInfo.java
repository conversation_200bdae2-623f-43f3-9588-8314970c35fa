package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class SubjectInfo implements Serializable {
    @JSONField(name = "subject_type")
    private String subjectType;

    @JSONField(name = "business_license_info")
    private BusinessLicenseInfo businessLicenseInfo;

    @JSONField(name = "identity_info")
    private IdentityInfo identityInfo;
}
