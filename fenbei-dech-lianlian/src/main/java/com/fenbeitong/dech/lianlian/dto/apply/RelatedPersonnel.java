package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RelatedPersonnel implements Serializable {
    @JSONField(name = "uac_info")
    private UACInfo uacInfo;

    @JSONField(name = "admin_info")
    private AdminInfo adminInfo;

    @JSONField(name = "ubo_infos")
    private UBOInfos uboInfos;

    @JSONField(name = "shareholder_infos")
    private List<ShareholderInfo> shareholderInfos;

    @JSONField(name = "contact_infos")
    private List<ContactInfo> contactInfos;

    @JSONField(name = "receive_info")
    private ReceiveInfo receiveInfo;
}
