package com.fenbeitong.dech.lianlian.service;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fenbeitong.dech.lianlian.LianlianPayClient;
import com.fenbeitong.dech.lianlian.constant.LianlianContants;
import com.fenbeitong.dech.lianlian.dto.acct.AcctDetailRespDTO;
import com.fenbeitong.dech.lianlian.dto.acct.AcctListRespDTO;
import com.fenbeitong.dech.lianlian.dto.acct.QueryAcctReqDTO;

@Component
public class LianlianAccountService {

	@Autowired
    private LianlianPayClient lianlianPayClient;

	/**
	 * 查询账户详情
	 * @param request
	 * @return
	 */
	public AcctDetailRespDTO queryAcctDetail(QueryAcctReqDTO request) {
        return lianlianPayClient.post(LianlianContants.QUERY_ACCT_DETAIL, JSON.toJSONString(request), new TypeReference<AcctDetailRespDTO>() {}, null);
	}

	/**
	 * 根据商户号查询账户列表
	 * @param request
	 * @return
	 */
	public AcctListRespDTO queryAcctList(QueryAcctReqDTO request) {
        return lianlianPayClient.post(LianlianContants.QUERY_ACCT_LIST, JSON.toJSONString(request), new TypeReference<AcctListRespDTO>() {}, null);
	}
}
