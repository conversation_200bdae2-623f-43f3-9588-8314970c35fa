package com.fenbeitong.dech.lianlian.service;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fenbeitong.dech.lianlian.LianlianClient;
import com.fenbeitong.dech.lianlian.constant.LianlianContants;
import com.fenbeitong.dech.lianlian.dto.BalanceReqDTO;
import com.fenbeitong.dech.lianlian.dto.LianlianBaseRespDTO;
import com.fenbeitong.dech.lianlian.dto.vcc.VccAcctRespDTO;

@Component
public class LianlianVccAccountService {

	@Autowired
	private LianlianClient lianlianClient;
	
	public VccAcctRespDTO queryAccountBalance(BalanceReqDTO request) {
		
		LianlianBaseRespDTO<VccAcctRespDTO> resp = lianlianClient.post(LianlianContants.QUERY_VCC_ACCT_BALANCE, JSON.toJSONString(request), new TypeReference<LianlianBaseRespDTO<VccAcctRespDTO>>() {});
		
		if (Objects.isNull(resp) || Objects.isNull(resp.getData())) {
			return null;
		}
		
		return resp.getData();
	}
}
