package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BusinessInfo implements Serializable {
    @J<PERSON>NField(name = "merchant_name")
    private String merchantName;
    @JSONField(name = "merchant_short_name")
    private String merchantShortName;
    @JSONField(name = "service_phone")
    private String servicePhone;
    @JSONField(name = "mcc")
    private String mcc;
    @JSONField(name = "business_desc")
    private String businessDesc;
    @JSONField(name = "office_space")
    private String officeSpace;
    @J<PERSON>NField(name = "business_classify")
    private String businessClassify;
    @JSONField(name = "expect_trade_amount")
    private String expectTradeAmount;
    @JSONField(name = "foreign_business")
    private String foreignBusiness;
    @JSONField(name = "exclusive_cooperate")
    private String exclusiveCooperate;
    @JSONField(name = "business_scale")
    private String businessScale;
    @JSONField(name = "additional_file")
    private List<String> additionalFile;
    @JSONField(name = "address_info")
    private AddressInfo addressInfo;
}
