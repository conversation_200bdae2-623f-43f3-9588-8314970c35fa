package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.card.LianlianCardBaseRespDTO;
import lombok.Data;

import java.util.List;

@Data
public class AccountApplyResultRespDTO extends LianlianCardBaseRespDTO {
    /**
     * 请求流水号
     */
    @JSONField(name ="txnSeqno")
    private String txnSeqno;
    /**
     * 商户ID
     */
    @JSONField(name ="mch_id")
    private String mchId;
    /**
     * 进件状态
     * 必需
     * APPLY已受理； REJECTED 驳回；SUCCESS进件成功； REFUSED进件失败
     */
    @JSONField(name ="status")
    private String status;
    /**
     * 进件状态
     * 必需
     * APPLY已受理； REJECTED 驳回；SUCCESS进件成功； REFUSED进件失败
     */
    @JSONField(name ="fail_reason")
    private String failReason;
    /**
     * 微信子商户号
     * 可选
     * 多个子商户号以逗号分隔
     */
    @JSONField(name ="wx_sub_mchid")
    private String wxSubMchid;
    /**
     * 支付宝子商户号
     * 可选
     * 多个子商户号以逗号分隔
     */
    @JSONField(name ="ali_sub_mchid")
    private String aliSubMchid;
    /**
     * array[string]
     * 虚拟卡号
     * 可选
     * 虚拟卡号
     */
    @JSONField(name ="virtual_card")
    private List<String> virtualCard;
}
