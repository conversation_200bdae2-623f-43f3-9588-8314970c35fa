package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class BankCardInfo implements Serializable {
    @JSONField(name = "account_type")
    private String accountType;

    @JSONField(name = "account_name")
    private String accountName;

    @JSONField(name = "account_number")
    private String accountNumber;

    @JSONField(name = "bank_code")
    private String bankCode;

    @JSONField(name = "cnaps_code")
    private String cnapsCode;

    @JSONField(name = "cnaps_name")
    private String cnapsName;
}
