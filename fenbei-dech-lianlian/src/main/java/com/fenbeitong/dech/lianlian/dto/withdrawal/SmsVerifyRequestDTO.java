package com.fenbeitong.dech.lianlian.dto.withdrawal;

import java.math.BigDecimal;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsVerifyRequestDTO {

	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 商户提现单号
	 */
	@JSONField(name = "out_order_no")
	private String outOrderNo;
	
	/**
	 * 该笔订单的资金总额，单位为RMB-元。 大于0的数字，精确到小数点后两位
	 */
	@JSONField(name = "order_amount")
	private BigDecimal orderAmount;
	
	/**
	 * 验证码
	 */
	@JSONField(name = "verify_code")
	private String verifyCode;
	
	/**
	 * 签约授权令牌
	 */
//	private String token;
}
