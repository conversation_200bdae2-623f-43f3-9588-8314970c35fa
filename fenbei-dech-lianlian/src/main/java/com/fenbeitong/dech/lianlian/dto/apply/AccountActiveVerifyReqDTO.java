package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class AccountActiveVerifyReqDTO implements Serializable {
    /**
     * 商户号
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     * 银行账户号 加款充值的银行账户号
     */
    @JSONField(name = "bank_account_no")
    private String bankAccountNo;
    /**
     商户系统唯一交易流水号。与申请的交易流水号一致。
     */
    @JSONField(name = "out_order_no")
    private String outOrderNo;
    /**
     * 授权令牌。有效期10分钟。需要在账户激活验证接口上送改字段
     */
    @JSONField(name = "token")
    private String token;
    /**
     * 短信验证码
     */
    @JSONField(name = "verify_code")
    private String verifyCode;
}
