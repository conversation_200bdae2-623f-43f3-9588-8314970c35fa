package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class CardDetailRespDTO extends LianlianCardBaseRespDTO {
    /**
     * 商务卡唯一编号
     */
    @JSONField(name ="mch_id")
    private String mchId;
    /**
     * 商务卡唯一编号
     */
    @JSONField(name ="user_id")
    private String userId;
    /**
     * 商务卡唯一编号
     */
    @JSONField(name ="account_no")
    private String accountNo;
    /**
     * 商务卡唯一编号
     */
    @JSONField(name ="account_name")
    private String accountName;
    /**
     * 商务卡资金总余额
     * 必需
     * 单位为RMB-元。 大于0的数字，精确到小数点后两位。 如：49.65
     */
    @JSONField(name ="amt_balcur")
    private String amtBalcur;
    /**
     * 商务卡可用余额
     * 必需
     * 单位为RMB-元。 大于0的数字，精确到小数点后两位。 如：49.65
     */
    @JSONField(name ="amt_balaval")
    private String amtBalaval;
    /**
     * 商务卡冻结余额
     * 必需
     * 单位为RMB-元。 大于0的数字，精确到小数点后两位。 如：49.65
     */
    @JSONField(name ="amt_balfrz")
    private String amtBalfrz;
    /**
     * 卡过期时间
     * 必需
     * 正则匹配:yyyy-MM-dd
     */
    @JSONField(name ="expire_time")
    private String expireTime;
    /**
     * 商务卡配置模版id
     */
    @JSONField(name ="template_id")
    private String templateId;
    /**
     *
     * string
     * 银行虚拟卡号
     * 可选
     * 只有当商务卡为境外卡时返回,可用于绑定银行卡消费
     */
    @JSONField(name ="bank_card_no")
    private String bankCardNo;
    /**
     * string <MMYY>
     *  银行虚拟卡号有效期
     *  可选
     *  示例值:0726
     */
    @JSONField(name ="bank_card_expire_time")
    private String bankCardExpireTime;
    /**
     *
     * 枚举值
     * NORMAL-正常
     * CLOSED-已冻结
     * CANCEL-已注销
     * EXPIRED-已过期
     *
     */
    @JSONField(name ="account_status")
    private String accountStatus;
    /**
     * string
     * 商务卡创建时间
     * 必需
     * 正则匹配:yyyy-MM-dd HH:mm:ss
     */
    @JSONField(name ="create_time")
    private String createTime;
    /**
     * string
     * 商户侧公司主体id
     */
    @JSONField(name ="enterprise_id")
    private String enterpriseId;
}
