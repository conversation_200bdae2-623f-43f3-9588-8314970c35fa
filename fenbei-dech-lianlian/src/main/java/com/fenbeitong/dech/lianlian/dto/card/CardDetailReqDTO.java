package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CardDetailReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
        string
        商务卡唯一编号
        <= 32 字符
     */
    @JSONField(name = "account_no")
    private String accountNo;
}
