package com.fenbeitong.dech.lianlian;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.SignatureException;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.fenbeitong.dech.lianlian.constant.LianlianContants;
import com.fenbeitong.dech.lianlian.util.CipherException;
import com.fenbeitong.dech.lianlian.util.RSA;
import com.fenbeitong.dech.lianlian.util.ResponseHttpCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;

import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**  
 * @Description: 
 * <AUTHOR>
 * @date 2024年6月21日 
*/
@Component
public class LianlianClient implements InitializingBean {
    
    @NacosValue("${lianlian.max.idle:5}")
    private int maxIdleConns;
    
    @NacosValue("${lianlian.keep.alive:5}")
    private int keepAliveDuration;
    
    @NacosValue("${lianlian.timeout.read:10}")
    private int readTimeout;
    
    @NacosValue("${lianlian.timeout.write:10}")
    private int writeTimeout;
    
    @NacosValue("${lianlian.timeout.conn:10}")
    private int connectTimeout;
    
    @NacosValue("${lianlian.key.private}")
    private String privateKey;
    
    @NacosValue("${lianlian.key.public}")
    private String publicKey;
    
    @NacosValue("${lianlian.host}")
    private String lianlianHost;
    
    private OkHttpClient client;
    
    MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
    
    private static final long REQUEST_TIME_THRESHOLD = TimeUnit.MINUTES.toSeconds(10);

	@Override
	public void afterPropertiesSet() throws Exception {
		ConnectionPool pool = new ConnectionPool(maxIdleConns, keepAliveDuration, TimeUnit.MINUTES);
		client = new OkHttpClient().newBuilder()
				.readTimeout(readTimeout, TimeUnit.SECONDS)
				.writeTimeout(writeTimeout, TimeUnit.SECONDS)
				.connectTimeout(connectTimeout, TimeUnit.SECONDS)
				.connectionPool(pool)
				.build();
		
	}
	
	/**
	 * 
	 * @param <T>
	 * @param requestBody
	 * @return
	 */
	public <T> T post(String uri, String requestBody, TypeReference<T> type) {
        try {
        	FinhubLogger.info("【对接连连】uri->{},参数：{}", uri, requestBody);
			Request request = buildRequest(LianlianContants.POST, uri, requestBody, null);
			return responseHandler(request, uri, type);
		} catch (Exception e) {
			FinhubLogger.error("【对接连连】请求：{}时异常，参数：{}", requestBody, uri, e);
			throw new FinhubException(); // TODO
		}
	}
	
	private Request buildRequest(String method, String uri, String requestBody, String queryString) throws UnsupportedEncodingException, CipherException {
		//拼装签名格式内容：{method}&{URI}&{epoch}&{requestBody}[&{queryString}]
		StringBuilder signBuilder = buildSignBuilder(method, uri, requestBody, queryString);
		FinhubLogger.info("Request     >> " + signBuilder);
		
		String signHeader = buildSignHeader(signBuilder);
		
		String token = Base64.getEncoder().encodeToString((String.format("%s:%s", LianlianContants.DEVELOP_ID, LianlianContants.TOKEN)).getBytes());

		String url = lianlianHost + uri;
		if (StringUtils.isNotBlank(queryString)) {
			url += ("?" + queryString);
		}
		
		Request.Builder builder = new Request.Builder()
		        .url(url)
		        .addHeader(LianlianContants.AUTHORIZATION, LianlianContants.BASIC_TOKEN + token)
		        .addHeader(LianlianContants.SIGNATURE_HEADER, signHeader);
		if (StringUtils.equalsIgnoreCase(LianlianContants.POST, method)) {
			RequestBody body = RequestBody.create(mediaType, requestBody);
			builder.post(body);
		} else if (StringUtils.equalsIgnoreCase(LianlianContants.GET, method)) {
			builder.get();
		}
		
		return builder.build();
	}
	
	private StringBuilder buildSignBuilder(String method, String uri, String requestBody, String queryString) throws UnsupportedEncodingException {
		StringBuilder signBuilder = new StringBuilder();
        signBuilder.append(method)
                .append(LianlianContants.CONCAT_STR).append(uri)
                .append(LianlianContants.CONCAT_STR).append(LianlianContants.epoch)
                .append(LianlianContants.CONCAT_STR).append(requestBody);
        if (StringUtils.isNotBlank(queryString)) {
        	signBuilder.append(LianlianContants.CONCAT_STR).append(URLEncoder.encode(queryString, LianlianContants.ENCODE));
		}
        return signBuilder;
	}
	
	private String buildSignHeader(StringBuilder signBuilder) throws CipherException {
		String sign = RSA.sign(RSA.Mode.SHA256withRSA, signBuilder.toString(), privateKey);
		FinhubLogger.info("SIGN        >> " + sign);

		String signHeader = String.format("t=%s,v=%s", LianlianContants.epoch, sign);
		FinhubLogger.info("Sign Header >> " + signHeader);
		return signHeader;
	}
	
	public <T> T get(String uri, String queryString, TypeReference<T> type) {
        try {
        	FinhubLogger.info("【对接连连】uri->{},参数：{}", uri, queryString);
			Request request = buildRequest(LianlianContants.GET, uri, "", queryString);
			return responseHandler(request, uri, type);
		} catch (Exception e) {
			FinhubLogger.error("【对接连连】请求：{}时异常，参数：{}", queryString, uri, e);
			throw new FinhubException(); // TODO
		}
	}
	
	/**
     * 处理响应结果 Processing response results.
     *
     * @param builder
     * @param uri
	 * @throws Exception 
     */
    private  <T> T responseHandler(Request request, String uri, TypeReference<T> type) throws Exception {
        try (Response response = client.newCall(request).execute()) {
            int responseCode = response.code();
            String responseBody = response.body().string();
            FinhubLogger.info("【对接连连】Request uri >> {} HTTP code>>{} result >> {}", uri, responseCode, responseBody);
            
            if (ResponseHttpCode.OK == responseCode && verifyResponse(response, responseBody)) {
            	FinhubLogger.info("The signature is passed.");
                return JSON.parseObject(responseBody, type);
            }
        } catch (Exception e) {
            throw e;
        }
        
        return null;
    }
    
    /**
     * 验签，检验返回的真实性
     * Check the signature and verify the authenticity of the return.
     *
     * @param response
     * @param responseBody
     * @return
     */
    protected boolean verifyResponse(Response response, String responseBody) {
        try {
            String llpSignHeader = response.header(LianlianContants.SIGNATURE_HEADER);
            if (StringUtils.isBlank(llpSignHeader)) {
            	return true;
            }
            llpSignHeader = llpSignHeader.trim();
            String[] arr = llpSignHeader.split(",");
            String responseEpoch = arr[0].substring("t=".length());
            String responseSign = arr[1].substring("v=".length());
            // 确认epoch时间
            String epoch = validEpoch(responseEpoch);
            return RSA.verify(RSA.Mode.SHA256withRSA, epoch + LianlianContants.CONCAT_STR + responseBody, responseSign, publicKey);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * epoch时间校验，与当前时间误差10分钟内
     * Epoch time verification, within 10 minutes from the current time.
     *
     * @param epoch
     * @return
     * @throws SignatureException
     */
    private String validEpoch(String epoch) throws SignatureException {
        try {
            long t = Long.parseLong(epoch);
            if (Math.abs(System.currentTimeMillis() / 1000 - t) > REQUEST_TIME_THRESHOLD) {
                throw new SignatureException("Invalid signature time.");
            }
            return epoch;
        } catch (Exception e) {
            throw new SignatureException("Invalid signature time.");
        }
    }
    
}
