package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AccountApplyReqDTO implements Serializable {

    @JSONField(name = "txn_seqno")
    private String txnSeqno;

    @JSONField(name = "notify_url")
    private String notifyUrl;

    @JSONField(name = "channel_online_type")
    private String channelOnlineType;

    @JSONField(name = "subject_info")
    private SubjectInfo subjectInfo;

    @JSONField(name = "business_info")
    private BusinessInfo businessInfo;

    @JSONField(name = "business_qualifications")
    private List<BusinessQualification> businessQualifications;

    @JSONField(name = "sales_infos")
    private SalesInfos salesInfos;

    @JSONField(name = "related_personnel")
    private RelatedPersonnel relatedPersonnel;

    @JSONField(name = "product_infos")
    private List<ProductInfo> productInfos;

    @JSONField(name = "settle_info")
    private SettleInfo settleInfo;

    @JSONField(name = "protocol_info")
    private ProtocolInfo protocolInfo;


}
