package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 实体卡对象
 */
@Data
public class PhysicalCardApplyReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     商户请求唯一编号
     */
    @JSONField(name = "out_batch_no")
    private String outBatchNo;
    /**
        template_id
    string
        商务卡模版id
    必需
<= 19 字符
     */
    @JSONField(name = "template_id")
    private String templateId;
    /**
        currency
    string
        卡币种
    必需
    枚举值:
    CNY
     */
    private String currency;

    @JSONField(name = "card_list")
    private List<PhysicalCardApplyListReq> cardList;

    @JSONField(name = "shipping_address")
    private PhysicalCardApplyShippingAddress shippingAddress;
}
