package com.fenbeitong.dech.lianlian.dto.acct;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryAcctReqDTO {

	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 账户号
	 */
	@JSONField(name = "account_no")
	private String accountNo;
}
