package com.fenbeitong.dech.lianlian.dto.withdrawal;

import java.math.BigDecimal;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.BaseRespDTO;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/09
 */
@Data
public class SmsVerifyResponseDTO extends BaseRespDTO {

	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 交易结果代码 0000代表受理成功
	 */
	@JSONField(name = "ret_code")
	private String retCode;
	
	/**
	 * 交易结果描述
	 */
	@JSONField(name = "ret_msg")
	private String retMsg;
	
	/**
	 * 商户提现单号
	 */
	@JSONField(name = "out_order_no")
	private String outOrderNo;
	
	/**
	 * 系统充值单号
	 */
	@JSONField(name = "order_no")
	private String orderNo;
	
	/**
	 * 该笔订单的资金总额，单位为RMB-元。 大于0的数字，精确到小数点后两位
	 */
	@JSONField(name = "order_amount")
	private BigDecimal orderAmount;
	
	/**
	 * 账期
	 */
	@JSONField(name = "account_date")
	private String accountDate;
	
}
