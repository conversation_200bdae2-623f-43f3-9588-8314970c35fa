package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体卡对象
 */
@Data
public class CaptchaApplyReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     用户id
     必需
     验证码会发到用户关联的手机号/邮箱上
     */
    @JSONField(name = "user_id")
    private String userId;

    /**
     枚举值 名称
     MOBILE 手机号
     EMAIL 邮件
     */
    @JSONField(name = "send_type")
    private String sendType;

    /**
     业务类型

     reset_card_pin
     physcard_active
     */
    @JSONField(name = "busi_type")
    private String busiType;







}
