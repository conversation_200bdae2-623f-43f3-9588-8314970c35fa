package com.fenbeitong.dech.lianlian.constant;

public enum LianLianAccountUrlEnum {

    /**========================Fat===================*/
    FILE_V1_UPLOAD_FILE_FAT("/merchant/v1/file/uploadfile","上传文件"),
    CUSTOMER_ACCESS_APPLY_FAT("/v2/customer/access/apply","商户进件-进件申请"),
    CUSTOMER_ACCESS_QUERY_FAT("/mpay-openapi/v1/customer/access/query","商务进件-进件结果查询"),

    /**========================PROD===================*/
    FILE_V1_UPLOAD_FILE("/v1/file/uploadfile","上传文件"),
    PROTOCOL_DOWNLOAD("/v1/customer/protocol/download","协议下载"),
    CUSTOMER_ACCESS_APPLY("/sp/v2/customer/access/apply","商户进件-进件申请"),

    CUSTOMER_MODIFY_BASE_INFO("/sp/v2/customer/modify/baseInfo","商务进件-基本信息变更"),

    CUSTOMER_MODIFY_PRODUCT_INFO("/sp/v1/customer/modify/productInfo","商务进件-产品信息变更"),
    CUSTOMER_ACCESS_QUERY("/sp/v1/customer/access/query","商务进件-进件结果查询"),

    CUSTOMER_MODIFY_QUERY("/sp/v1/customer/modify/query","商务进件-变更结果查询"),

    ACCOUNT_ACTIVE_APPLY("/account/v1/active/apply","账户激活申请"),

    ACCOUNT_ACTIVE_VERIFY("/account/v1/active/verify","账户激活验证")
    ;
    private final String url;
    private final String desc;

    LianLianAccountUrlEnum(String url, String desc){
        this.url = url;
        this.desc = desc;
    }

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }
}
