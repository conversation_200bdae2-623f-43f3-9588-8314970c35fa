package com.fenbeitong.dech.lianlian.dto.template;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class ConsumeRule implements Serializable {
    @JSONField(name = "emp_visible")
    private boolean empVisible;

    @JSONField(name = "pay_scene")
    private String payScene;

    @JSONField(name = "single_day_limit")
    private String singleDayLimit;

    @JSONField(name = "single_limit")
    private String singleLimit;

    @JSONField(name = "single_month_limit")
    private String singleMonthLimit;
}
