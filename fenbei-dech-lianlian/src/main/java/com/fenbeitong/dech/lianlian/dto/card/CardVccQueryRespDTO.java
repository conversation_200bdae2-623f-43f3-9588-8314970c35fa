package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data

public class CardVccQueryRespDTO extends LianlianCardBaseRespDTO{
    /**
     *  string
     安全码
     必需
     调用方需要base64 decode，根据PCI标准，此字段不可存储
     */
    @JSONField(name ="security_code")
    private String securityCode;
    /**
     *  string
     *  商务卡唯一编号
     *  必需
     */
    @JSONField(name ="account_no")
    private String accountNo;
    /**
     *  string
     *         银行虚拟卡号
     *     必需
     *     卡申请成功后返回，可用于绑卡消费
     */
    @JSONField(name ="bank_card_no")
    private String bankCardNo;
    /**
     *  string <MMYY>
     *        银行虚拟卡号有效期
     *    必需
     *     示例值:
     *         0726
     */
    @JSONField(name ="bank_card_expire_time")
    private String bankCardExpireTime;

}
