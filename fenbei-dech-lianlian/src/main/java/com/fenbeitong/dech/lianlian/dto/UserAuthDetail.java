package com.fenbeitong.dech.lianlian.dto;

import lombok.Data;

import java.io.Serializable;

@Data
@SuppressWarnings("serial")
public class UserAuthDetail implements Serializable {

	private String thirdUserId;

	/**
     * 绑定状态
	 * Y：绑定
     * N：未绑定
	 */
	private String bindingStatus;

	/**
	 * 连连用户号
	 */
	private String userId;

	/**
	 * kyc状态
     * 绑定状态N不返回；
     * 绑定状态Y返回：
         * 0:审核中
         * 1:审核通过
         * 2:审核不通过
	 */
	private String kycStatus;

}
