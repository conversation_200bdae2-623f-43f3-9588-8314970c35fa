package com.fenbeitong.dech.lianlian.dto;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 * @date 2024/06/28
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class QueryAcctFlowRequestDTO implements Serializable {

	private String thirdUserId;
	
	/**
	 * 开始时间 交易发⽣开始时间，查询开始到结束时间不超 过30天，⽰例："2023-04-05 00:00:00"
	 */
	private String applicationStartTime;
	
	private String applicationEndTime;
	
	private List<String> currency;
	
	/**
	 * 每⻚数量，默认20
	 */
	private int pageSize;
	
	/**
	 * 分⻚查询返回的最新分⻚定位 分⻚查询时结果中有返回 则查询下⼀⻚需要赋值 
	 */
	private String cursor;
}
