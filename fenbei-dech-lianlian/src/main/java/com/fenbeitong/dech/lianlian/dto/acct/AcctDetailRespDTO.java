package com.fenbeitong.dech.lianlian.dto.acct;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.BaseRespDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcctDetailRespDTO extends BaseRespDTO {

	/**
	 * 交易结果代码 0000代表受理成功
	 */
	@JSONField(name = "ret_code")
	private String retCode;

	/**
	 * 交易结果描述
	 */
	@JSONField(name = "ret_msg")
	private String retMsg;

	/**
	 * 公司主体id
	 */
	@JSONField(name = "enterprise_id")
    private String enterpriseId;

	/**
	 * 公司主体名称
	 */
	@JSONField(name = "enterprise_name")
    private String enterpriseName;

	/**
	 * 账户号
	 */
	@JSONField(name = "account_no")
	private String accountNo;

	/**
	 * 账户名称
	 */
	@JSONField(name = "account_name")
	private String accountName;

	/**
	 * 账户状态 NORMAL
	 */
	@JSONField(name = "account_status")
	private String accountStatus;

	/**
	 * 账户币种
	 */
	private String currency;

	/**
	 * 银行账户号 加款充值的银行账户号
	 */
	@JSONField(name = "bank_account_no")
	private String bankAccountNo;

	/**
	 * 银行账户名称
	 */
	@JSONField(name = "bank_account_name")
	private String bankAccountName;

	/**
	 * 银行名称
	 */
	@JSONField(name = "bank_name")
	private String bankName;

	/**
	 * 银行号
	 */
	@JSONField(name = "bank_no")
	private String bankNo;

	/**
	 * 支行名称
	 */
	@JSONField(name = "bank_branch_name")
	private String bankBranchName;

	/**
	 * 资金余额 单位：元
	 */
	@JSONField(name = "amt_balcur")
	private String amtBalcur;

	/**
	 * 可用余额 单位：元
	 */
	@JSONField(name = "amt_balaval")
	private String amtBalaval;

	/**
	 * 冻结金额 单位：元
	 */
	@JSONField(name = "amt_balfrz")
	private String amtBalfrz;

    /**
     * 账户类型：BUSINESS_CARD_MARGIN、BUSINESS_CARD、BASE
     */
    @JSONField(name = "account_type")
    private String accountType;

}
