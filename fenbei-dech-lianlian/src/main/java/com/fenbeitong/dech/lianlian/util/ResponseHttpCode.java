package com.fenbeitong.dech.lianlian.util;
/**
 * 连连可能返回的http状态码
 * Possible returned HTTP status codes.
 *
 * <AUTHOR>
 * @since 2022/2/22
 */
public class ResponseHttpCode {
    /**
     * 成功 Success.
     */
    public static final int OK = 200;

    /**
     * 业务失败 Business failure.
     */
    public static final int BAD_REQUEST = 400;

    /**
     * 客户端未授权或验签失败
     * The client is not authorized or the signature verification fails.
     */
    public static final int UNAUTHORIZED = 401;

    /**
     * 资源不存在，在定义REST api时我们把所有的请求当作为资源
     * Resources do not exist. When defining the rest API, we regard all requests as resources.
     * 例如：https://global-api.lianlianpay.com/collections/accounts/account/{accountId}接口
     * 当accountId = accountA的时候正常返回200，accountId = accountB的时候可能返回404
     */
    public static final int NOT_FOUND = 404;

    /**
     * 不支持方法类型 Method type is not supported.
     */
    public static final int METHOD_NOT_ALLOWED = 405;

    /**
     * 服务器内部异常 Server internal exception.
     */
    public static final int INTERNAL_SERVER_ERROR = 500;
}