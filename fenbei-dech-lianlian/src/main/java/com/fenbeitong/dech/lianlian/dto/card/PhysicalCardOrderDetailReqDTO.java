package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体卡对象
 */
@Data
public class PhysicalCardOrderDetailReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     商户请求唯一编号
     */
    @JSONField(name = "out_batch_no")
    private String outBatchNo;



}
