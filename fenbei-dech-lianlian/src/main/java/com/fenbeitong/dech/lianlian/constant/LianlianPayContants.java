package com.fenbeitong.dech.lianlian.constant;

/**
 * @Description:
 * <AUTHOR>
 * @date 2024年6月21日
*/
public interface LianlianPayContants {
	/**
     * 沙盒环境host
     * Sandbox environment host.
     */
    String SAND_BOX_HOST = "https://cwallet-openapi.lianlianpay-inc.com";

    /**
     * 线上环境host
     * Online environment host.
     */
    String ONLINE_HOST = "https://cwallet-openapi.lianlianpay.com";

    /**
     * 编码格式 Coding format
     */
    String ENCODE = "UTF-8";

    /**
     * 连接字符串 Concat string
     */
    String CONCAT_STR = "&";

    /**
     * 授权头 Authorization header
     */
    String SIGNATURE_TYPE_VALUE = "RSA";

    /**
     * 签名头 Signature header
     */
    String SIGNATURE_TYPE_KEY = "Signature-Type";

    /**
     * 签名头 Signature header
     */
    String SIGNATURE_DATA_KEY = "Signature-Data";

    /**
     * 连连提供的developId
     * DEVELOP_ID, Provided by LianLian
     */
    String DEVELOP_ID = "48dU96IgR349BfY9hJyq8Z1ad";

    /**
     * 连连提供的token
     * TOKEN, Provided by LianLian
     */
    String TOKEN = "OapZWvNz3ZxJB0yX24nvirN0k9yv5Vma";

    /**
     * Basic 认证
     */
    String BASIC_TOKEN = "Basic ";

    /**
     * Bearer 认证 Basic authentication
     */
    String BEARER_TOKEN = "Bearer <<accessToken>>";


    String epoch = System.currentTimeMillis() / 1000 + "";

    String POST = "POST";

    String GET = "GET";

}
