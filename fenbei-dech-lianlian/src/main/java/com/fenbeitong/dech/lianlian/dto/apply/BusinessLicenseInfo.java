package com.fenbeitong.dech.lianlian.dto.apply;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class BusinessLicenseInfo implements Serializable {
    @JSONField(name = "license_type")
    private String licenseType;

    @JSONField(name = "license_img")
    private String licenseImg;

    @JSONField(name = "license_number")
    private String licenseNumber;

    @JSONField(name = "license_name")
    private String licenseName;

    @JSONField(name = "validity_start_time")
    private String validityStartTime;

    @JSONField(name = "validity_end_time")
    private String validityEndTime;

    @JSONField(name = "business_scope")
    private String businessScope;

    @JSONField(name = "registered_capital")
    private String registeredCapital;
    /**
     * 注册资本
     */
    @JSONField(name = "registered_capital_currency")
    private String registeredCapitalCurrency;

    @JSONField(name = "real_pay_capital")
    private String realPayCapital;
    @JSONField(name = "real_pay_capital_currency")
    private String realPayCapitalCurrency;

    @JSONField(name = "register_date")
    private String registerDate;

    @JSONField(name = "approved_date")
    private String approvedDate;

    @JSONField(name = "address_info")
    private AddressInfo addressInfo;
}
