package com.fenbeitong.dech.lianlian.dto.withdrawal;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.dech.lianlian.dto.BaseRespDTO;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/15
 */
@Data
public class ApplyReceiptRespDTO extends BaseRespDTO {

	/**
	 * 交易结果代码 0000代表受理成功
	 */
	@JSONField(name = "ret_code")
	private String retCode;
	
	/**
	 * 交易结果描述
	 */
	@JSONField(name = "ret_msg")
	private String retMsg;
	
	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 商户提现单号
	 */
	@JSONField(name = "out_order_no")
	private String outOrderNo;
	
	/**
	 * 系统充值单号
	 */
	@JSONField(name = "order_no")
	private String orderNo;
	
	/**
	 * 商户回单申请流水
	 */
	@JSONField(name = "receipt_seqno")
	private String receiptSeqno;
	
	/**
	 * 渠道订单号
	 */
//	@JSONField(name = "chnl_txno")
//	private String chnlTxno;
	
	/**
	 * 状态: APPLY：申请中、SUCCESS：成功 FAIL：失败
	 */
	@JSONField(name = "receipt_status")
	private String receiptStatus;
	
	/**
	 * 回单下载链接
	 */
	@JSONField(name = "download_url")
	private String downloadUrl;
	
}
