package com.fenbeitong.dech.lianlian.dto.withdrawal;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/16
 */
@Data
public class TradeDetailRespDTO {

	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 交易结果代码 0000代表受理成功
	 */
	@JSONField(name = "ret_code")
	private String retCode;
	
	/**
	 * 交易结果描述
	 */
	@JSONField(name = "ret_msg")
	private String retMsg;
	
	/**
	 * 结果集总数
	 */
	private Integer total;
	
	/**
	 * 资金流水列表
	 */
	@JSONField(name = "acctbal_list")
	private List<TradeDetailItemDTO> acctbalList;
}
