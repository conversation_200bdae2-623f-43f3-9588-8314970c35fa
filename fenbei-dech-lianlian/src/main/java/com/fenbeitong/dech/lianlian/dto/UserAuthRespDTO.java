package com.fenbeitong.dech.lianlian.dto;

import lombok.Data;

import java.io.Serializable;

@Data
@SuppressWarnings("serial")
public class UserAuthRespDTO implements Serializable {

	private String thirdUserId;

	/**
     * 绑定状态
	 * Y：绑定
     * N：未绑定
	 */
	private String bindingStatus;

	/**
	 * 回调地址
     * 返回格式 https://global.lianlianpay-inc111.com/signin?token=uue33dk0582kdkdsss{&redirectUrl=回调地址}
     * token：链接过期校验值，2小时
     * 注意：&redirectUrl=回调地址，当商户需要回调到自己页面时需要拼接
	 */
	private String returnUrl;

	/**
	 * kyc状态
     * 绑定状态N不返回；
     * 绑定状态Y返回：
         * 0:审核中
         * 1:审核通过
         * 2:审核不通过
	 */
	private String kycStatus;

}
