package com.fenbeitong.dech.lianlian.dto.withdrawal;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeDetailReqDTO {

	/**
	 * 商户号
	 */
	@JSONField(name = "mch_id")
	private String mchId;
	
	/**
	 * 账户号
	 */
	@JSONField(name = "account_no")
	private String accountNo;
	
	/**
	 * 交易账期查询开始时间，必须小于等于当前时间，闭区间。格式：yyyy-MM-dd HH:mm:ss
	 */
	@JSONField(name = "start_time")
	private String startTime;
	
	/**
	 * 交易账期查询结束时间，必须大于等于开始时间且小于等于当前时间，闭区间。注：为了查询效率考虑，开始和结束时间约束在7天内。格式：yyyy-MM-dd HH:mm:ss
	 */
	@JSONField(name = "end_time")
	private String endTime;
	
	/**
	 * 请求页码 表示当前请求第几页，从1开始计数
	 */
	private Integer page;
	
	/**
	 * 每页记录数 1 <= 值 <= 100
	 */
	private Integer limit;
	
	/**
	 * 出入账标识 DEBIT 出账、CREDIT 入账
	 */
	@JSONField(name = "flag_dc")
	private String flagDc;
	
	/**
	 * 业务类型 CONSUME 消费、 REFUND 退款、 REVERSE 撤销、 ADJUST 调账、 CHARGE 充值、 WITHDRAWAL提现
	 */
	@JSONField(name = "biz_type")
	private String bizType;
}
