package com.fenbeitong.dech.lianlian.dto.card;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
@Data
public class LianlianCardBaseRespDTO implements Serializable  {
    @JSONField(name ="ret_code")
    private String retCode;
    @JSONField(name ="ret_msg")
    private String retMsg;

    public boolean success(){
        return "0000".equals(retCode);
    }
}
