package com.fenbeitong.dech.lianlian.dto.card;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CardModifyLimitReqDTO implements Serializable {
    /**
     * mch_id string 商户唯一编码 必需
     * <= 18 字符
     */
    @JSONField(name = "mch_id")
    private String mchId;
    /**
     string
     商务卡唯一编号
     <= 32 字符
     */
    @JSONField(name = "account_no")
    private String accountNo;
    /**
     string
     单笔限额
     可选
     单位：元，最大限额：300000
     */
    @JSONField(name = "single_limit")
    private String singleLimit;
    /**
     string
     单日限额
     可选
     单位：元，最大限额********
     */
    @JSONField(name = "single_day_limit")
    private String singleDayLimit;
    /**
     string
     单月限额
     可选
     单位：元，最大限额********
     */
    @JSONField(name = "single_month_limit")
    private String singleMonthLimit;
}
