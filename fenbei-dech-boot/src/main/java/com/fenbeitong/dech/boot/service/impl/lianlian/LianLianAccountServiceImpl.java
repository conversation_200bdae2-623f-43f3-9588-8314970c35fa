package com.fenbeitong.dech.boot.service.impl.lianlian;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.fenbeitong.dech.api.model.dto.lianlian.LianLianBaseDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.req.*;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianAccountActiveApplyRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianAccountApplyResultQueryRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianAccountQueryRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianProtocolDownloadRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.file.LianLianFileUploadRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.file.LianLianFileUploadRpcRespDTO;
import com.fenbeitong.dech.api.service.lianlian.ILianLianAccountService;
import com.fenbeitong.dech.common.until.NewOssHandler;
import com.fenbeitong.dech.lianlian.dto.acct.AcctDetailRespDTO;
import com.fenbeitong.dech.lianlian.dto.acct.AcctListRespDTO;
import com.fenbeitong.dech.lianlian.dto.acct.QueryAcctReqDTO;
import com.fenbeitong.dech.lianlian.dto.apply.*;
import com.fenbeitong.dech.lianlian.dto.card.LianlianCardBaseRespDTO;
import com.fenbeitong.dech.lianlian.dto.file.FileUploadReqDTO;
import com.fenbeitong.dech.lianlian.dto.file.FileUploadRespDTO;
import com.fenbeitong.dech.lianlian.service.LianLianAccountApplyService;
import com.fenbeitong.dech.lianlian.service.LianlianAccountService;
import com.fenbeitong.dech.lianlian.util.RSA;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.zip.InflaterInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 *
 */
@DubboService
public class LianLianAccountServiceImpl implements ILianLianAccountService {
    @Autowired
    private LianLianAccountApplyService lianLianAccountApplyService;

    @Autowired
    private LianlianAccountService lianlianAccountService;

    @NacosValue("${lianlian.key.public}")
    private String publicKey;
    @Autowired
    NewOssHandler newOssHandler;

    @Override
    public LianLianFileUploadRpcRespDTO fileUpload(LianLianFileUploadRpcReqDTO lianLianFileUploadRpcReqDTO) {
        FileUploadReqDTO uploadFileReqDTO = new FileUploadReqDTO();
        uploadFileReqDTO.setTxnTime(lianLianFileUploadRpcReqDTO.getTxnTime());
        uploadFileReqDTO.setTxnSeqno(lianLianFileUploadRpcReqDTO.getTxnSeqno());
        uploadFileReqDTO.setUserNo(lianLianFileUploadRpcReqDTO.getUserNo());
        uploadFileReqDTO.setFileType(lianLianFileUploadRpcReqDTO.getFileType());
        uploadFileReqDTO.setFileContext(lianLianFileUploadRpcReqDTO.getFileContext());
        uploadFileReqDTO.setTimestamp(lianLianFileUploadRpcReqDTO.getTimestamp());
        uploadFileReqDTO.setContextType(lianLianFileUploadRpcReqDTO.getContextType());
        FileUploadRespDTO fileUploadRespDTO = lianLianAccountApplyService.upload(uploadFileReqDTO);
        LianLianFileUploadRpcRespDTO lianLianFileUploadRpcRespDTO = new LianLianFileUploadRpcRespDTO();
        lianLianFileUploadRpcRespDTO.setTxnSeqno(fileUploadRespDTO.getTxnSeqno());
        lianLianFileUploadRpcRespDTO.setDocId(fileUploadRespDTO.getDocId());
        return lianLianFileUploadRpcRespDTO;
    }

    @Override
    public boolean apply(LianLianAccountApplyRpcReqDTO lianLianAccountApplyRpcReqDTO) {
        FinhubLogger.info("apply:{}", JSON.toJSON(lianLianAccountApplyRpcReqDTO));
        AccountApplyReqDTO accountApplyReqDTO = new AccountApplyReqDTO();
        accountApplyReqDTO.setTxnSeqno(lianLianAccountApplyRpcReqDTO.getTxnSeqno());
        accountApplyReqDTO.setNotifyUrl(lianLianAccountApplyRpcReqDTO.getNotifyUrl());
        accountApplyReqDTO.setChannelOnlineType(lianLianAccountApplyRpcReqDTO.getChannelOnlineType());
        accountApplyReqDTO.setSubjectInfo(convertSubjectInfo(lianLianAccountApplyRpcReqDTO.getSubjectInfo()));
        accountApplyReqDTO.setBusinessInfo(convertBusinessInfo(lianLianAccountApplyRpcReqDTO.getBusinessInfo()));
        accountApplyReqDTO.setBusinessQualifications(convertBusinessQualification(lianLianAccountApplyRpcReqDTO.getBusinessQualifications()));
        accountApplyReqDTO.setSalesInfos(convertSalesInfos(lianLianAccountApplyRpcReqDTO.getSalesInfos()));
        accountApplyReqDTO.setRelatedPersonnel(convertRelatedPersonnel(lianLianAccountApplyRpcReqDTO.getRelatedPersonnel()));
        accountApplyReqDTO.setProductInfos(convertProductInfo(lianLianAccountApplyRpcReqDTO.getProductInfos()));
        accountApplyReqDTO.setSettleInfo(convertSettleInfo(lianLianAccountApplyRpcReqDTO.getSettleInfo()));
        accountApplyReqDTO.setProtocolInfo(convertProtocolInfo(lianLianAccountApplyRpcReqDTO.getProtocolInfo()));
        LianlianCardBaseRespDTO lianlianCardBaseRespDTO =  lianLianAccountApplyService.apply(accountApplyReqDTO);
        return lianlianCardBaseRespDTO.success();
    }

    @Override
    public LianLianAccountApplyResultQueryRpcRespDTO applyResultQuery(LianLianAccountApplyResultQueryRpcReqDTO lianLianAccountApplyResultQueryRpcReqDTO) {
        AccountApplyResultReqDTO accountApplyResultReqDTO = new AccountApplyResultReqDTO();
        accountApplyResultReqDTO.setTxnSeqno(lianLianAccountApplyResultQueryRpcReqDTO.getTxnSeqno());
        AccountApplyResultRespDTO accountApplyResultRespDTO = lianLianAccountApplyService.applyResultQuery(accountApplyResultReqDTO);
        LianLianAccountApplyResultQueryRpcRespDTO lianLianAccountApplyResultQueryRpcRespDTO = new LianLianAccountApplyResultQueryRpcRespDTO();
        lianLianAccountApplyResultQueryRpcRespDTO.setStatus(accountApplyResultRespDTO.getStatus());
        lianLianAccountApplyResultQueryRpcRespDTO.setFailReason(accountApplyResultRespDTO.getFailReason());
        lianLianAccountApplyResultQueryRpcRespDTO.setMchId(accountApplyResultRespDTO.getMchId());
        lianLianAccountApplyResultQueryRpcRespDTO.setTxnSeqno(accountApplyResultRespDTO.getTxnSeqno());
        lianLianAccountApplyResultQueryRpcRespDTO.setVirtualCard(accountApplyResultRespDTO.getVirtualCard());
        lianLianAccountApplyResultQueryRpcRespDTO.setAliSubMchid(accountApplyResultRespDTO.getAliSubMchid());
        lianLianAccountApplyResultQueryRpcRespDTO.setWxSubMchid(accountApplyResultRespDTO.getWxSubMchid());
        return lianLianAccountApplyResultQueryRpcRespDTO;
    }

    @Override
    public boolean modifyBaseInfo(LianLianAccountModifyBaseInfoRpcReqDTO lianLianAccountModifyBaseInfoRpcReqDTO) {
        FinhubLogger.info("lianlian modifyBaseInfo:{}", JSON.toJSON(lianLianAccountModifyBaseInfoRpcReqDTO));
        AccountModifyBaseInfoReqDTO accountModifyBaseInfoReqDTO = new AccountModifyBaseInfoReqDTO();
        accountModifyBaseInfoReqDTO.setTxnSeqno(lianLianAccountModifyBaseInfoRpcReqDTO.getTxnSeqno());
        accountModifyBaseInfoReqDTO.setNotifyUrl(lianLianAccountModifyBaseInfoRpcReqDTO.getNotifyUrl());
        accountModifyBaseInfoReqDTO.setMchid(lianLianAccountModifyBaseInfoRpcReqDTO.getMchid());
        accountModifyBaseInfoReqDTO.setSubjectInfo(convertSubjectInfo(lianLianAccountModifyBaseInfoRpcReqDTO.getSubjectInfo()));
        //不上送营业执照号
        accountModifyBaseInfoReqDTO.getSubjectInfo().getBusinessLicenseInfo().setLicenseNumber(null);
        accountModifyBaseInfoReqDTO.setBusinessInfo(convertBusinessInfo(lianLianAccountModifyBaseInfoRpcReqDTO.getBusinessInfo()));
        accountModifyBaseInfoReqDTO.setBusinessQualifications(convertBusinessQualification(lianLianAccountModifyBaseInfoRpcReqDTO.getBusinessQualifications()));
        accountModifyBaseInfoReqDTO.setRelatedPersonnel(convertRelatedPersonnel(lianLianAccountModifyBaseInfoRpcReqDTO.getRelatedPersonnel()));
        accountModifyBaseInfoReqDTO.setProtocolInfo(convertProtocolInfo(lianLianAccountModifyBaseInfoRpcReqDTO.getProtocolInfo()));
        LianlianCardBaseRespDTO lianlianCardBaseRespDTO =  lianLianAccountApplyService.modifyBaseInfo(accountModifyBaseInfoReqDTO);
        return lianlianCardBaseRespDTO.success();
    }


    @Override
    public boolean modifyProductInfo(LianLianAccountModifyProductInfoRpcReqDTO lianLianAccountModifyProductInfoRpcReqDTO) {
        FinhubLogger.info("lianlian modifyProductInfo:{}", JSON.toJSON(lianLianAccountModifyProductInfoRpcReqDTO));
        AccountModifyProductInfoReqDTO accountModifyProductInfoReqDTO = new AccountModifyProductInfoReqDTO();
        accountModifyProductInfoReqDTO.setTxnSeqno(lianLianAccountModifyProductInfoRpcReqDTO.getTxnSeqno());
        accountModifyProductInfoReqDTO.setNotifyUrl(lianLianAccountModifyProductInfoRpcReqDTO.getNotifyUrl());
        accountModifyProductInfoReqDTO.setMchid(lianLianAccountModifyProductInfoRpcReqDTO.getMchid());
        if(lianLianAccountModifyProductInfoRpcReqDTO.getSalesInfos() == null){
            lianLianAccountModifyProductInfoRpcReqDTO.setSalesInfos(new LianLianSalesInfos());
        }
        accountModifyProductInfoReqDTO.setSalesInfos(convertSalesInfos(lianLianAccountModifyProductInfoRpcReqDTO.getSalesInfos()));
        accountModifyProductInfoReqDTO.setProductInfos(convertProductInfo(lianLianAccountModifyProductInfoRpcReqDTO.getProductInfos()));
        if (lianLianAccountModifyProductInfoRpcReqDTO.getSettleInfo() != null){
            if (lianLianAccountModifyProductInfoRpcReqDTO.getSettleInfo().getSettleMaterials() == null){
                lianLianAccountModifyProductInfoRpcReqDTO.getSettleInfo().setSettleMaterials(new LianLianSettleMaterials());
            }
        }else {
            return false;
        }
        accountModifyProductInfoReqDTO.setSettleInfo(convertSettleInfo(lianLianAccountModifyProductInfoRpcReqDTO.getSettleInfo()));
        if (lianLianAccountModifyProductInfoRpcReqDTO.getProtocolInfo() == null){
            lianLianAccountModifyProductInfoRpcReqDTO.setProtocolInfo(new LianLianProtocolInfo());
        }
        accountModifyProductInfoReqDTO.setProtocolInfo(convertProtocolInfo(lianLianAccountModifyProductInfoRpcReqDTO.getProtocolInfo()));
        LianlianCardBaseRespDTO lianlianCardBaseRespDTO =  lianLianAccountApplyService.modifyProductInfo(accountModifyProductInfoReqDTO);
        return lianlianCardBaseRespDTO.success();
    }




    private List<ProductInfo> convertProductInfo(List<LianLianProductInfo> lianLianProductInfos){
        List<ProductInfo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lianLianProductInfos)){
            for (LianLianProductInfo lianLianProductInfo:lianLianProductInfos) {
                ProductInfo productInfo = new ProductInfo();
                productInfo.setPayType(lianLianProductInfo.getPayType());
                productInfo.setVccBizScene(lianLianProductInfo.getVccBizScene());
                productInfo.setChargeInfos(convertChargeInfo(lianLianProductInfo.getChargeInfos()));
                productInfo.setChargeTemplateId(lianLianProductInfo.getChargeTemplateId());
                productInfo.setProductCode(lianLianProductInfo.getProductCode());
//                productInfo.setWxAlipayInfo(convertWxAlipayInfo(lianLianProductInfo.getWxAlipayInfo()));
                productInfo.setReserved(convertLianLianReserved(lianLianProductInfo.getReserved()));
                list.add(productInfo);
            }
        }
        return list;
    }
    private List<ChargeInfo> convertChargeInfo(List<LianLianChargeInfo> lianLianChargeInfos){
        List<ChargeInfo> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(lianLianChargeInfos)){
            for (LianLianChargeInfo lianLianChargeInfo:lianLianChargeInfos) {
                ChargeInfo chargeInfo = new ChargeInfo();
                chargeInfo.setBankCode(lianLianChargeInfo.getBankCode());
                chargeInfo.setRate(lianLianChargeInfo.getRate());
                chargeInfo.setDownLimit(lianLianChargeInfo.getDownLimit());
                chargeInfo.setUpLimit(lianLianChargeInfo.getUpLimit());
                chargeInfo.setPayType(lianLianChargeInfo.getPayType());
                chargeInfo.setCardType(lianLianChargeInfo.getCardType());
                list.add(chargeInfo);
            }
        }
        return list;
    }
    private WxAlipayInfo convertWxAlipayInfo(LianLianWxAlipayInfo lianLianWxAlipayInfo){
        if (lianLianWxAlipayInfo == null){
            return null;
        }
        WxAlipayInfo wxAlipayInfo = new WxAlipayInfo();
        wxAlipayInfo.setAlipayMchName(lianLianWxAlipayInfo.getAlipayMchName());
        wxAlipayInfo.setAlipayMchNo(lianLianWxAlipayInfo.getAlipayMchNo());
        wxAlipayInfo.setWxMchName(lianLianWxAlipayInfo.getWxMchName());
        wxAlipayInfo.setWxMchNo(lianLianWxAlipayInfo.getWxMchNo());
        return wxAlipayInfo;
    }
    private Reserved convertLianLianReserved(LianLianReserved lianLianReserved){
        if (lianLianReserved == null){
            return null;
        }
        Reserved reserved = new Reserved();
        reserved.setFrequency(lianLianReserved.getFrequency());
        reserved.setRemark(lianLianReserved.getRemark());
        reserved.setMaxAmt(lianLianReserved.getMaxAmt());
        reserved.setTimeUnit(lianLianReserved.getTimeUnit());
        reserved.setValidMonth(lianLianReserved.getValidMonth());
        return reserved;
    }
    private ProtocolInfo convertProtocolInfo(LianLianProtocolInfo lianLianProtocolInfo){
        ProtocolInfo protocolInfo = new ProtocolInfo();
        protocolInfo.setAuthorizationDoc(lianLianProtocolInfo.getAuthorizationDoc());
        protocolInfo.setProcotolDoc(lianLianProtocolInfo.getProcotolDoc());
        protocolInfo.setConfirmMethod(lianLianProtocolInfo.getConfirmMethod());
        protocolInfo.setSignMethod(lianLianProtocolInfo.getSignMethod());
        protocolInfo.setSignPerson(lianLianProtocolInfo.getSignPerson());
        return protocolInfo;
    }

    private SettleInfo convertSettleInfo(LianLianSettleInfo lianLianSettleInfo){
        SettleInfo settleInfo = new SettleInfo();
        settleInfo.setBankCardInfo(convertBankCardInfo(lianLianSettleInfo.getBankCardInfo()));
        settleInfo.setSettleMaterials(convertSettleMaterials(lianLianSettleInfo.getSettleMaterials()));
        return settleInfo;
    }

    private BankCardInfo convertBankCardInfo(LianLianBankCardInfo lianLianBankCardInfo){
        BankCardInfo bankCardInfo = new BankCardInfo();
        bankCardInfo.setBankCode(lianLianBankCardInfo.getBankCode());
        bankCardInfo.setAccountName(encode(lianLianBankCardInfo.getAccountName()));
        bankCardInfo.setAccountNumber(encode(lianLianBankCardInfo.getAccountNumber()));
        bankCardInfo.setAccountType(lianLianBankCardInfo.getAccountType());
        bankCardInfo.setCnapsName(lianLianBankCardInfo.getCnapsName());
        bankCardInfo.setCnapsCode(lianLianBankCardInfo.getCnapsCode());
        return bankCardInfo;
    }
    private SettleMaterials convertSettleMaterials(LianLianSettleMaterials lianLianSettleMaterials){
        SettleMaterials settleMaterials = new SettleMaterials();
        if(lianLianSettleMaterials.getSettleIdCard() != null) {
            settleMaterials.setSettleIdCard(encode(lianLianSettleMaterials.getSettleIdCard()));
        }
        settleMaterials.setBankCardBack(lianLianSettleMaterials.getBankCardBack());
        settleMaterials.setBankCardFront(lianLianSettleMaterials.getBankCardFront());
        settleMaterials.setOpenPermit(lianLianSettleMaterials.getOpenPermit());
        settleMaterials.setOtherFile(lianLianSettleMaterials.getOtherFile());
        return settleMaterials;
    }
    private RelatedPersonnel convertRelatedPersonnel(LianLianRelatedPersonnel lianLianRelatedPersonnel){
        RelatedPersonnel relatedPersonnel = new RelatedPersonnel();
        relatedPersonnel.setUacInfo(convertUACInfo(lianLianRelatedPersonnel.getUacInfo()));
        relatedPersonnel.setAdminInfo(convertAdminInfo(lianLianRelatedPersonnel.getAdminInfo()));
        relatedPersonnel.setUboInfos(convertUBOInfos(lianLianRelatedPersonnel.getUboInfos()));
        relatedPersonnel.setShareholderInfos(convertShareholderInfos(lianLianRelatedPersonnel.getShareholderInfos()));
        relatedPersonnel.setContactInfos(convertContactInfo(lianLianRelatedPersonnel.getContactInfos()));
        relatedPersonnel.setReceiveInfo(convertReceiveInfo(lianLianRelatedPersonnel.getReceiveInfo()));
        return relatedPersonnel;
    }

    private ReceiveInfo convertReceiveInfo(LianLianReceiveInfo lianLianReceiveInfo){
        ReceiveInfo receiveInfo = new ReceiveInfo();
        receiveInfo.setAddressInfo(convertAddress(lianLianReceiveInfo.getAddressInfo()));
        receiveInfo.setRecipients(lianLianReceiveInfo.getRecipients());
        receiveInfo.setRecipientsEmail(encode(lianLianReceiveInfo.getRecipientsEmail()));
        receiveInfo.setRecipients(lianLianReceiveInfo.getRecipients());
        receiveInfo.setRecipientsPhone(encode(lianLianReceiveInfo.getRecipientsPhone()));
        return receiveInfo;
    }
    private List<ContactInfo> convertContactInfo(List<LianLianContactInfo> lianLianContactInfos){
        List<ContactInfo> list = new ArrayList<>();
        //默认技术人
        ContactInfo contactInfoDefault = new ContactInfo();
        contactInfoDefault.setContactEmail(encode("<EMAIL>"));
        contactInfoDefault.setContactPhone(encode("***********"));
        contactInfoDefault.setContactName(encode("韩磊"));
        contactInfoDefault.setContactType("TECH_CONTACT_PERSON");
        list.add(contactInfoDefault);
        //默认技术人
        ContactInfo contactInfoDefault2 = new ContactInfo();
        contactInfoDefault2.setContactEmail(encode("<EMAIL>"));
        contactInfoDefault2.setContactPhone(encode("***********"));
        contactInfoDefault2.setContactName(encode("符晓婉"));
        contactInfoDefault2.setContactType("BUSINESS_CONTACT_PERSON");
        list.add(contactInfoDefault2);
        //默认技术人
        ContactInfo contactInfoDefault3 = new ContactInfo();
        contactInfoDefault3.setContactEmail(encode("<EMAIL>"));
        contactInfoDefault3.setContactPhone(encode("***********"));
        contactInfoDefault3.setContactName(encode("陈瑾"));
        contactInfoDefault3.setContactType("RISK_CONTACT_PERSON");
        list.add(contactInfoDefault3);
        //默认技术人
        ContactInfo contactInfoDefault4 = new ContactInfo();
        contactInfoDefault4.setContactEmail(encode("<EMAIL>"));
        contactInfoDefault4.setContactPhone(encode("***********"));
        contactInfoDefault4.setContactName(encode("符晓婉"));
        contactInfoDefault4.setContactType("FIN_CONTACT_PERSON");
        list.add(contactInfoDefault4);
        if (CollectionUtils.isNotEmpty(lianLianContactInfos)){
            for (LianLianContactInfo lianLianContactInfo:lianLianContactInfos) {
                ContactInfo contactInfo = new ContactInfo();
                contactInfo.setContactEmail(encode(lianLianContactInfo.getContactEmail()));
                contactInfo.setContactPhone(encode(lianLianContactInfo.getContactPhone()));
                contactInfo.setContactName(encode(lianLianContactInfo.getContactName()));
                contactInfo.setContactType(lianLianContactInfo.getContactType());
                list.add(contactInfo);
            }
        }
        return list;
    }
    private List<ShareholderInfo> convertShareholderInfos(List<LianLianShareholderInfo> lianLianShareholderInfos){
        List<ShareholderInfo> shareholderInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lianLianShareholderInfos)){
            for (LianLianShareholderInfo lianLianShareholderInfo:lianLianShareholderInfos) {
                ShareholderInfo shareholderInfo = new ShareholderInfo();
                shareholderInfo.setIdBack(lianLianShareholderInfo.getIdBack());
                shareholderInfo.setIdNo(encode(lianLianShareholderInfo.getIdNo()));
                shareholderInfo.setIdType(lianLianShareholderInfo.getIdType());
                shareholderInfo.setIdName(encode(lianLianShareholderInfo.getIdName()));
                shareholderInfo.setIdFront(lianLianShareholderInfo.getIdFront());
                shareholderInfo.setValidityEndTime(lianLianShareholderInfo.getValidityEndTime());
                shareholderInfo.setValidityStartTime(lianLianShareholderInfo.getValidityStartTime());
                shareholderInfos.add(shareholderInfo);
            }
        }
        return shareholderInfos;
    }
    private List<Ubo> convertUbo(List<LianLianUbo> uboList){
        List<Ubo> ubos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(uboList)){
            for (LianLianUbo lianLianUbo:uboList) {
                Ubo ubo = new Ubo();
                ubo.setAddressInfo(convertAddress(lianLianUbo.getAddressInfo()));
                ubo.setIdInfo(convertIdInfo(lianLianUbo.getIdInfo()));
                ubo.setSpecialApprove(lianLianUbo.getSpecialApprove());
                ubos.add(ubo);
            }
        }
        return ubos;
    }
    private UBOInfos convertUBOInfos(LianLianUBOInfos lianLianUBOInfos){
        UBOInfos  uboInfos = new UBOInfos();
        uboInfos.setUboList(convertUbo(lianLianUBOInfos.getUboList()));
        uboInfos.setIdentifyingMethod(lianLianUBOInfos.getIdentifyingMethod());
        uboInfos.setProveFiles(lianLianUBOInfos.getProveFiles());
        return uboInfos;
    }
    private UACInfo convertUACInfo(LianLianUACInfo lianLianUACInfo){
        UACInfo uacInfo = new UACInfo();
        uacInfo.setIsIdentity(lianLianUACInfo.getIsIdentity());
        if ("Y".equals(lianLianUACInfo.getIsIdentity())){
            return uacInfo;
        }
        uacInfo.setIdInfo(convertIdInfo(lianLianUACInfo.getIdInfo()));
        uacInfo.setAddressInfo(convertAddress(lianLianUACInfo.getAddressInfo()));
        return uacInfo;
    }
    private AdminInfo convertAdminInfo(LianLianAdminInfo lianLianAdminInfo){
        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setAdminEmail(encode(lianLianAdminInfo.getAdminEmail()));
        adminInfo.setAdminPhone(encode(lianLianAdminInfo.getAdminPhone()));
        adminInfo.setAdminName(encode(lianLianAdminInfo.getAdminName()));
        return adminInfo;
    }
    private List<OfflineInfo> convertOfflineInfo(List<LianLianOfflineInfo> lianLianOfflineInfos){
        List<OfflineInfo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lianLianOfflineInfos)) {
            for (LianLianOfflineInfo lianLianOfflineInfo:lianLianOfflineInfos) {
                OfflineInfo offlineInfo = new OfflineInfo();
                offlineInfo.setCashierPhoto(lianLianOfflineInfo.getCashierPhoto());
                offlineInfo.setBusinessInteriorPhotos(lianLianOfflineInfo.getBusinessInteriorPhotos());
                offlineInfo.setDoorHeadPhoto(lianLianOfflineInfo.getDoorHeadPhoto());
                offlineInfo.setSpecialFiles(lianLianOfflineInfo.getSpecialFiles());
                offlineInfo.setOperatorsShopPhoto(lianLianOfflineInfo.getOperatorsShopPhoto());
                list.add(offlineInfo);
            }
        }
        return list;
    }
    private List<WebInfo> convertWebInfo(List<LianLianWebInfo> lianLianWebInfos){
        List<WebInfo> webInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lianLianWebInfos)) {
            for (LianLianWebInfo lianLianWebInfo: lianLianWebInfos) {
                WebInfo webInfo = new WebInfo();
                webInfo.setAuthorityDoc(lianLianWebInfo.getAuthorityDoc());
                webInfo.setOtherFiles(lianLianWebInfo.getOtherFiles());
                webInfo.setDomain(lianLianWebInfo.getDomain());
                webInfo.setIcpType(lianLianWebInfo.getIcpType());
                webInfo.setIcpLicenseNum(lianLianWebInfo.getIcpLicenseNum());
                webInfo.setIcpSubjectName(lianLianWebInfo.getIcpSubjectName());
                webInfos.add(webInfo);
            }
        }
        return webInfos;
    }
    private List<AppInfo> convertAppInfo(List<LianLianAppInfo> lianLianAppInfos){
        List<AppInfo> appInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lianLianAppInfos)) {
            for (LianLianAppInfo lianLianAppInfo:lianLianAppInfos) {
                AppInfo appInfo = new AppInfo();
                appInfo.setApplicationDesc(lianLianAppInfo.getApplicationDesc());
                appInfo.setApplicationName(lianLianAppInfo.getApplicationName());
                appInfo.setSceneType(lianLianAppInfo.getSceneType());
                appInfo.setOtherFiles(lianLianAppInfo.getOtherFiles());
                appInfo.setApplicationIdentification(lianLianAppInfo.getApplicationIdentification());
                appInfos.add(appInfo);
            }
        }
        return appInfos;
    }
    private SalesInfos convertSalesInfos(LianLianSalesInfos lianLianSalesInfos){
        SalesInfos salesInfos = new SalesInfos();
        salesInfos.setAppInfo(convertAppInfo(lianLianSalesInfos.getAppInfo()));
        salesInfos.setOfflineInfo(convertOfflineInfo(lianLianSalesInfos.getOfflineInfo()));
        salesInfos.setWebInfo(convertWebInfo(lianLianSalesInfos.getWebInfo()));
        return salesInfos;
    }
    private List<BusinessQualification> convertBusinessQualification(List<LianLianBusinessQualification> lianLianBusinessQualifications){
        List<BusinessQualification> businessQualifications = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lianLianBusinessQualifications)){
            for (LianLianBusinessQualification lianLianBusinessQualification:lianLianBusinessQualifications) {
                BusinessQualification businessQualification = new BusinessQualification();
                businessQualification.setQualificationDoc(lianLianBusinessQualification.getQualificationDoc());
                businessQualification.setQualificationName(lianLianBusinessQualification.getQualificationName());
                businessQualification.setValidityEndTime(lianLianBusinessQualification.getValidityEndTime());
                businessQualification.setValidityStartTime(lianLianBusinessQualification.getValidityStartTime());
                businessQualifications.add(businessQualification);
            }
        }
        return businessQualifications;
    }
    private BusinessInfo convertBusinessInfo(LianLianBusinessInfo lianLianBusinessInfo){

        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setMerchantName(lianLianBusinessInfo.getMerchantName());
        businessInfo.setMerchantShortName(lianLianBusinessInfo.getMerchantShortName());
        businessInfo.setServicePhone(lianLianBusinessInfo.getServicePhone());
        businessInfo.setMcc(lianLianBusinessInfo.getMcc());
        businessInfo.setBusinessDesc(lianLianBusinessInfo.getBusinessDesc());
        businessInfo.setOfficeSpace(lianLianBusinessInfo.getOfficeSpace());
        businessInfo.setBusinessClassify(lianLianBusinessInfo.getBusinessClassify());
        businessInfo.setExpectTradeAmount(lianLianBusinessInfo.getExpectTradeAmount());
        businessInfo.setForeignBusiness(lianLianBusinessInfo.getForeignBusiness());
        businessInfo.setExclusiveCooperate(lianLianBusinessInfo.getExclusiveCooperate());
        businessInfo.setBusinessScale(lianLianBusinessInfo.getBusinessScale());
        businessInfo.setAdditionalFile(lianLianBusinessInfo.getAdditionalFile());
        businessInfo.setAddressInfo(convertAddress(lianLianBusinessInfo.getAddressInfo()));
        return businessInfo;
    }
    private SubjectInfo convertSubjectInfo(LianLianSubjectInfo lianLianSubjectInfo){
        if (lianLianSubjectInfo != null){
            SubjectInfo subjectInfo = new SubjectInfo();
            if (lianLianSubjectInfo.getIdentityInfo()!=null) {
                subjectInfo.setIdentityInfo(convertIdentityInfo(lianLianSubjectInfo.getIdentityInfo()));
            }
            subjectInfo.setSubjectType(lianLianSubjectInfo.getSubjectType());
            if (lianLianSubjectInfo.getBusinessLicenseInfo() != null) {
                subjectInfo.setBusinessLicenseInfo(convertBusinessLicenseInfo(lianLianSubjectInfo.getBusinessLicenseInfo()));
            }
            return subjectInfo;
        }
        return null;
    }
    private BusinessLicenseInfo convertBusinessLicenseInfo(LianLianBusinessLicenseInfo lianLianBusinessLicenseInfo){
        BusinessLicenseInfo businessLicenseInfo = new BusinessLicenseInfo();
        if (lianLianBusinessLicenseInfo.getAddressInfo()!= null) {
            businessLicenseInfo.setAddressInfo(convertAddress(lianLianBusinessLicenseInfo.getAddressInfo()));
        }
        businessLicenseInfo.setBusinessScope(lianLianBusinessLicenseInfo.getBusinessScope());
        businessLicenseInfo.setLicenseImg(lianLianBusinessLicenseInfo.getLicenseImg());
        businessLicenseInfo.setLicenseName(lianLianBusinessLicenseInfo.getLicenseName());
        businessLicenseInfo.setLicenseNumber(encode(lianLianBusinessLicenseInfo.getLicenseNumber()));
        businessLicenseInfo.setLicenseType(lianLianBusinessLicenseInfo.getLicenseType());
        businessLicenseInfo.setApprovedDate(lianLianBusinessLicenseInfo.getApprovedDate());
        businessLicenseInfo.setRegisteredCapital(lianLianBusinessLicenseInfo.getRegisteredCapital());
        businessLicenseInfo.setRegisteredCapitalCurrency(lianLianBusinessLicenseInfo.getRegisteredCapitalCurrency());
        businessLicenseInfo.setRealPayCapital(lianLianBusinessLicenseInfo.getRealPayCapital());
        businessLicenseInfo.setRealPayCapitalCurrency(lianLianBusinessLicenseInfo.getRealPayCapitalCurrency());
        businessLicenseInfo.setRegisterDate(lianLianBusinessLicenseInfo.getRegisterDate());
        businessLicenseInfo.setValidityEndTime(lianLianBusinessLicenseInfo.getValidityEndTime());
        businessLicenseInfo.setValidityStartTime(lianLianBusinessLicenseInfo.getValidityStartTime());
        return businessLicenseInfo;
    }

    private AddressInfo convertAddress(LianLianAddressInfo lianLianAddressInfo){

        AddressInfo addressInfo = new AddressInfo();
        if (lianLianAddressInfo == null){
            return  addressInfo;
        }
        addressInfo.setAddress(lianLianAddressInfo.getAddress());
        addressInfo.setCity(lianLianAddressInfo.getCity());
        addressInfo.setArea(lianLianAddressInfo.getArea());
        addressInfo.setNationality(lianLianAddressInfo.getNationality());
        addressInfo.setLatitude(lianLianAddressInfo.getLatitude());
        addressInfo.setLongitude(lianLianAddressInfo.getLongitude());
        addressInfo.setProvince(lianLianAddressInfo.getProvince());
        return addressInfo;
    }
    private IdentityInfo convertIdentityInfo(LianLianIdentityInfo lianLianIdentityInfo){
        IdentityInfo identityInfo = new IdentityInfo();
        if (lianLianIdentityInfo.getAddressInfo()!= null) {
            identityInfo.setAddressInfo(convertAddress(lianLianIdentityInfo.getAddressInfo()));
        }
        if (lianLianIdentityInfo.getIdInfo()!=null) {
            identityInfo.setIdInfo(convertIdInfo(lianLianIdentityInfo.getIdInfo()));
        }

        identityInfo.setAge(lianLianIdentityInfo.getAge());
        identityInfo.setPhone(encode(lianLianIdentityInfo.getPhone()));
        if (StringUtils.isNotBlank(lianLianIdentityInfo.getOperatorsIdcard())) {
            identityInfo.setOperatorsIdcard(encode(lianLianIdentityInfo.getOperatorsIdcard()));
        }
        return identityInfo;
    }

    private IdInfo convertIdInfo(LianLianIdInfo lianLianIdInfo){
        IdInfo idInfo = new IdInfo();
        idInfo.setIdBack(lianLianIdInfo.getIdBack());
        idInfo.setIdNo(encode(lianLianIdInfo.getIdNo()));
        idInfo.setIdFront(lianLianIdInfo.getIdFront());
        idInfo.setIdName(encode(lianLianIdInfo.getIdName()));
        idInfo.setIdType(lianLianIdInfo.getIdType());
        idInfo.setValidityStartTime(lianLianIdInfo.getValidityStartTime());
        idInfo.setValidityEndTime(lianLianIdInfo.getValidityEndTime());
        return idInfo;
    }

    private String encode(String data){
        try {
            return RSA.encrypt(data,publicKey);
        }catch (Exception e){
            FinhubLogger.error("encode error data:" + data,e);
            return null;
        }
    }

    /*
	 * (non-javadoc)
	 * @see com.fenbeitong.dech.api.service.lianlian.ILianLianAccountService#queryAcctInfor(com.fenbeitong.dech.api.model.dto.lianlian.account.req.QueryLianlianAcctReqDTO)
	 */
    @Override
    public QueryLianlianAcctRespDTO queryAcctInfor(QueryLianlianAcctReqDTO request) {
        AcctListRespDTO resp = lianlianAccountService.queryAcctList(QueryAcctReqDTO.builder().mchId(request.getMchId()).build());
        if (Objects.isNull(resp) ||CollectionUtils.isEmpty(resp.getList()) || !resp.isSuccess()) {
            return null;
        }
        FinhubLogger.info("lianlianAccountService.queryAcctList:{}", JsonUtils.toJson(resp.getList()));
        AcctDetailRespDTO acctDetail = resp.getList().stream().filter(ad -> (StringUtils.equalsIgnoreCase("NORMAL", ad.getAccountStatus())&&StringUtils.equalsIgnoreCase("BASE", ad.getAccountType()))).findFirst().orElse(resp.getList().get(0));
        FinhubLogger.info("lianlianAccountService.acctDetail:{}", JsonUtils.toJson(acctDetail));
        QueryLianlianAcctRespDTO result = new QueryLianlianAcctRespDTO();
        BeanUtils.copyProperties(acctDetail, result);
        result.setMchId(request.getMchId());
        return result;
    }

    @Override
    public LianLianAccountActiveApplyRpcRespDTO activeApply(LianLianAccountActiveApplyRpcReqDTO lianLianAccountActiveApplyRpcReqDTO) {
        AccountActiveApplyReqDTO accountActiveApplyReqDTO = new AccountActiveApplyReqDTO();
        accountActiveApplyReqDTO.setBankAccountNo(lianLianAccountActiveApplyRpcReqDTO.getBankAccountNo());
        accountActiveApplyReqDTO.setMchId(lianLianAccountActiveApplyRpcReqDTO.getMchId());
        accountActiveApplyReqDTO.setOutOrderNo(lianLianAccountActiveApplyRpcReqDTO.getOutOrderNo());
        AccountActiveApplyRespDTO accountActiveApplyRespDTO = lianLianAccountApplyService.activeApply(accountActiveApplyReqDTO);
        LianLianAccountActiveApplyRpcRespDTO lianLianAccountActiveApplyRpcRespDTO = new LianLianAccountActiveApplyRpcRespDTO();
        lianLianAccountActiveApplyRpcRespDTO.setToken(accountActiveApplyRespDTO.getToken());
        lianLianAccountActiveApplyRpcRespDTO.setRetMsg(accountActiveApplyRespDTO.getRetMsg());
        lianLianAccountActiveApplyRpcRespDTO.setRetCode(accountActiveApplyRespDTO.getRetMsg());
        return lianLianAccountActiveApplyRpcRespDTO;
    }

    /**
     * 激活
     */
    @Override
    public LianLianBaseDTO activeVerify(LianLianAccountActiveVerifyRpcReqDTO lianLianAccountActiveVerifyRpcReqDTO) {
        AccountActiveVerifyReqDTO accountApplyResultReqDTO = new AccountActiveVerifyReqDTO();
        accountApplyResultReqDTO.setBankAccountNo(lianLianAccountActiveVerifyRpcReqDTO.getBankAccountNo());
        accountApplyResultReqDTO.setToken(lianLianAccountActiveVerifyRpcReqDTO.getToken());
        accountApplyResultReqDTO.setVerifyCode(lianLianAccountActiveVerifyRpcReqDTO.getVerifyCode());
        accountApplyResultReqDTO.setMchId(lianLianAccountActiveVerifyRpcReqDTO.getMchId());
        accountApplyResultReqDTO.setOutOrderNo(lianLianAccountActiveVerifyRpcReqDTO.getOutOrderNo());
        LianlianCardBaseRespDTO lianlianCardBaseRespDTO =  lianLianAccountApplyService.activeVerify(accountApplyResultReqDTO);
        LianLianBaseDTO lianLianBaseDTO = new LianLianBaseDTO();
        lianLianBaseDTO.setRetCode(lianlianCardBaseRespDTO.getRetCode());
        lianLianBaseDTO.setRetMsg(lianlianCardBaseRespDTO.getRetMsg());
        return lianLianBaseDTO;
    }

    @Override
    public LianLianAccountQueryRpcRespDTO accountDetail(LianLianAccountQueryRpcReqDTO lianLianAccountQueryRpcReqDTO) {
        QueryAcctReqDTO queryAcctReqDTO = new QueryAcctReqDTO();
        queryAcctReqDTO.setMchId(lianLianAccountQueryRpcReqDTO.getMchId());
        queryAcctReqDTO.setAccountNo(lianLianAccountQueryRpcReqDTO.getAccountNo());
        AcctDetailRespDTO acctDetailRespDTO =  lianlianAccountService.queryAcctDetail(queryAcctReqDTO);
        LianLianAccountQueryRpcRespDTO lianLianAccountQueryRpcRespDTO = new LianLianAccountQueryRpcRespDTO();
        lianLianAccountQueryRpcRespDTO.setRetCode(acctDetailRespDTO.getRetCode());
        lianLianAccountQueryRpcRespDTO.setRetMsg(acctDetailRespDTO.getRetMsg());
        lianLianAccountQueryRpcRespDTO.setAccountName(acctDetailRespDTO.getAccountName());
        lianLianAccountQueryRpcRespDTO.setAccountStatus(acctDetailRespDTO.getAccountStatus());
        lianLianAccountQueryRpcRespDTO.setBankAccountName(acctDetailRespDTO.getBankAccountName());
        lianLianAccountQueryRpcRespDTO.setAccountNo(acctDetailRespDTO.getAccountNo());
        lianLianAccountQueryRpcRespDTO.setBankAccountNo(acctDetailRespDTO.getBankAccountNo());
        lianLianAccountQueryRpcRespDTO.setAmtBalaval(acctDetailRespDTO.getAmtBalaval());
        lianLianAccountQueryRpcRespDTO.setAmtBalcur(acctDetailRespDTO.getAmtBalcur());
        lianLianAccountQueryRpcRespDTO.setAmtBalfrz(acctDetailRespDTO.getAmtBalfrz());
        lianLianAccountQueryRpcRespDTO.setCurrency(acctDetailRespDTO.getCurrency());
        lianLianAccountQueryRpcRespDTO.setBankNo(acctDetailRespDTO.getBankNo());
        lianLianAccountQueryRpcRespDTO.setBankName(acctDetailRespDTO.getBankName());
        lianLianAccountQueryRpcRespDTO.setBankBranchName(acctDetailRespDTO.getBankBranchName());
        lianLianAccountQueryRpcRespDTO.setEnterpriseId(acctDetailRespDTO.getEnterpriseId());
        lianLianAccountQueryRpcRespDTO.setEnterpriseName(acctDetailRespDTO.getEnterpriseName());
        return lianLianAccountQueryRpcRespDTO;
    }

    @Override
    public LianLianProtocolDownloadRespDTO protocolDownload(LianLianProtocolDownloadReqDTO lianLianProtocolDownloadReqDTO) {
        ProtocolDownloadReqDTO protocolDownloadReqDTO = new ProtocolDownloadReqDTO();
        protocolDownloadReqDTO.setMchId(lianLianProtocolDownloadReqDTO.getMchId());
        ProtocolDownloadRespDTO protocolDownloadRespDTO = lianLianAccountApplyService.protocolDownload(protocolDownloadReqDTO);
        String fbtUrl= null;
        String fbtName = null;
        if (protocolDownloadRespDTO.success() && StringUtils.isNotEmpty(protocolDownloadRespDTO.getBody())) {
            byte[] keyByte = Base64.getDecoder().decode(protocolDownloadRespDTO.getBody());
            ZipInputStream zipInputStream = new ZipInputStream(new ByteArrayInputStream(keyByte));
            ZipEntry zipEntry;
            try {
                while ((zipEntry = zipInputStream.getNextEntry()) != null){
                    String name = zipEntry.getName();
                    FinhubLogger.info("file name:{}",name);
                    if (name.contains("pdf")){
                        byte[] bs = getZipEntryData(zipInputStream);
                        File destFile =  bytesToFile(bs,tempPath,name);
                        String fileName = ObjectId.get().toString();
                        fbtUrl = newOssHandler.uploadFileToOss(destFile, "system", fileName, name, BankNameEnum.LIANLIAN.getCode());
                        fbtName = name;
                        break;
                    }
                }
            }catch (IOException e){
                FinhubLogger.error("协议文件提取失败:" + lianLianProtocolDownloadReqDTO.getMchId());
            }
        }
        LianLianProtocolDownloadRespDTO lianLianProtocolDownloadRespDTO = new LianLianProtocolDownloadRespDTO();
        lianLianProtocolDownloadRespDTO.setFbtUrl(fbtUrl);
        lianLianProtocolDownloadRespDTO.setFileName(fbtName);
        lianLianProtocolDownloadRespDTO.setRetMsg(protocolDownloadRespDTO.getRetMsg());
        lianLianProtocolDownloadRespDTO.setRetCode(protocolDownloadRespDTO.getRetCode());
        return lianLianProtocolDownloadRespDTO;
    }
    private static final String SYSTEM_PROP_TMP_DIR = "java.io.tmpdir";
    String tempPath = System.getProperty(SYSTEM_PROP_TMP_DIR) + File.separator;
    public File bytesToFile(byte[] bytes,String outPath,String fileName){
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            File dir = new File (outPath);
            if (!dir.exists() && dir. isDirectory()) {//判断文件目泉是否存在
                dir.mkdirs();
            }
            file = new File(outPath + File.separator + fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream( fos);
            bos.write(bytes);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    FinhubLogger.error("bytesToFile 文件转换异常");
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    FinhubLogger.error("bytesToFile 文件转换异常");
                }
            }
        }
        return file;
    }
    public byte[] getZipEntryData(InflaterInputStream zis){
        try {
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            byte[] temp = new byte[1024];
            byte[] buf;
            int length;
            while ((length = zis.read(temp,0,1024))!=-1){
                bout.write(temp,0,length);
            }
            buf = bout.toByteArray();
            bout.close();
            return buf;
        }catch (IOException e){
            FinhubLogger.error("文件解压失败,请关注");
        }
        return null;
    }
}
