package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:51:23
 * @Version 1.0
 **/
@Data
public class TransferInApplySignVerifyReqDTO implements Serializable {
    // businessNo String 是 32 业务请求流水号  OB（固定字母，标记开放银行渠道） + appId（10位，不够10位的在左边补0）+ 两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） + yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0
    private String businessNo;
    // signId String 是 32 签约ID 申请签约接口返回 ****************
    private String signId;
    // otpValue String 是 10 验证码 验证码 432112
    private String otpValue;
    // agreementNo String 是 - 协议号 账户开户/开户结果查询/账户信息查询/授权/授权查询返回的协议号 -
    private String agreementNo;
}
