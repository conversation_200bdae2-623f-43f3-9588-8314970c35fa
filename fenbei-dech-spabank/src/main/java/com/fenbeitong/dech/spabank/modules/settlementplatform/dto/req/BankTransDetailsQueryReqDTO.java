package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName BankTransDetailsQueryReqDTO
 * @Description: 查询交易明细
 * <AUTHOR>
 * @Date 2021/11/5
 **/
@Data
public class BankTransDetailsQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	string(1)	Y	1:当日，2：历史
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 	见证子帐户的帐号	string(32)	Y
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 	查询标志	string(1)	Y	1：全部 2：转出 3：转入 
     */
    @JsonProperty(value = "QueryFlag")
    private String queryFlag;

    /*
     * 	开始日期	string(8)	N	若是历史查询，则必输，当日查询时，不起作用；开始日期不能超过当前日期
     */
    @JsonProperty(value = "StartDate")
    private String startDate;

    /*
     * 	终止日期	string(8)	N	若是历史查询，则必输，当日查询时，不起作用；终止日期不能超过当前日期
     */
    @JsonProperty(value = "EndDate")
    private String endDate;

    /*
     * 	页码	string(6)	Y	起始值为1，每次最多返回20条记录，第二页返回的记录数为第21至40条记录，第三页为41至60条记录，顺序均按照建立时间的先后
     */
    @JsonProperty(value = "PageNum")
    private String pageNum;

    /*
     * 	保留域	string(120)	N
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
