package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @ClassName SpaQueryReceiptRespDTO
 * @Description: 3.2历史单笔PDF回单查询接口[ELC002]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaHistoryReceiptQueryRespDTO extends SpaB2BICBaseRespDTO{

    /**
     * 	本次返回记录数
     * 	当前返回记录数据
     */
    private String ResultNum;

    /**
     * 	结束标志
     * 	Y-结束，后续无记录
     *  N-未结束
     */
    private String EndFlag;

    /**
     * 	回单类型
     */
    private String ReceiptType;

    /**
     * 	回单子类型
     */
    private String SubReceiptType;

    private List<SpaHistoryReceiptQueryDetails> list;

}
