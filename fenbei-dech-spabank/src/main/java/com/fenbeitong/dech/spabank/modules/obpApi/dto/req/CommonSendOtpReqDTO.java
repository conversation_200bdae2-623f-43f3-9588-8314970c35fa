package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class CommonSendOtpReqDTO implements Serializable {
    // 是否虚拟卡业务 目前业务报销卡需要上送subAppId， 虚拟卡不需要上送subAppId
    private Boolean isBankCard = Boolean.FALSE;
    // 	businessNo	业务请求流水号	string	必须
    private String	businessNo;
    // 	mobileNo	手机号
    private String	mobileNo;
    // 	accountNo	银行卡号
    private String	accountNo;
    // 	scene	场景码
    private String	scene;
    // 	bizKey	业务参数
    private String	bizKey;

}
