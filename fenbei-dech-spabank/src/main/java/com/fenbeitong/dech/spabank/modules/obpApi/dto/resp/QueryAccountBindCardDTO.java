package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

@Data
public class QueryAccountBindCardDTO extends SpaBankObpApiBaseRespDTO {
    // 绑定卡等级 绑卡等级0-信用卡 1-安全卡(主绑卡) 2-普通卡
    private String bindCardLevel;
    // 绑定卡加密卡号 绑定卡加密卡号
    private String bindCardNo;
    // 绑定卡掩码卡号 绑定卡掩码卡号
    private String bindMaskCardNo;
    // 是否超级卡 否为超级绑定卡 Y/N
    private String isSuperCard;
    // 银行名
    private String bankName;
}
