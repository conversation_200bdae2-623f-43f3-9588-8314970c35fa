package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName SmallAmountTransferQueryReqDTO
 * @Description: KFEJZB6061 查询小额鉴权转账结果 SmallAmountTransferQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SmallAmountTransferQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 返回状态	Y 1
     * (0：成功，1：失败，2：待确认)
     * 当返回2：待确认时，说明平安银行已转账成功，但是收款账户还未收到资金
     * SmallAmountTransferStatusEnum
     */
    @JsonProperty(value = "ReturnStatu")
    private String returnStatu;

    /*
     * 返回信息	Y 80
     * 失败返回具体信息
     */
    @JsonProperty(value = "ReturnMsg")
    private String returnMsg;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
