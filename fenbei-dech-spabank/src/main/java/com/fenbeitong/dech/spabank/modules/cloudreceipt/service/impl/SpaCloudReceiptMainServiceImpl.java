package com.fenbeitong.dech.spabank.modules.cloudreceipt.service.impl;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.dech.spabank.modules.cloudreceipt.client.CloudReceiptClient;
import com.fenbeitong.dech.spabank.modules.cloudreceipt.constant.CloudReceiptResult;
import com.fenbeitong.dech.spabank.modules.cloudreceipt.constant.SpaCloudReceiptMethodNameConstant;
import com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req.*;
import com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp.*;
import com.fenbeitong.dech.spabank.modules.cloudreceipt.service.SpaCloudReceiptMainService;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @ClassName CloudReceiptMainServiceImpl
 * @Description: 云收款基础service实现
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Service
public class SpaCloudReceiptMainServiceImpl implements SpaCloudReceiptMainService {

    @Autowired
    private CloudReceiptClient cloudReceiptClient;


    @Override
    public TradeEBankPayRespDTO tradeEBankPay(TradeEBankPayReqDTO tradeEBankPayReqDTO) {
        return requestFormCommon(SpaCloudReceiptMethodNameConstant.TRADE_E_BANK_PAY, JsonUtils.toJson(tradeEBankPayReqDTO), TradeEBankPayRespDTO.class);
    }

    @Override
    public QuerySingleOrderRespDTO querySingleOrder(QuerySingleOrderReqDTO querySingleOrderReqDTO) {
        return requestCommon(SpaCloudReceiptMethodNameConstant.QUERY_SINGLE_ORDER, JsonUtils.toJson(querySingleOrderReqDTO), QuerySingleOrderRespDTO.class);
    }

    @Override
    public ApplyRefundRespDTO applyRefund(ApplyRefundReqDTO applyRefundReqDTO) {
        return requestCommon(SpaCloudReceiptMethodNameConstant.APPLY_REFUND, JsonUtils.toJson(applyRefundReqDTO), ApplyRefundRespDTO.class);
    }

    @Override
    public QuerySingleRefundRespDTO querySingleRefund(QuerySingleRefundReqDTO querySingleRefundReqDTO) {
        return requestCommon(SpaCloudReceiptMethodNameConstant.QUERY_SINGLE_REFUND, JsonUtils.toJson(querySingleRefundReqDTO), QuerySingleRefundRespDTO.class);
    }

    @Override
    public DownloadReconciliationFileRespDTO downloadReconciliationFile(DownloadReconciliationFileReqDTO downloadReconciliationFileReqDTO) {
        return requestCommon(SpaCloudReceiptMethodNameConstant.DOWNLOAD_RECONCILIATION_FILE, JsonUtils.toJson(downloadReconciliationFileReqDTO), DownloadReconciliationFileRespDTO.class);
    }

    /**
     * @MethodName: requestCommon
     * @Description: 公共请求封装
     * @Param: [interfaceName, request]
     * @Return: T
     * @Author: Jarvis.li
     * @Date: 2021/10/15
     */
    public  <T> T requestCommon(String interfaceName, String request, Class<T> responseT)  {
        String response = null;
        long end = 0L;
        long start = 0L;
        try {
            start = System.currentTimeMillis();
            response = cloudReceiptClient.request(JsonUtils.toObj(request, Map.class), interfaceName);
            end = System.currentTimeMillis();
            CloudReceiptResult<T> resultVO = JsonUtils.toObj(response, CloudReceiptResult.class);
            if(ObjUtils.isEmpty(resultVO.getErrors())){
                return JSON.parseObject(JSON.toJSONString(resultVO.getData()), responseT);
            }
            FinhubLogger.error("【平安-云收款公共封装请求失败】【接口名{}】【耗时{}ms】【请求报文{}】【{}】",interfaceName, end-start, JSON.toJSON(request), resultVO.getErrors().get(0).getErrorMessage());
            return null;
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【平安-云收款公共封装请求异常】【接口名{}】【耗时{}ms】【请求报文{}】【返回报文{}】,exception=",interfaceName, end-start, JSON.toJSON(request), JSON.toJSON(response), e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_CLOUD_RECEIPT_EXCEPTION);
        }
    }

    /**
     * @MethodName: requestFormCommon
     * @Description: 公共请求封装
     * @Param: [interfaceName, request]
     * @Return: T
     * @Author: Jarvis.li
     * @Date: 2021/10/15
     */
    public  <T> T requestFormCommon(String interfaceName, String request, Class<T> responseT)  {
        String response = null;
        long end = 0L;
        long start = 0L;
        try {
            start = System.currentTimeMillis();
            response = cloudReceiptClient.requestForm(request, interfaceName);
            end = System.currentTimeMillis();
            return JSON.parseObject(response, responseT);
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【平安-云收款公共封装请求异常】【接口名{}】【耗时{}ms】【请求报文{}】【返回报文{}】,exception=",interfaceName, end-start, JSON.toJSON(request), JSON.toJSON(response), e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_CLOUD_RECEIPT_EXCEPTION);
        }
    }
}
