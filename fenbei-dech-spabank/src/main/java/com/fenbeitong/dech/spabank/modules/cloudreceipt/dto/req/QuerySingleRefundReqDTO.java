package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName QuerySingleRefundReqDTO
 * @Description: 3.7.5 查询单笔退款
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Data
public class QuerySingleRefundReqDTO extends SpaCloudReceiptBaseReqDTO{

    /*
     * 商户编号	Y 32
     * 商户在云收款系统的编号
     */
    @JsonProperty(value = "TraderNo")
    private String traderNo;

    /*
     * 退款订单号	Y 100
     * 商户系统生成的订单号，需要保证在商户系统唯一
     */
    @JsonProperty(value = "ReturnOrderNo")
    private String returnOrderNo;

    /*
     * 退款订单发送时间	Y 20
     * 商户系统订单生成时间 格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "ReturnOrderSendTime")
    private String returnOrderSendTime;

}
