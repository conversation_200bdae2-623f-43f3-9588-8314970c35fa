package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryReturnRemittanceDetailsRespDTO
 * @Description: 3.11支付退票查询[4019]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryHistoryBalanceReqDTO implements Serializable {

    /**
     * 	账号
     */
    private String Account;

    /**
     * 	日期
     */
    private String RptDate;

    /**
     * 	保留域
     */
    private String Reserve;

}
