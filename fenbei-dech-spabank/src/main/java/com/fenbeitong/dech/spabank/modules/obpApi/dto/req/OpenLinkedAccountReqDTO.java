package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class OpenLinkedAccountReqDTO implements Serializable {

    //	影像审核Id	必须	影像审核Id	最大长度:40
    private String  imageCheckId;
    // 是否虚拟卡业务 目前业务报销卡需要上送subAppId， 虚拟卡不需要上送subAppId
    private Boolean isBankCard;
    //	开户订单号	必须	来自开户前置接口返回	最大长度:40
    private String  openOrderNo;
    //	证件号码	必须	必须为二代身份证号码，必须为18位，字母X必须大写。需要和OTP发送接口中bizKey中的idNo的值一样。	最大长度:18
    private String  idNo;
    //	证件类型	必须	1：身份证，暂时只支持身份证	枚举:1
    private String  idType;
    //	手机号	必须	必须为待绑定的银行卡号的预留手机号码。需要和OTP发送接口中bizKey中的mobileNo的值一样。	最大长度:11
    private String  mobileNo;
    //	姓名	必须	需要和OTP发送接口中bizKey中的trueName的值一样。	最大长度:50
    private String  trueName;
    //	待绑定的银行卡号	必须	待绑定的银行卡号	最大长度:32
    private String  bindCardNo;
    //	职业代码	必须	职业代码，参考职业码值表。可以为空，但是如果该客户被要求补充此项信息将会开户失败	最大长度:32
    private String  occupation;
    //	工作单位名称	非必须	工作单位名称	最大长度:70
    private String  workOrgName;
    // 子商户号
    private String subAppId;
    //	开户授权标识	必须	平安银行电子II、III类户服务开户授权标识，string(1) 0:不授权 1:授权	枚举:0,1
    private String  openAcctAgreement;
    //	客户信息授权条款标识	必须	客户信息授权条款标识，string(1) 0:不授权 1:授权	枚举:0,1
    private String  userInfoAgreement;
    //	交易发起应用名称（监管需要）	必须	交易发起应用名称	最大长度:50
    private String  mchAppName;
    //	可以采集到的设备型号（监管需要）	必须	如 iPhone， Sagit( 小 米 6) ，MT -TL00（华为 Mate7）等	最大长度:50
    private String  deviceName;
    //	活动ID	非必须	活动ID	最大长度:50
    private String  activityId;
    //	业务流水号	必须	业务流水号需要和OTP发送接口中的businessNo的值一样,要求全局唯一， OB（固定字母，标记开放银行渠道） + appId（10位，不够10位的在左边补0）+ 两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） + yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）	最大长度:32
    private String  businessNo;
    //	调用方IP	必须	调用方IP	最大长度:32
    private String  sourceIP;
    //	交易报文采集持卡人使用手机支付时所设备的手机号（监管需要）	必须	交易报文采集持卡人使用手机支付时所设备的手机号（监管需要）	最大长度:32
    private String  fullDeviceNumber;
    //	设备类型（监管需要）	非必须	设备类型（监管需要）	最大长度:32
    private String  deviceType;
    //	SIM卡张数（监管需要）	非必须	0：未插SIM卡 1：1张SIM卡 2：2张SIM卡 3：其他	最大长度:10
    private String  simCardCount;
    //	OTP流水号	必须	OTP短信验证码发送接口返回的OTP订单号	最大长度:64
    private String  otpOrderNo;
    //	短信验证码	必须	生产环境会发送到用户手机上，测试环境短信验证码查询地址（登录账号和密码待银行分配）：https://test-b-fat.pingan.com.cn/bbc/otp/index.html	最大长度:10
    private String  otpValue;
    //	桌面系统采集硬盘序列号（监管需要）	必须	桌面系统采集硬盘序列号	最大长度:40
    private String  deviceID;
    //	终端类型	必须	终端类型	最大长度:40
    private String  terminalType;
    //	内媒一级渠道	非必须	内媒一级渠道	最大长度:40
    private String  innerId;
    //	橱窗位置	非必须	橱窗位置	最大长度:40
    private String  channelPosition;
    //	MGM流水号	非必须	MGM流水号	最大长度:40
    private String  activityFlowId;
    //	经纬度（监管需要）	必须	经纬度（监管需要）	最大长度:40
    private String  lbs;
    //	分包渠道	非必须	分包渠道	最大长度:40
    private String  mediaSource;
    //	IP版本号（监管需要）	必须	04：IPV4 06：IPV6	最大长度:40
    private String  ipType;
    //	MAC地址（监管需要）	必须	例如：00-23-5A-15-99-42，格式要求：每2位之间用 -连接	最大长度:40
    private String  macAddr;
    //	外媒参数相关	非必须	外媒参数相关	最大长度:40
    private String  source;
    //	外媒参数相关	非必须	外媒参数相关	最大长度:40
    private String  outerSource;
    //	外媒参数相关	非必须	外媒参数相关	最大长度:40
    private String  outerId;
    //	外媒参数相关	非必须	外媒参数相关	最大长度:40
    private String  cid;
    //	外媒参数相关	非必须	外媒参数相关	最大长度:50
    private String  umCode;
    //	住址地址-省份	必须	住址地址-省份	最大长度:60
    private String  homeProvince;
    //	住址地址-省份编码	必须	住址地址-省份编码	最大长度:10
    private String  homeProvinceCode;
    //	住址地址-城市	必须	住址地址-城市	最大长度:60
    private String  homeCity;
    //	住址地址-城市编码	必须	住址地址-城市编码	最大长度:10
    private String  homeCityCode;
    //	住址地址-区县	必须	住址地址-区县	最大长度:60
    private String  homeCounty;
    //	住址地址-区县编码	必须	住址地址-区县编码	最大长度:10
    private String  homeCountyCode;
    //	住址地址-街道地址	必须	住址地址-街道地址	最大长度:300
    private String  homeStreetAddress;
    //	住址地址-详细地址	非必须	住址地址-详细地址
    private String  homeAddressDetail;

}
