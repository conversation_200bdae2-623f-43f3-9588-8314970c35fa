package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName BankWithdrawCashBackQueryReqDTO
 * @Description: KFEJZB6048	查询银行提现退单信息	BankWithdrawCashBackQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class BankWithdrawCashBackQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     *  1-提现退票
     *  2-小额鉴权退票
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 开始日期  Y 8
     * 开始日期不能超过当前日期********
     */
    @JsonProperty(value = "StartDate")
    private String startDate;

    /*
     * 终止日期  Y 8
     * 终止日期不能超过当前日期********
     */
    @JsonProperty(value = "EndDate")
    private String endDate;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
