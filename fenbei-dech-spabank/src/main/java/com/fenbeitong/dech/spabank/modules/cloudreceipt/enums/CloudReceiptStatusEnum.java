package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

/**
 * @ClassName CloudReceiptRespEnum
 * @Description: 云收款接口状态枚举
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum CloudReceiptStatusEnum {

    PROCESSING("processing","处理中"),
    SUCCEEDED("succeeded","成功"),
    FAILED("failed","失败");

    CloudReceiptStatusEnum(String status, String desc){
        this.status = status;
        this.desc = desc;
    }

    private String status;
    private String desc;

    public String getPayCardType() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

}
