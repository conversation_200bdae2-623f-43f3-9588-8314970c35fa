package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class QueryBalanceReqDTO implements Serializable {
    // 是否虚拟卡业务 查询余额报销卡和虚拟卡上送key不同
    private Boolean isBankCard;
    // 	businessNo	业务请求流水号	string	必须
    private String	businessNo;
    // 	tradeNoId	业务请求流水号	string	必须
    private String	tradeNoId;
    // 	thirdId	必输，调用方唯一用户ID，最大32位长度。一个身份证号码只能有一个thirdId。
    private String	thirdId;
    // 	acctNoArray	卡号	string	必须
    private String	acctNoArray;
    // 	trueName	姓名	string	必须
    private String	trueName;
    // 	idNo	证件号	string	必须
    private String	idNo;
    // 	idType	证件类型	string	必须
    private String	idType;
    // 	agreementNo 协议号
    private String	agreementNo;
    // 	ccy 币种，默认查所有
    private String	ccy = "RMB";
    // 	isConvert 外币是否换算成人民币
    private String	isConvert = "1";
    // 	isNeedRitianli 是否需要日添利
    private String	isNeedRitianli = "N";
    // 	requestTime 必输，发起请求时的服务器时间，格式：yyyy-MM-dd HH:mm:ss服务提供方安全校验使用
    private String	requestTime;
    // 	otpOrderNo otp流水号
    private String	otpOrderNo;
    // 	otpValue otp验证码
    private String	otpValue;

}
