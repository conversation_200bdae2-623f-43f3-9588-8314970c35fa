package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MembershipTrancheFreezeReqDTO
 * @Description: KFEJZB6007	会员资金冻结-不验证	MembershipTrancheFreeze
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MembershipTrancheFreezeReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     * 1：冻结（会员→担保）
     * 2：解冻（担保→会员）
     * 4：见证+收单的冻结资金解冻
     * 5: 可提现冻结（会员→担保）
     * 6: 可提现解冻（担保→会员）
     * 7: 在途充值解冻（担保→会员）
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 子账户账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 交易网会员代码	Y 32
     * 交易网会员代码
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 消费金额	N 15
     * 订单的消费金额
     */
    @JsonProperty(value = "ConsumeAmt")
    private String consumeAmt;

    /*
     * 交易金额	Y 15
     * 交易网会员代码
     */
    @JsonProperty(value = "TranAmt")
    private String tranAmt;

    /*
     * 交易手续费	Y 15
     * 解冻时，将根据该金额收取手续费，若无手续费则送0。冻结时，改字段不启用
     */
    @JsonProperty(value = "TranCommission")
    private String tranCommission = "0";

    /*
     * 币种	Y 3
     * RMB
     */
    @JsonProperty(value = "Ccy")
    private String ccy = "RMB";

    /*
     * 订单号	Y 32
     * 全局唯一，不能与6034/6101/6006/6139订单号相同。如果是解冻，需要送冻结时送的子订单号或订单号
     */
    @JsonProperty(value = "OrderNo")
    private String orderNo;

    /*
     * 订单内容	N 500
     */
    @JsonProperty(value = "OrderContent")
    private String orderContent;

    /*
     * 备注	N 120
     */
    @JsonProperty(value = "Remark")
    private String remark;

    /*
     * 保留域	N 120
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
