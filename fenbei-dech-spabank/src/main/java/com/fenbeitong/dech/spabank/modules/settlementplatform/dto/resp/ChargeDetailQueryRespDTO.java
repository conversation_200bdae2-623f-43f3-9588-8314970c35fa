package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName ChargeDetailQueryRespDTO
 * @Description: KFEJZB6146	查询充值明细-见证收单	ChargeDetailQuery
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class ChargeDetailQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 交易状态	Y 1
     * （0：成功，1：失败，2：异常,3:冲正，5：待处理）
     */
    @JsonProperty(value = "TranStatus")
    private String tranStatus;

    /*
     * 交易金额	Y 15
     */
    @JsonProperty(value = "TranAmt")
    private String tranAmt;

    /*
     * 佣金费	Y 15
     */
    @JsonProperty(value = "CommissionAmt")
    private String commissionAmt;

    /*
     * 支付方式	Y 1
     * 0-冻结支付 1-普通支付
     */
    @JsonProperty(value = "PayMode")
    private String payMode;

    /*
     * 交易日期	Y 8
     */
    @JsonProperty(value = "TranDate")
    private String tranDate;

    /*
     * 交易时间	Y 6
     */
    @JsonProperty(value = "TranTime")
    private String tranTime;

    /*
     * 订单转入见证子账户的帐号	Y 32
     */
    @JsonProperty(value = "OrderInSubAcctNo")
    private String orderInSubAcctNo;

    /*
     * 订单转入见证子账户的名称	Y 120
     */
    @JsonProperty(value = "OrderInSubAcctName")
    private String orderInSubAcctName;

    /*
     * 订单实际转入见证子账户的帐号	Y 32
     */
    @JsonProperty(value = "OrderActInSubAcctNo")
    private String orderActInSubAcctNo;

    /*
     * 订单实际转入见证子账户的名称	Y 120
     */
    @JsonProperty(value = "OrderActInSubAcctName")
    private String orderActInSubAcctName;

    /*
     * 见证系统流水号	Y 16
     */
    @JsonProperty(value = "FrontSeqNo")
    private String frontSeqNo;

    /*
     * 交易描述	Y 120
     * 当充值失败时，返回交易失败原因
     */
    @JsonProperty(value = "ReservedMsg")
    private String TranDesc;
}
