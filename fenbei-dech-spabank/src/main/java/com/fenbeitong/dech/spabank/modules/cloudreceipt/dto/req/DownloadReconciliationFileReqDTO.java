package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName DownloadReconciliationFileReqDTO
 * @Description: 3.14 下载对账文件
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Data
public class DownloadReconciliationFileReqDTO{

    /*
     * 商户编号	Y 32
     * 商户在云收款系统的编号
     */
    @JsonProperty(value = "TraderNo")
    private String TraderNo;

    /*
     * 对账日期	Y 8
     * 对账日期 格式：yyyyMMdd
     */
    @JsonProperty(value = "ReconcileDate")
    private String reconcileDate;

}
