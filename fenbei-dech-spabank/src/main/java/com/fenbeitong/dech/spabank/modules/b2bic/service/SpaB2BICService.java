package com.fenbeitong.dech.spabank.modules.b2bic.service;

import com.fenbeitong.dech.spabank.modules.b2bic.dto.req.*;
import com.fenbeitong.dech.spabank.modules.b2bic.dto.resp.*;

/**
 * @ClassName SpaB2BICService
 * @Description: 平安银企直联
 * <AUTHOR>
 * @Date 2022/1/7
 **/
public interface SpaB2BICService {

    /*
     * @MethodName: queryTransferStatus
     * @Description: 3.5单笔转账指令查询[4005]
     * @Param: [reqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.b2bic.dto.resp.SpaQueryTransferStatusRespDTO
     * @Author: Jarvis.li
     * @Date: 2022/1/10
    **/
    SpaQueryTransferStatusRespDTO queryTransferStatus(SpaQueryTransferStatusReqDTO reqDTO);

    /*
     * @MethodName: fundTransfer
     * @Description: 3.3企业单笔资金划转[4004]
     * @Param: [reqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.b2bic.dto.resp.SpaFundTransferRespDTO
     * @Author: Jarvis.li
     * @Date: 2022/1/10
    **/
    SpaFundTransferRespDTO fundTransfer(SpaFundTransferReqDTO reqDTO);

    /*
     * @MethodName: queryTradeDetails
     * @Description: 3.9查询账户当日历史交易明细[4013]
     * @Param: [reqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.b2bic.dto.resp.SpaQueryTradeDetailsRespDTO
     * @Author: Jarvis.li
     * @Date: 2022/1/10
     **/
    SpaQueryTradeDetailsRespDTO queryTradeDetails(SpaQueryTradeDetailsReqDTO reqDTO);

    /*
     * @MethodName: queryReturnRemittanceDetails
     * @Description: 3.11支付退票查询[4019]
     * @Param: [reqDTO]
     * @Return: SpaQueryReturnRemittanceDetailsRespDTO
     * @Author: Jarvis.li
     * @Date: 2022/1/21
    **/
    SpaQueryReturnRemittanceDetailsRespDTO queryReturnRemittanceDetails(SpaQueryReturnRemittanceDetailsReqDTO reqDTO);

    /*
     * @MethodName: queryReceiptDetails
     * @Description: 3.2历史单笔PDF回单查询接口[ELC002]
     * @Param: [reqDTO]
     * @Return: SpaQueryReceiptRespDTO
     * @Author: Jarvis.li
     * @Date: 2022/1/21
    **/
    SpaQueryReceiptRespDTO queryReceiptDetails(SpaQueryReceiptReqDTO reqDTO);

    /*
     * @MethodName: queryHistoryBalance
     * @Description: 3.8历史余额查询[4012]
     * @Param: [reqDTO]
     * @Return: SpaQueryHistoryBalanceRespDTO
     * @Author: Jarvis.li
     * @Date: 2022/1/21
    **/
    SpaQueryHistoryBalanceRespDTO queryHistoryBalance(SpaQueryHistoryBalanceReqDTO reqDTO);

    /**
     * 接口名: 当日历史回单数据查询接口[ELC009]
     * 查询历史回单
     *
     */
    SpaHistoryReceiptQueryRespDTO historyReceiptQuery(SpaHistoryReceiptQueryReqDTO spaHistoryReceiptQueryReqDTO);

    SpaHistoryReceiptGenerateRespDTO historyReceiptGenerate(SpaHistoryReceiptGenerateReqDTO spaHistoryReceiptGenerateReqDTO);

}
