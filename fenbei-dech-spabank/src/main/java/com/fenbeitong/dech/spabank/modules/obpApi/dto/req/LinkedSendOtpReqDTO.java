package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class LinkedSendOtpReqDTO implements Serializable {
    // 是否虚拟卡业务 目前业务报销卡需要上送subAppId， 虚拟卡不需要上送subAppId
    private Boolean isBankCard;
    // 	businessNo	业务请求流水号	string	必须
    private String	businessNo;
    // 	orderNo	开户订单号	string	必须
    private String	orderNo;
    // 	trueName	姓名	string	必须
    private String	trueName;
    // 	idNo	证件号	string	必须
    private String	idNo;
    // 	idType	证件类型	string	必须
    private String	idType;
    // 	bindCardNo	绑定卡号	string	必须
    private String	bindCardNo;
    // 	mobileNo	手机号码	string	必须
    private String	mobileNo;
    // 	subAppId	子商户号	string	必须
    private String	subAppId;

}
