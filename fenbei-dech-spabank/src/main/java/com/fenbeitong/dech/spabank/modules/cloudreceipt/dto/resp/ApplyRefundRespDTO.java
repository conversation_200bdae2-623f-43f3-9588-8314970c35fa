package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @ClassName ApplyRefundRespDTO
 * @Description: 3.7.4 申请退款
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public class ApplyRefundRespDTO extends SpaBaseRespDTO {

    /*
     * 退款订单号	Y 100
     * 商户系统生成的订单号，需要保证在商户系统唯一
     */
    @JsonProperty(value = "ReturnOrderNo")
    private String returnOrderNo;

    /*
     * 银行订单号	Y 50
     * 云收款系统订单号
     */
    @JsonProperty(value = "BankOrderNo")
    private String bankOrderNo;

    /*
     * 通道订单号 Y 32
     * 上游通道返回的订单号
     */
    @JsonProperty(value = "ChannelOrderNo")
    private String channelOrderNo;

    /*
     * 退款订单状态 Y 20
     * 1成功 2处理中 4 失败
     */
    @JsonProperty(value = "ReturnOrderStatus")
    private String returnOrderStatus;

    /*
     * 创建时间	Y 20
     * 创建时间，格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "CreateDate")
    private String createDate;

}
