package com.fenbeitong.dech.spabank.modules.b2bic.client;

import com.fenbeitong.dech.common.until.XmlUtils;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName SpaB2BICClient
 * @Description: 银企直联请求类
 * <AUTHOR>
 * @Date 2022/1/10
 **/
@Component
public class SpaB2BICClient {

    @Value("${spaBank.b2bic.pfxPath}")
    private String b2bicPfxPath;
    @Value("${spaBank.b2bic.pfxPwd}")
    private String b2bicPfxPwd;
    @Value("${spaBank.b2bic.code}")
    private String b2bicCode;

    public <T> T request(String request, String txnCode, Class<T> c) throws FinhubException, Exception {
        FinhubLogger.info("平安银行-银企直联接口请求参数,request={},txnCode={},b2bicCode={},b2bicPfxPath={},b2bicPfxPwd={}", request, txnCode, b2bicCode, b2bicPfxPath, b2bicPfxPwd);
        String signStr = B2BICUtils.sign(request,b2bicPfxPath,b2bicPfxPwd);
        Map map = B2BICUtils.sendTobank(request, txnCode, b2bicCode, b2bicPfxPath, b2bicPfxPwd);
        FinhubLogger.info("平安银行-银企直联接口请求参数,request={},txnCode={},response={}", request, txnCode, JsonUtils.toJson(map));
        Map<String, Map> headMap = (Map<String, Map>) map.get("head");
        String errCode =  headMap.get("errCode") + "";
        String errMsg =  headMap.get("errMsg") + "";
        // 成功
        if(ObjUtils.isNotBlank(errCode) && !"000000".equals(errCode)){
            FinhubLogger.info("平安银行-银企直联接口请求受理失败,request={},errCode={},errMsg={}", request, errCode.trim(), errMsg.trim());
            throw new FinhubException(GlobalExceptionEnum.BANK_SPA_B2BIC_ERROR.getCode(), errCode.trim() + ":" + errMsg.trim());
        }
        
        return (T) XmlUtils.convertXmlStrToObject(c, map.get("body").toString());
    }
}
