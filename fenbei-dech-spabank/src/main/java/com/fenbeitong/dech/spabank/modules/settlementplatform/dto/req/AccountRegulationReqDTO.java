package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp.SpaSettlementPlatBaseRespDTO;
import lombok.Data;

/**
 * @ClassName AccountRegulationReqDTO
 * @Description: KFEJZB6145	调账-见证收单	AccountRegulation
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class AccountRegulationReqDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 收单渠道类型	Y 2
     * "根据不同收单渠道上送
     *  01-橙E收款
     *  YST1-云收款"
     */
    @JsonProperty(value = "AcquiringChannelType")
    private String acquiringChannelType;

    /*
     * 订单号	Y 30
     * 下单时的子订单号，不是总订单号，详见《电商见证宝开发说明V1.0.docx》的2.4章节
     */
    @JsonProperty(value = "OrderNo")
    private String orderNo;

    /*
     * 见证子账户的账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 交易网会员代码	Y 32
     * "交易网会员代码即会员在平台端系统的会员编号
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 见证子账户的名称	Y 32
     */
    @JsonProperty(value = "SubAcctName")
    private String subAcctName;

    /*
     * 金额	Y 32
     */
    @JsonProperty(value = "Amt")
    private String amt;

    /*
     * 币种	Y 3
     * 默认为RMB
     */
    @JsonProperty(value = "Ccy")
    private String ccy = "RMB";

    /*
     * 备注	Y 120
     */
    @JsonProperty(value = "Remark")
    private String remark;

    /*
     * 保留域	N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
