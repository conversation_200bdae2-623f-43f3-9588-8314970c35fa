package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName SpaOrderStatusEnum
 * @Description: 订单状态
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum SpaTranStatusEnum {
    /**
     * （0：成功，1：失败，2：待确认, 5：待处理，6：处理中）
     */
    TRADE_SUCCESSFUL("0","成功"),
    TRADE_FAIL("1","失败"),
    TRADE_CONFIRMED("2","待确认"),
    TRADE_PENDING("5","待处理"),
    TRADE_PROCESSING("6","处理中")
    ;

    SpaTranStatusEnum(String tranStatus, String desc){
        this.tranStatus = tranStatus;
        this.desc = desc;
    }

    private String tranStatus;
    private String desc;

    public String getTranStatus() {
        return tranStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Boolean isSuccess(String status) {
        return StringUtils.isNotBlank(status) && TRADE_SUCCESSFUL.getTranStatus().equals(status) ? true : false;
    }

    public static Boolean isFail(String status) {
        return StringUtils.isNotBlank(status) && TRADE_FAIL.getTranStatus().equals(status) ? true : false;
    }

    public static Boolean isProcessing(String status) {
        if(StringUtils.isNotBlank(status)){
            if(!TRADE_SUCCESSFUL.getTranStatus().equals(status) && !TRADE_FAIL.getTranStatus().equals(status)){
                return true;
            }
        }
        return false;
    }

}
