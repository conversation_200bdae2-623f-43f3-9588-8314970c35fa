package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

/**
 * @ClassName PayCardTypeEnum
 * @Description: 支付卡类型
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum PayCardTypeEnum {
    /**
     * 1：借记卡/储蓄卡2：贷记卡/信用卡
     */
    PAYMENT("1","借记卡/储蓄卡"),
    REFUND("2","贷记卡/信用卡")
    ;

    PayCardTypeEnum(String payCardType, String desc){
        this.payCardType = payCardType;
        this.desc = desc;
    }

    private String payCardType;
    private String desc;

    public String getPayCardType() {
        return payCardType;
    }

    public String getDesc() {
        return desc;
    }
}
