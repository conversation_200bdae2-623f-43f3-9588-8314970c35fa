package com.fenbeitong.dech.spabank.modules.obpApi.constant;

/**
 * @ClassName SpaObpApiMethodNameConstant
 * @Description: 平安银行-商户联营卡-接口名称常量
 * <AUTHOR>
 * @Date 2022/07/28
 **/
public class SpaObpApiMethodNameConstant {

    /**
     * [账户]-开户前置校验职业家庭地址接口
     */
    public static final String CHECK_USER_INFO = "/V1.0/retail/obp-api-ibank-acct.checkUserInfo";

    /**
     * [联动账户]-开户前置校验
     */
    public static final String OPEN_ACCOUNT_PRE = "/V1.0/retail/obp-api-ibank-linkedAcct.openAccountPre";

    /**
     *  /obp-api-ibank-acct.wefileId
     * [联动账户]-获取上传影像的Token接口
     */
    public static final String OBTAIN_FILE_TOKEN = "/V1.0/retail/obp-api-ibank-acct.wefileId";

    /**
     *  /obp-api-ibank-acct.submitImage
     * [联动账户]-影像审核
     */
    public static final String SUBMIT_IMAGE = "/V1.0/retail/obp-api-ibank-acct.submitImage";

    /**
     *  /obp-api-ibank-linkedAcct.linkedSendOtp
     * [联动账户]-联动账户-发送otp
     */
    public static final String LINKED_SEND_OTP = "/V1.0/retail/obp-api-ibank-linkedAcct.linkedSendOtp";

    /**
     *  /obp-api-ibank-linkedAcct.openLinkedAccount
     * [联动账户]-II类户III类户联动开户
     */
    public static final String OPEN_LINKED_ACCOUNT = "/V1.0/retail/obp-api-ibank-linkedAcct.openLinkedAccount";

    /**
     * /obp-api-ibank-linkedAcct.queryOpenAccountResult
     * [联动账户]II类户III类户联动开户结果查询
     */
    public static final String QUERY_OPEN_ACCOUNT_RESULT = "/V1.0/retail/obp-api-ibank-linkedAcct.queryOpenAccountResult";

    /**
     *  /obp-api-ibank-linkedAcct.queryAccountStatus
     * [联动账户]-开户状态查询
     */
    public static final String QUERY_ACCOUNT_STATUS = "/V1.0/retail/obp-api-ibank-linkedAcct.queryAccountStatus";

    /**
     *  /obp-api-ibank.commonSendOtp
     * MAX卡发送OTP
     */
    public static final String COMMON_SEND_OTP = "/V1.0/retail/obp-api-ibank.commonSendOtp";

    /**
     *  /obp-api-ibank-acct.qryBalanceInfoWithoutThirdId
     * 余额查询_不带thirdId
     */
    public static final String QUERY_BALANCE = "/V1.0/retail/obp-api-ibank-acct.qryBalanceInfoWithoutThirdId";

    /**
     *  /obp-api-ibank-linkedAcct.toTransfer
     * 联动II类户提现
     */
    public static final String TO_TRANSFER = "/V1.0/retail/obp-api-ibank-linkedAcct.toTransfer";

    /**
     *  obp-api-ibank-acct.toTransferResultUpgrade
     * 平安二类户提现到绑定的他行一类户结果查询
     */
    public static final String TO_TRANSFER_RESULT = "/V1.0/retail/obp-api-ibank-acct.toTransferResultUpgrade";

    /**
     *  /obp-api-ibank-acct.transferInPreCheck
     *  代扣前置校验
     */
    public static final String TRANSFER_IN_PRE_CHECK = "/V1.0/retail/obp-api-ibank-acct.transferInPreCheck";

    /**
     * obp-api-ibank-acct.transferInApplySignUpgrade
     * 代扣交易签约申请
     */
    public static final String TRANSFER_IN_APPLY_SIGN = "/V1.0/retail/obp-api-ibank-acct.transferInApplySignUpgrade";

    /**
     * obp-api-ibank-acct.transferInApplySignVerify
     * 代扣交易签约验证
     */
    public static final String TRANSFER_IN_APPLY_SIGN_VERIFY = "/V1.0/retail/obp-api-ibank-acct.transferInApplySignVerifyUpgrade";

    /**
     * obp-api-ibank-acct.queryTransferInApplySignResultSmart
     * 代扣交易签约查询，
     */
    public static final String QUERY_TRANSFER_IN_APPLY_SIGN_RESULT_SMART = "/V1.0/retail/obp-api-ibank-acct.queryTransferInApplySignResultSmartUpgrade";

    /**
     * obp-api-ibank-acct.queryBankTransferLimitSmart
     * 代扣银行卡限额查询
     */
    public static final String QUERY_BANK_TRANSFER_LIMIT_SMART = "/V1.0/retail/obp-api-ibank-acct.queryBankTransferLimitSmart";
    /**
     * obp-api-ibank-acct.transferIn
     * 代扣。从绑定卡代扣金额到平安银行二类户卡上。最低代扣2元。代扣限额不够时用户也可以通过绑定卡银行APP或网银转账到平安银行二类户卡号。一般实时返回结果。
     */
    public static final String TRANSFER_IN = "/V1.0/retail/obp-api-ibank-acct.transferInUpgrade";
    /**
     * obp-api-ibank-acct.transferInResultQuery
     * 代扣结果查询
     */
    public static final String TRANSFER_IN_RESULT_QUERY = "/V1.0/retail/obp-api-ibank-acct.transferInResultQueryUpgrade";
    /**
     * obp-api-ibank-acct.qryTransListOutLogin
     * 出入金交易明细
     */
    public static final String QRY_TRANS_LIST_OUT_LOGIN = "/V1.0/retail/obp-api-ibank-acct.qryTransListOutLoginUpgrade";
    /**
     * obp-api-ibank-acct.geth5faceid
     * 获取腾讯H5人脸识别流水号
     */
    public static final String GET_H5_FACE_ID = "/V1.0/retail/obp-api-ibank-acct.geth5faceid";
    /**
     * obp-api-ibank-acct.geth5authresult
     * 获取腾讯h5人脸比对结果
     */
    public static final String GET_H5_AUTH_RESULT = "/V1.0/retail/obp-api-ibank-acct.geth5authresult";
    /**
     * applyAccountRefund
     * 申请划扣二三类户资金至平台监管户
     */
    public static final String APPLY_ACCOUNT_REFUND = "/V1.0/retail/applyAccountRefund";
    /**
     * qryRefundState
     * 查询划扣二三类户资金的流水状态
     */
    public static final String QRY_REFUND_STATE = "/V1.0/retail/qryRefundState";
    /**
     * obp-api-ibank-acct.queryTransactionDetail
     * 查询交易明细接口
     */
    public static final String QUERY_TRANSACTION_DETAIL = "/V1.0/retail/obp-api-ibank-acct.queryTransactionDetail";

    /**
     * obp-api-ibank-acct.queryLinkedAcct
     * 查询用户一类卡信息
     */
    public static final String QUERY_LINKED_ACCT = "/V1.0/retail/obp-api-ibank-acct.queryLinkedAcct";

}
