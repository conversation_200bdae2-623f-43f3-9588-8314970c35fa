package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @description: 申请修改手机号码，用于有需要进行短信动态码验证的平台，申请修改会员手机号码
 * @author: yanqiu.hu
 * @create: 2022-11-16 17:57:46
 * @Version 1.0
 **/
@Data
public class ApplyForChangeOfCellPhoneNumRespDTO extends SpaSettlementPlatBaseRespDTO {
    // 接收手机号码 string(12)  短信码发到该手机上，银行只返回后四位。
    @JsonProperty(value = "ReceiveMobile")
    private String receiveMobile;

    // 短信指令号 string(32)
    @JsonProperty(value = "MessageOrderNo")
    private String messageOrderNo;

    // 保留域 string(20)
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
