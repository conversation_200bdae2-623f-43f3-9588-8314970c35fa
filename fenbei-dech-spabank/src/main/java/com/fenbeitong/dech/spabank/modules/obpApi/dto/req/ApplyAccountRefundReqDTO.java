package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2023-02-20 15:45:55
 * @Version 1.0
 **/
@Data
public class ApplyAccountRefundReqDTO implements Serializable {
    // marketCode String 否 - 市场代码 市场代码和资金汇总账号2选1必送，若2个都上送了必须检查一致性 qydm
    private String marketCode;
    // 资金汇总账号	资金汇总账号 Y 32
    private String fundSummaryAcctNo;
    // memberSubAcctNo String 是 - 会员子账户 划扣的资金回流到的会员子账号 ****************
    private String memberSubAcctNo;
    // secondClassAcctNo String 是 - 二类户账号 被扣划的二三类户账号 ****************
    private String secondClassAcctNo;
    // secondClassAcctName String 是 - 二类户账户户名 被扣划的二三类户的户名 张三
    private String secondClassAcctName;
    // tranAmt String 是 - 交易金额 - 100
    private String tranAmt;
    // remark String 否 - 备注 备注或附言 备注
    private String remark;
    // reservedMsg String 否 - 保留域 - 保留域
    private String reservedMsg;
}
