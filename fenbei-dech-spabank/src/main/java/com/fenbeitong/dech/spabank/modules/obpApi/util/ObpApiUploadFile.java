package com.fenbeitong.dech.spabank.modules.obpApi.util;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.dech.spabank.modules.obpApi.constant.SpaObpApiCommonConstant;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.ObtainFileTokenRespDTO;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.OpenUploadFileRespDTO;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.SpaBankObpApiBaseRespDTO;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.SpaBankObpApiRespDTO;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
public class ObpApiUploadFile {


    public static String uploadFile(ObtainFileTokenRespDTO obtainFileTokenRespDTO, String imageUrl, String imageName) {
        Map<String, String> map = new HashMap<>();
        map.put("project", obtainFileTokenRespDTO.getProject());
        map.put("charset","UTF-8");
        map.put("expiredTime", obtainFileTokenRespDTO.getExpiredTime());
        map.put("uuid", obtainFileTokenRespDTO.getUuid());
        map.put("weFileToken", obtainFileTokenRespDTO.getWefileToken());
        String result = upload(obtainFileTokenRespDTO.getWefileUrl(), imageUrl, imageName, map);
        return result;
    }

    public static String  upload(String url, String fileUrl, String fileName, Map<String, String> param) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            httpClient = HttpClients.createDefault();

            // 把一个普通参数和文件上传给下面这个地址 是一个servlet
            HttpPost httpPost = new HttpPost(url);

            InputStream intstream = new URL(fileUrl).openStream();

            // 相当于<input type="file" name="file"/>
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.addBinaryBody("file", intstream, ContentType.DEFAULT_BINARY, fileName);
            for (Map.Entry<String, String> entry : param.entrySet()) {
                String key = entry.getKey();
                // 相当于<input type="text" name="userName" value=userName>
                StringBody value = new StringBody(entry.getValue(), ContentType.create("text/plain", Consts.UTF_8));
                builder.addPart(key, value);
            }
            HttpEntity reqEntity = builder.build();

            httpPost.setEntity(reqEntity);

            // 发起请求 并返回请求的响应
            response = httpClient.execute(httpPost);

            FinhubLogger.info("The response value of token:" + response.getFirstHeader("token"));

            // 获取响应对象
            HttpEntity resEntity = response.getEntity();
            String result = "";
            if (resEntity != null) {
                // 打印响应长度
                FinhubLogger.info("Response content length: " + resEntity.getContentLength());
                // 打印响应内容
                result = EntityUtils.toString(resEntity, Charset.forName("UTF-8"));
            }
            // 销毁
            EntityUtils.consume(resEntity);
            if (ObjUtils.isNull(result)){
                FinhubLogger.warn("平安银行-平安商户联营卡上传文件接口返回为NULL,url={},fileUrl={}", url, fileUrl);
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_UPLOAD_ERROR);
            }
            SpaBankObpApiBaseRespDTO baseRespDTO = JsonUtils.toObj(result, SpaBankObpApiBaseRespDTO.class);
            if (!SpaObpApiCommonConstant.isSuccess(baseRespDTO.getResponseCode())){
                FinhubLogger.info("平安银行-平安商户联营卡上传文件接口返回失败,url={},fileUrl={}, error={}", url, fileUrl, baseRespDTO.getResponseCode() + "-" + baseRespDTO.getResponseMsg());
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_UPLOAD_ERROR);
            }
            Map<String, Object> resultMap = JsonUtils.toObj(result, Map.class);
            if (ObjUtils.isNull(resultMap.get("data"))){
                FinhubLogger.warn("平安银行-平安商户联营卡上传文件接口返回为NULL,url={},fileUrl={}", url, fileUrl);
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_UPLOAD_ERROR);
            }
            OpenUploadFileRespDTO uploadFileRespDTO = JsonUtils.toObj(JsonUtils.toJson(resultMap.get("data")), OpenUploadFileRespDTO.class);
            if (ObjUtils.isNull(uploadFileRespDTO) || ObjUtils.isBlank(uploadFileRespDTO.getFileId())){
                FinhubLogger.info("平安银行-平安商户联营卡上传文件接口返回data为空,url={},fileUrl={}", url, fileUrl);
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_UPLOAD_ERROR);
            }
            return uploadFileRespDTO.getFileId();
        } catch (Exception e) {
            FinhubLogger.info("出错啦...."+e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

}
