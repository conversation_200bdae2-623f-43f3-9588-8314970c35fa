package com.fenbeitong.dech.spabank.modules.settlementplatform.service;

import com.fenbeitong.dech.spabank.modules.obpApi.dto.req.BindLinkedAccountReqDTO;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.BindLinkedAccountRespDTO;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req.*;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp.*;

/**
 * @ClassName SpaSettlementPlatMainService
 * @Description: 结算通基础service
 * <AUTHOR>
 * @Date 2021/10/15
 **/
public interface SpaSettlementPlatMainService {

    /**
     * KFEJZB6248 实名开户 AutonymOpenCustAcctId
     */
    AutonymOpenCustAcctIdRespDTO autonymOpenCustAcctId(AutonymOpenCustAcctIdReqDTO autonymOpenCustAcctIdReqDTO);

    /**
     * KFEJZB6048 查询银行提现退单信息	BankWithdrawCashBackQuery
     */
    BankWithdrawCashBackQueryRespDTO bankWithdrawCashBackQuery(BankWithdrawCashBackQueryReqDTO bankWithdrawCashBackQueryReqDTO);

    /**
     * KFEJZB6240 会员绑定提现账户小额鉴权-校验法人 BindSmallAmountWithCheckCorp
     */
    BindSmallAmountWithCheckCorpRespDTO bindSmallAmountWithCheckCorp(BindSmallAmountWithCheckCorpReqDTO bindSmallAmountWithCheckCorpReqDTO);

    /**
     * KFEJZB6296 企业用户支持修改会员名称、公司名称、法人名称、法人证件类型、法人证件号码,个人用户只支持修改会员名称；
	 * 接口暂不支持个体工商户若企业同名户为个体工商户,不解绑提现账户,不更新同名户信息,若个人同名户为个体工商户,解绑提现账户,不更新同名户信息	
     * @param request
     * @return
     */
    MemberInformationChangeRespDTO updateCompanyInfor(MemberInformationChangeRequestDTO request);
    
    /**
     * KFEJZB6241 小额鉴权回填金额-校验法人	CheckAmountWithCorp
     */
    CheckAmountWithCorpRespDTO checkAmountWithCorp(CheckAmountWithCorpReqDTO checkAmountWithCorpReqDTO);

    /**
     * KFEJZB6142 查询明细单验证码	DetailVerifiedCodeQuery
     */
    DetailVerifiedCodeQueryRespDTO detailVerifiedCodeQuery(DetailVerifiedCodeQueryReqDTO detailVerifiedCodeQueryReqDTO);

    /**
     * KFEJZB6007	会员资金冻结-不验证	MembershipTrancheFreeze
     */
//    MembershipTrancheFreezeRespDTO membershipTrancheFreeze(MembershipTrancheFreezeReqDTO membershipTrancheFreezeReqDTO);

    /**
     * KFEJZB6163	会员资金支付-不验证	MembershipTranchePay
     */
//    MembershipTranchePayRespDTO membershipTranchePay(MembershipTranchePayReqDTO membershipTranchePayReqDTO);

    /**
     * KFEJZB6033	会员提现-不验证	MembershipWithdrawCash
     */
    MembershipWithdrawCashRespDTO membershipWithdrawCash(MembershipWithdrawCashReqDTO membershipWithdrawCashReqDTO);

    /**
     * KFEJZB6164	会员间交易退款-不验证	MemberTransactionRefund
     * @return
     */
    MemberTransactionRefundRespDTO memberTransactionRefund(MemberTransactionRefundReqDTO memberTransactionRefundReqDTO);

    /**
     * KFEJZB6034	会员间交易-不验证	MemberTransaction
     */
    MemberTransactionRespDTO memberTransaction(MemberTransactionReqDTO memberTransactionReqDTO);

    /**
     * KFEJZB6037 查询会员子账号 QueryCustAcctId
     */
    QueryCustAcctIdRespDTO queryCustAcctId(QueryCustAcctIdReqDTO queryCustAcctIdReqDTO);

    /**
     * KFEJZB6103	查询对账文件信息	ReconciliationDocumentQuery
     */
    ReconciliationDocumentQueryRespDTO reconciliationDocumentQuery(ReconciliationDocumentQueryReqDTO reconciliationDocumentQueryReqDTO);

    /**
     * KFEJZB6110	查询银行单笔交易状态	SingleTransactionStatusQuery
     */
    SingleTransactionStatusQueryRespDTO singleTransactionStatusQuery(SingleTransactionStatusQueryReqDTO singleTransactionStatusQueryQueryReqDTO);

    /**
     * KFEJZB6011	查询资金汇总账户余额	SupAcctIdBalanceQuery
     */
    SupAcctIdBalanceQueryRespDTO supAcctIdBalanceQuery(SupAcctIdBalanceQueryReqDTO supAcctIdBalanceQueryReqDTO);

    /**
     * KFEJZB6065	会员解绑提现账户	UnbindRelateAcct
     */
    UnbindRelateAcctRespDTO unbindRelateAcct(UnbindRelateAcctReqDTO unbindRelateAcctReqDTO);

    /**
     * KFEJZB6138	维护会员绑定提现账户联行号	MntMbrBindRelateAcctBankCode
     */
    MntMbrBindRelateAcctBankCodeRespDTO mntMbrBindRelateAcctBankCode(MntMbrBindRelateAcctBankCodeReqDTO mntMbrBindRelateAcctBankCodeReqDTO);

    /**
     * KFEJZB6098	会员绑定信息查询	MemberBindQuery
     */
    MemberBindQueryRespDTO memberBindQuery(MemberBindQueryReqDTO memberBindQueryReqDTO);

    /**
     * KFEJZB6244	登记行为记录信息	RegisterBehaviorRecordInfo
     */
    RegisterBehaviorRecordInfoRespDTO registerBehaviorRecordInfo(RegisterBehaviorRecordInfoReqDTO registerBehaviorRecordInfoReqDTO);

    /**
     * KFEJZB6139	登记挂账(支持撤销)	RegisterBillSupportWithdraw
     */
    RegisterBillRespDTO registerBillSupportWithdraw(RegisterBillReqDTO registerBillReqDTO);

    /**
     * KFEJZB6140	登记挂账撤销	RevRegisterBillSupportWithdraw
     */
    RevRegisterBillRespDTO revRegisterBillSupportWithdraw(RevRegisterBillReqDTO revRegisterBillReqDTO);

    /**
     * KFEJZB6061	查询小额鉴权转账结果	SmallAmountTransferQuery
     */
    SmallAmountTransferQueryRespDTO smallAmountTransferQuery(SmallAmountTransferQueryReqDTO smallAmountTransferQueryReqDTO);

    /**
     * KFEJZB6146	查询充值明细-见证收单	ChargeDetailQuery
     */
    ChargeDetailQueryRespDTO chargeDetailQuery(ChargeDetailQueryReqDTO chargeDetailQueryReqDTO);

    /**
     * KFEJZB6145	调账-见证收单	AccountRegulation
     */
    AccountRegulationRespDTO accountRegulation(AccountRegulationReqDTO accountRegulationReqDTO);

    /**
     * KFEJZB6147	平台补账-见证收单	PlatformAccountSupply
     */
    PlatformAccountSupplyRespDTO platformAccountSupply(PlatformAccountSupplyReqDTO platformAccountSupplyReqDTO);

    /**
     * KFEJZB6010	查询银行子账户余额	CustAcctIdBalanceQuery
     */
    CustAcctIdBalanceQueryRespDTO custAcctIdBalanceQuery(CustAcctIdBalanceQueryReqDTO custAcctIdBalanceQueryReqDTO);

    /**
     * KFEJZB6072	查询银行时间段内交易明细	BankTransactionDetailsQuery
     */
    BankTransDetailsQueryRespDTO bankTransactionDetailsQuery(BankTransDetailsQueryReqDTO bankTransDetailsQueryReqDTO);

    /**
     * KFEJZB6072	查询银行时间段内交易明细	BankTransactionDetailsQuery
     */
    CommonTransRechargeQueryRespDTO commonTransferRechargeQuery(CommonTransRechargeQueryReqDTO commonTransRechargeQueryReqDTO);

    /**
     * KFEJZB6238	会员绑定提现账户银联鉴权-校验法人	BindUnionPayWithCheckCorp
     */
    BindUnionPayWithCheckCorpRespDTO bindUnionPayWithCheckCorp(BindUnionPayWithCheckCorpReqDTO bindUnionPayWithCheckCorpReqDTO);

    /**
     * KFEJZB6239	银联鉴权回填短信码-校验法人	CheckMsgCodeWithCorp
     */
    CheckMsgCodeWithCorpRespDTO checkMsgCodeWithCorp(CheckMsgCodeWithCorpReqDTO checkMsgCodeWithCorpReqDTO);

    /**
     *  KFEJZB6294 会员绑定提现账户-二类户 BindTwoLevelOfAccount
     */
    BindLinkedAccountRespDTO bindLinkedAccount(BindLinkedAccountReqDTO bindLinkedAccountReqDTO);

    /**
     * KFEJZB6083	申请修改手机号码	ApplyForChangeOfCellPhoneNum	用于有需要进行短信动态码验证的平台，申请修改会员手机号码。
     */
    ApplyForChangeOfCellPhoneNumRespDTO applyForChangeOfCellPhoneNum(ApplyForChangeOfCellPhoneNumReqDTO applyForChangeOfCellPhoneNumReqDTO);

    /**
     *  KFEJZB6084	回填动态码-修改手机	BackfillDynamicPassword	用于申请修改手机号码的会员，回填手机动态码发送至银行。
     */
    BackFillDynamicPasswordRespDTO backFillDynamicPassword(BackFillDynamicPasswordReqDTO backFillDynamicPasswordReqDTO);



    /**
     * KFEJZB6324 </br>
     * 见证子台帐信息查询 </br>
     * EJZBCustInformationQuery </br>
     * 见证子台账信息查询接口，支持产业结算通平台调用，查询子台账开立和补录的信息。
     * 备注: KFEJZB6244接口的签约结果可以通过此接口查询
     */
    EJZBCustInfomationQueryRespDTO eJZBCustInformationQuery(EJZBCustInfomationQueryReqDTO ejzbCustInfomationQueryReqDTO);
}
