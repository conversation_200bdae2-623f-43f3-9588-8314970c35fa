package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @ClassName SpaQueryReturnRemittanceDetailsRespDTO
 * @Description: 3.11支付退票查询[4019]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryReturnRemittanceDetailsRespDTO extends SpaB2BICBaseRespDTO{

    /**
     * 		符合查询条件的笔数	9(10)	Y
     * 		符合当前查询条件的笔数
     */
    private String TotalCts;

    /**
     * 		记录结束标志	C(1)	Y
     * 	Y:无剩余记录
     * 	N:有剩余记录
     */
    private String IsEnd;

    /**
     * 		当前页码	C(10)	Y
     * 	同上送
     */
    private String PageNo;

    /**
     * 		每页记录条数	C(5)	Y
     * 		同上送
     */
    private String PageCts;

    private List<SpaQueryReturnRemittanceDetails> list;

}
