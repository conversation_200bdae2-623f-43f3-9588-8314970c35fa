package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req.SpaSettlementPlatBaseReqDTO;
import lombok.Data;

/**
 * @ClassName SupAcctIdBalanceQueryReqDTO
 * @Description: KFEJZB6011	查询资金汇总账户余额	SupAcctIdBalanceQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SupAcctIdBalanceQueryRespDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 上日余额  15
     * 前一天的余额
     */
    @JsonProperty(value = "LastBalance")
    private String lastBalance;

    /*
     * 当前余额  15
     * 当前余额
     */
    @JsonProperty(value = "CurBalabce")
    private String curBalabce;

    /*
     * 账户余额  15
     * 账户余额 210000.56
     */
    @JsonProperty(value = "Balance")
    private String balance;

    /*
     * 增值余额  15
     * 当前余额	210000.56
     */
    @JsonProperty(value = "AddedBalance")
    private String addedBalance;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
