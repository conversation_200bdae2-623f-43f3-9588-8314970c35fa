package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName QuerySingleOrderRespDTO
 * @Description: 3.11查询单笔订单
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Data
public class QuerySingleOrderRespDTO extends SpaBaseRespDTO {

    /*
     * 商户编号	Y 32
     * 商户在云收款系统的编号
     */
    @JsonProperty(value = "TraderNo")
    private String traderNo;

    /*
     * 商户订单号	Y 100
     * 商户系统生成的订单号
     */
    @JsonProperty(value = "TraderOrderNo")
    private String traderOrderNo;

    /*
     * 银行订单号	Y 50
     * 云收款系统订单号
     */
    @JsonProperty(value = "BankOrderNo")
    private String bankOrderNo;

    /*
     * 支付方式编号	Y 50
     * 云收款支付方式编号
     */
    @JsonProperty(value = "PayModeNo")
    private String payModeNo;

    /*
     * 交易金额	Y 20
     * 订单金额（单位：分）
     */
    @JsonProperty(value = "TranAmt")
    private String tranAmt;

    /*
     * 订单类型	Y 3
     * 1支付 2退款 3撤销
     * SpaOrderTypeEnum
     */
    @JsonProperty(value = "OrderType")
    private String orderType;

    /*
     * 订单状态	Y 20
     * 0 已受理;1 交易成功 ;2 交易中; 3 用户支付中;  4 交易关闭; 9 已撤销
     * SpaOrderStatusEnum
     */
    @JsonProperty(value = "OrderStatus")
    private String orderStatus;

    /*
     * 订单发送时间	Y 20
     * 商户系统订单生成时间 格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "OrderSendTime")
    private String orderSendTime;


    /*
     * 支付成功时间	N 20
     * 云收款系统订单支付成功时间，格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "PaySuccessTime")
    private String paySuccessTime;

    /*
     * 通道订单号	N 32
     * 上游通道返回的订单号
     */
    @JsonProperty(value = "ChannelOrderNo")
    private String channelOrderNo;

    /*
     * 付款方信息	N 100
     * 支付账户信息
     */
    @JsonProperty(value = "PayerInfo")
    private String payerInfo;

    /*
     * 支付卡类型	N 20
     * 1：借记卡/储蓄卡2：贷记卡/信用卡
     * PayCardTypeEnum
     */
    @JsonProperty(value = "PayCardType")
    private String payCardType;

    /*
     * 二维码	N 300
     * 商户被扫二维码
     */
    @JsonProperty(value = "DimensionalCode")
    private String dimensionalCode;

    /*
     * 清算属性	N 20
     * 订单支付成功时返回订单清算属性的值
     */
    @JsonProperty(value = "SettleProperty")
    private String settleProperty;
}
