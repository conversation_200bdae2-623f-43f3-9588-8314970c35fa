package com.fenbeitong.dech.spabank.modules.cloudreceipt.client;

import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.dech.spabank.utils.SpaDateUtil;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.pingan.openbank.api.sdk.OpenBankApiClient;
import com.pingan.openbank.api.sdk.config.OpenBankConfig;

import cn.hutool.core.util.CharsetUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName CloudReceiptClient
 * @Description: 云收款请求类
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Component
public class CloudReceiptClient {

    //单位毫秒
    private static final int TIMEOUT = 20000;

    @Value("${spaBank.cloudReceipt.businessId}")
    private String businessId;

    @Value("${spaBank.settlementPlat.appId}")
    private String settlementPlatAppId;
    private static Map<String, String> headers = new HashMap<>();
    private static Map<String, String> formHeaders = new HashMap<>();
    static {
        headers.put(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
        formHeaders.put(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.toString());
    }

    public String request(Map<String,String> reqParam, String methodName)  {
        //必填 系统流水号（CnsmrSeqNo）规范：6位uid(文件传输用户短号)+6位系统日期(YYMMDD)+10位随机数
        reqParam.put("CnsmrSeqNo", SpaDateUtil.randomCnsmrSeqNo());
        //必填 商户编号
        reqParam.put("TraderNo", businessId);
        FinhubLogger.info("平安银行-云收款接口请求参数,url={},request={}", methodName, reqParam);
        String response = null;
        long end = 0L;
        long start = 0L;
        try {
            start = System.currentTimeMillis();
            response = OpenBankApiClient.post(settlementPlatAppId, methodName,reqParam,null);
            end = System.currentTimeMillis();
            FinhubLogger.info("平安银行-云收款接口返回,url={},耗时{}ms,request={},response={}", methodName, end-start, JsonUtils.toJson(reqParam), response);
            if (StringUtils.isBlank(response)){
                FinhubLogger.info("平安银行-云收款接口返回为NULL,url={},耗时{}ms,request={},response={}", methodName, end-start, JsonUtils.toJson(reqParam), response);
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_CLOUD_RECEIPT_RESPONSE_NULL);
            }
            return response;
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【平安银行-云收款接口请求异常】【耗时{}ms】【url={}】【request={}】,exception=",end-start, methodName, JsonUtils.toJson(reqParam), e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_CLOUD_RECEIPT_EXCEPTION);
        }
    }

    public String requestForm(String request, String methodName)  {
        OpenBankConfig openBankConfig = OpenBankApiClient.getOpenBankConfig(settlementPlatAppId);
        String url =  openBankConfig.getBaseUrl() + methodName;
        FinhubLogger.info("平安银行-云收款接口请求参数,url={},request={}", url, request);
        String response = null;
        long end = 0L;
        long start = 0L;
        try {
            start = System.currentTimeMillis();
            response = HttpClientUtils.postBody(url, request, TIMEOUT, CharsetUtil.UTF_8, formHeaders);
            end = System.currentTimeMillis();
            FinhubLogger.info("平安银行-云收款接口返回,url={},耗时{}ms,request={},response={}", url, end - start, request, response);
            if (StringUtils.isBlank(response)) {
                FinhubLogger.info("平安银行-云收款接口返回为NULL,url={},耗时{}ms,request={},response={}", url, end - start, request, response);
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_CLOUD_RECEIPT_RESPONSE_NULL);
            }
            return response;
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【平安银行-云收款接口请求异常】【耗时{}ms】【url={}】【request={}】,exception=", end - start, url, request, e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_CLOUD_RECEIPT_EXCEPTION);
        }
    }
}
