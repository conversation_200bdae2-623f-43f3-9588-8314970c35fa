package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryReturnRemittanceDetailsRespDTO
 * @Description: 3.11支付退票查询[4019]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaHistoryReceiptGenerateDetail implements Serializable {
    /**
     * 账号,必输
     */
    private String SeqNo;
    /**
     * 	记账日期	C(30)	必输
     * 	取ELC009接口应答的list.AccountDate
     */
    private String AccountDate;
    /**
     * 回单类型	C(13,2)	必输
     * 取ELC009接口应答的list. ReceiptType
     */
    private String RecepitType;

}
