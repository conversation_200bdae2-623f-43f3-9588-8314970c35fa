package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

@Data
public class QueryOpenLinkAccountResultRespDTO extends SpaBankObpApiBaseRespDTO {
    // 异常信息	非必须		联动开户失败异常信息
    private String failMsg;
    // 联动关系	非必须		0-未建立,1-已建立,2-建立失败	状态为1时业务完全成功，并且下方数据均有返回
    private String linkedStatus;
    // 二类户协议号	非必须
    private String secondAgreementNo;
    // 二类户卡号	非必须
    private String secondCardNo;
    // 二类户账户来源	非必须		-1:存量账户,0:新开账户
    private String secondSource;
    // 三类户协议号	非必须
    private String thirdAgreementNo;
    // 三类户卡号	非必须
    private String thirdCardNo;
    // 三类户账户来源	非必须		-1:存量账户,0:新开账户
    private String thirdSource;
    // 代扣状态	非必须		-2:处理中,-1:失败,0:成功,1:无需代扣
    private String transferInStatus;

}
