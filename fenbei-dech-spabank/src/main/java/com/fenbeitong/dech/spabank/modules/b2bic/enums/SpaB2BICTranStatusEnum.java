package com.fenbeitong.dech.spabank.modules.b2bic.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName SpaB2BICTranStatusEnum
 * @Description: 订单状态
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum SpaB2BICTranStatusEnum {
    /**
     * 20：成功
     * 30：失败
     * 其他为银行受理成功处理中
     */
    TRADE_SUCCESSFUL("20","成功"),
    TRADE_FAIL("30","失败")
    ;

    SpaB2BICTranStatusEnum(String tranStatus, String desc){
        this.tranStatus = tranStatus;
        this.desc = desc;
    }

    private String tranStatus;
    private String desc;

    public String getTranStatus() {
        return tranStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Boolean isSuccess(String status) {
        return StringUtils.isNotBlank(status) && TRADE_SUCCESSFUL.getTranStatus().equals(status) ? true : false;
    }

    public static Boolean isFail(String status) {
        return StringUtils.isNotBlank(status) && TRADE_FAIL.getTranStatus().equals(status) ? true : false;
    }

    public static Boolean isProcessing(String status) {
        if(StringUtils.isNotBlank(status)){
            if(!TRADE_SUCCESSFUL.getTranStatus().equals(status) && !TRADE_FAIL.getTranStatus().equals(status)){
                return true;
            }
        }
        return false;
    }

}
