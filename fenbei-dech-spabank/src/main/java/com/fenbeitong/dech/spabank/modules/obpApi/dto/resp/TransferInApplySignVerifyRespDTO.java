package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:50:37
 * @Version 1.0
 **/
@Data
public class TransferInApplySignVerifyRespDTO extends SpaBankObpApiBaseRespDTO {
    // signStatus String  协议状态  协议状态：0-生效 1-失效 2-待签约 3-签约失败 4-待解约 5-待确认6-签约已确认待查询7-解约已确认待查询
    private String signStatus;
    // channelCode String  通道代码
    private String channelCode;
}
