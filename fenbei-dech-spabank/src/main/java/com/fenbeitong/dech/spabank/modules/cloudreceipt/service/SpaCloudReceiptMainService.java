package com.fenbeitong.dech.spabank.modules.cloudreceipt.service;

import com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req.*;
import com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp.*;

/**
 * @ClassName CloudReceiptMainService
 * @Description: 云收款基础service
 * <AUTHOR>
 * @Date 2021/10/15
 **/
public interface SpaCloudReceiptMainService {

    /*
     * @MethodName: tradeEBankPay
     * @Description: 3.7.2 企业网银(B2B)支付
     * @Param: [tradeEBankPayReqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp.TradeEBankPayRespDTO
     * @Author: Jarvis.li
     * @Date: 2021/10/15
    **/
    TradeEBankPayRespDTO tradeEBankPay(TradeEBankPayReqDTO tradeEBankPayReqDTO);

    /*
     * @MethodName: querySingleOrder
     * @Description: 3.7.3 查询单笔订单
     * @Param: [querySingleOrderReqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp.QuerySingleOrderRespDTO
     * @Author: Jarvis.li
     * @Date: 2021/10/15
    **/
    QuerySingleOrderRespDTO querySingleOrder(QuerySingleOrderReqDTO querySingleOrderReqDTO);

    /*
     * @MethodName: applyRefund
     * @Description: 3.7.4 申请退款
     * @Param: [applyRefundReqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp.ApplyRefundRespDTO
     * @Author: Jarvis.li
     * @Date: 2021/10/15
    **/
    ApplyRefundRespDTO applyRefund(ApplyRefundReqDTO applyRefundReqDTO);

    /*
     * @MethodName: QuerySingleRefund
     * @Description: 3.7.5 查询单笔退款
     * @Param: [querySingleRefundReqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp.QuerySingleRefundRespDTO
     * @Author: Jarvis.li
     * @Date: 2021/10/15
    **/
    QuerySingleRefundRespDTO querySingleRefund(QuerySingleRefundReqDTO querySingleRefundReqDTO);

    /*
     * @MethodName: downloadReconciliationFile
     * @Description: 3.14 下载对账文件
     * @Param: [downloadReconciliationFileReqDTO]
     * @Return: com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp.DownloadReconciliationFileRespDTO
     * @Author: Jarvis.li
     * @Date: 2021/10/15
    **/
    DownloadReconciliationFileRespDTO downloadReconciliationFile(DownloadReconciliationFileReqDTO downloadReconciliationFileReqDTO);
}
