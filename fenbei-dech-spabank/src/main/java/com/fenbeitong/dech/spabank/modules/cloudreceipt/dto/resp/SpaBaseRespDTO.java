package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/14
 * @Description
 */
@Data
public class SpaBaseRespDTO implements Serializable {

    /**
     * 返回码
     */
    @JsonProperty(value = "TxnReturnCode")
    private String txnReturnCode;

    /**
     * 返回信息
     */
    @JsonProperty(value = "TxnReturnMsg")
    private String txnReturnMsg;
}
