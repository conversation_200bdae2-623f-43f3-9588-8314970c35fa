package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

/**
 * @ClassName CloudReceiptRespEnum
 * @Description: 云收款接口状态枚举
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum CloudReceiptRespEnum {

    T0000("T0000","接口调用成功，调用结果请参考返回参数中的状态值",CloudReceiptStatusEnum.SUCCEEDED),
    T5409("T5409","商户不存在",CloudReceiptStatusEnum.FAILED),
    // 订单不存在,需要稍后发起交易状态查询，10分钟后查询还是返回T6510视为原交易失败
    T6510("T6510","原支付订单不存在",CloudReceiptStatusEnum.PROCESSING),
    T6522("T6522","此订单为非退款类型订单,请调用查询单笔订单接口",CloudReceiptStatusEnum.FAILED),
    T6523("T6523","查询失败，此订单为非支付类型订单",CloudReceiptStatusEnum.FAILED),
    T6525("T6525","参数为空：[中文名称]为空",CloudReceiptStatusEnum.FAILED),
    T6526("T6526","参数长度超过限制：[中文名称]长度超过限制",CloudReceiptStatusEnum.FAILED),
    T6527("T6527","参数不合法：[具体提示]",CloudReceiptStatusEnum.FAILED),
    T5410("T5410","商户已停用",CloudReceiptStatusEnum.FAILED),
    T5411("T5411","商户上级机构已停用",CloudReceiptStatusEnum.FAILED),
    T5416("T5416","收银员账号不存在",CloudReceiptStatusEnum.FAILED),
    T6509("T6509","商户订单号已存在",CloudReceiptStatusEnum.FAILED),
    T6513("T6513","接口调用失败，渠道端错误码： 渠道端错误信息：",CloudReceiptStatusEnum.FAILED),
    T6514("T6514","可退款余额不足",CloudReceiptStatusEnum.FAILED),
    T6518("T6518","交易失败，请重新更换商户订单号重发",CloudReceiptStatusEnum.FAILED),
    T6520("T6520","原支付订单未支付成功",CloudReceiptStatusEnum.FAILED),
    T5422("T5422","智能存管通商户不支持部分退款",CloudReceiptStatusEnum.FAILED),
    T6528("T6528","当前退款金额大于商户今日可退款金额",CloudReceiptStatusEnum.FAILED),
    T6532("T6532","代扣交易不支持退款",CloudReceiptStatusEnum.FAILED),
    T6534("T6534","退款订单号已存在",CloudReceiptStatusEnum.FAILED),
    T9001("T9001","渠道端返回错误，渠道端错误码： 渠道端错误信息：",CloudReceiptStatusEnum.PROCESSING),
    T9100("T9100","交易通讯超时，请发起查询交易",CloudReceiptStatusEnum.PROCESSING),
    T9999("T9999","系统异常,请联系业务人员核查",CloudReceiptStatusEnum.PROCESSING),

    ;


    CloudReceiptRespEnum(String status, String desc, CloudReceiptStatusEnum cloudReceiptStatusEnum){
        this.status = status;
        this.desc = desc;
        this.cloudReceiptStatusEnum = cloudReceiptStatusEnum;
    }

    private String status;
    private String desc;
    private CloudReceiptStatusEnum cloudReceiptStatusEnum;

    public String getPayCardType() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public CloudReceiptStatusEnum getCloudReceiptStatusEnum() {
        return cloudReceiptStatusEnum;
    }
}
