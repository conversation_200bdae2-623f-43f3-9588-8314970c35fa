package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MemberTransactionRespDTO
 * @Description: KFEJZB6034	会员间交易-不验证	MemberTransaction
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MemberTransactionRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 见证系统流水号	Y 16
     * 即电商见证宝系统生成的流水号，可关联具体一笔请求
     */
    @JsonProperty(value = "FrontSeqNo")
    private String frontSeqNo;

    /*
     * 保留域	Y 4
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
