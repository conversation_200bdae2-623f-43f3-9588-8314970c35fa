package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SpaQueryReturnRemittanceDetailsRespDTO
 * @Description: 3.11支付退票查询[4019]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaHistoryReceiptGenerateReqDTO implements Serializable {
    /**
     * 账号,必输
     */
    private String OutAccNo;

    /**
     * 	记账起始日期，非必输
     * 	查询当日无需输入此字段
     *  查询历史回单必输(开始结束都需要输入，必须是历史日期)
     *  格式YYYYMMDD
     */
    private String AccountBeginDate;

    /**
     * 	记账结束日期,非必输
     * 	查询当日无需输入此字段
     *  查询历史回单必输(开始结束都需要输入，必须是历史日期)
     *  格式YYYYMMDD
     */
    private String AccountEndDate;
    /**
     * 生成PDF每页条数设置,非必输
     * N：一页pdf显示一条回单记录（默认）
     * Y：一页pdf显示四条回单记录
     * T：一页pdf显示三条回单记录
     */
    private String PDFNum;

    private List<SpaHistoryReceiptGenerateDetail> List;

}
