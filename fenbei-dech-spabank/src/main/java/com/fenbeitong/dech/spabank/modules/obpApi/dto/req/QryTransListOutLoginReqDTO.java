package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 出入金交易明细
 * @author: yanqiu.hu
 * @create: 2022-10-11 10:54:56
 * @Version 1.0
 **/
@Data
public class QryTransListOutLoginReqDTO implements Serializable {

    // tradeNoId String 是 订单号 必输。全局唯一，不能重复。每次请求时重新生成 tradeNoId_1597050505514
    private String tradeNoId;
    // bankType String 是 查询类型 0表示本行，1表示他行，默认填0 0
    private String bankType;
    // currType String 是 币种 如：RMB（人民币） RMB
    private String currType;
    // oldChannelSeqNo String 否 查询指令号 他行查询指令号，本行不传，他行必传 pn7852351
    private String oldChannelSeqNo;
    // pageIndex String 是 页码 分页参数，默认从第1页开始 1
    private String pageIndex;
    // pageSize String 是 页数 分页参数，默认每页显示10条 10
    private String pageSize;
    // startDate String 是 开始日期 格式yyyyMMdd，******** ********
    private String startDate;
    // endDate String 是 结束日期 格式yyyyMMdd，********。开始日期和结束日期不能超过365天 ********
    private String endDate;
    // requestTime String 是 请求时间 必输，发起请求时的服务器时间，格式：yyyy-MM-dd HH:mm:ss服务提供方安全校验使用 2020-09-30 11:30:27
    private String requestTime;
    // qryAddCardFlag String 否 查询加挂卡标识 非必输, 0：只查询加挂卡余额 1：查询客户所有卡余额（包含加挂与非加挂卡）默认为0 0
    private String qryAddCardFlag;
    // agreementNo String 是 协议号 联动二三开户/开户结果查询/账户信息查询会返回协议号 81ae29bfbf3543da8249d8e1624d6c5f
    private String agreementNo;

}
