package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:52:09
 * @Version 1.0
 **/
@Data
public class TransferInResultQueryReqDTO implements Serializable {
    // businessNo	String	是	32	业务请求流水号	跟入金（代扣）接口保持一致，	OB8870a9462e00202101088069009884
    private String businessNo;
    // agreementNo String	是	-	协议号	账户开户/开户结果查询/账户信息查询/授权/授权查询返回的协议号	OB8870a9462e00202101088069009884
    private String agreementNo;
}
