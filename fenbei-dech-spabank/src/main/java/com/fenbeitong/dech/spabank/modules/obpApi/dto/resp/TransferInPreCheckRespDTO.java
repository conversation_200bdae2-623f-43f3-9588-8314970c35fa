package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-10 20:03:07
 * @Version 1.0
 **/
@Data
public class TransferInPreCheckRespDTO extends SpaBankObpApiBaseRespDTO {
    // checkResult String  代扣前置检查结果  0：不支持代扣 1：支持代扣 2：优先级高的代扣通道有条件支持,存在无条件支持代扣的通道3：优先级高的代扣通道无条件支持代扣，其他代扣通道有条件的支持代扣 4：所有的代扣通道都是有条件支持的，如果不满足条件将无法代扣
    private String checkResult;
    // firstTransferInChannel String  代扣通道  检查结果为：1、2、3，则有值，则默认：8，表示走的BIB智能路由
    private String firstTransferInChannel;
}
