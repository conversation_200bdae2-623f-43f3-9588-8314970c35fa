package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SpaQueryTradeDetailsRespDTO
 * @Description: 3.9查询账户当日历史交易明细[4013]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryTradeDetailsRespDTO extends SpaB2BICBaseRespDTO{

    /**
     * 	账号	Char(20)	Y
     */
    private String AcctNo;

    /**
     * 	货币类型	Char(3)	Y
     */
    private String CcyCode;

    /**
     * 	数据结束标志	Char(1)	Y
     * 	“Y”---表示查询结果已全部输出完毕；
     *  “N”---表示查询结果只输出一部分，后续部分有待请求输出；
     */
    private String EndFlag;

    /**
     * 	预留字段	Char(120)
     */
    private String Reserve;

    /**
     * 	查询页码	Char(6)	Y	同上送
     */
    private String PageNo;

    /**
     * 	记录笔数	Char(2)		本次返回的笔数
     */
    private String PageRecCount;

    private List<SpaQueryTradeDetails> list;

}
