package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req.SpaSettlementPlatBaseReqDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class BindLinkedAccountReqDTO extends SpaSettlementPlatBaseReqDTO {

    //	子账户账号	系统返回的子账户帐号 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”（右侧）进行分隔。
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;
    //	交易网会员代码	交易网会员代码即会员在平台端系统的会员编号 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的交易网代码，并且须用“|::|”（右侧）进行分隔。
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;
    //	会员名称	客户真实姓名
    @JsonProperty(value = "MemberName")
    private String memberName;
    //	会员证件类型	详见《接口证件类型说明》
    @JsonProperty(value = "MemberGlobalType")
    private String memberGlobalType;
    //	会员证件号码	身份证号码
    @JsonProperty(value = "MemberGlobalId")
    private String memberGlobalId;
    //	会员账号	待绑定的二类户
    @JsonProperty(value = "MemberAcctNo")
    private String memberAcctNo;
    //	商户号	银行开放银行返回的商户号
    @JsonProperty(value = "TraderCode")
    private String traderCode;
    //	STRING(30)	手机号（个体工商户则填写法人手机号）
    @JsonProperty(value = "Mobile")
    private String mobile;
    //	个体工商户标志	1：是 2：否
    @JsonProperty(value = "IndivBusinessFlag")
    private String indivBusinessFlag;
    //	公司名称	个体工商户必输
    @JsonProperty(value = "CompanyName")
    private String companyName;
    //	公司证件类型	个体工商户必输 73-统一社会信用代码证号
    @JsonProperty(value = "CompanyGlobalType")
    private String companyGlobalType;
    //	公司证件号码	个体工商户必输
    @JsonProperty(value = "CompanyGlobalId")
    private String companyGlobalId;
    //	法人标志	1-是 2-否
    @JsonProperty(value = "RepFlag")
    private String repFlag;
    //	法人名称	LegalFlag为2时必输
    @JsonProperty(value = "ReprName")
    private String reprName;
    //	法人证件类型	LegalFlag为2时必输
    @JsonProperty(value = "ReprGlobalType")
    private String reprGlobalType;
    //	法人证件号码	LegalFlag为2时必输
    @JsonProperty(value = "ReprGlobalId")
    private String reprGlobalId;
    //	保留域
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

}
