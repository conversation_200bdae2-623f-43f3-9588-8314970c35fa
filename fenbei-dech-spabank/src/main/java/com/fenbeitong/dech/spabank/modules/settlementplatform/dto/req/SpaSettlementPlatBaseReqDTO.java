package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SettlementplatformBaseReqDTO
 * @Description: 结算通
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SpaSettlementPlatBaseReqDTO implements Serializable {

    /*
     * 资金汇总账号	Y 32
     * 资金汇总账号
     */
    @JsonProperty(value = "FundSummaryAcctNo")
    private String fundSummaryAcctNo;

    /*
     * 交易流水号	Y 22
     * "系统流水号，建议规范：用户短号（6位）+日期（6位）+随机编号（10位）例：C256341801183669951236
           平台也可自行定义，满足长度即可"
     */
    @JsonProperty(value = "CnsmrSeqNo")
    private String cnsmrSeqNo;

    /*
     * 交易码	Y 20
     * 资金汇总账号
     */
    @JsonProperty(value = "TxnCode")
    private String txnCode;

    /*
     * 交易客户号 N 20
     * Ecif客户号（配置上传
     */
    @JsonProperty(value = "TxnClientNo")
    private String txnClientNo;

    /*
     * 发送时间	Y 22
     * "发送时间，格式为YYYYMMDDHHmmSSNNN
        后三位固定000"
     */
    @JsonProperty(value = "TxnTime")
    private String txnTime;

    /*
     * 商户号	Y 22
     * 签约客户号，见证宝产品此字段为必输，四位平台代码
     */
    @JsonProperty(value = "MrchCode")
    private String mrchCode;
}
