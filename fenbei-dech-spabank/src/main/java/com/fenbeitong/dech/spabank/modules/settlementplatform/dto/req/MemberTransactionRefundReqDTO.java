package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MemberTransactionRefundReqDTO
 * @Description: KFEJZB6164	会员间交易退款-不验证	MemberTransactionRefund
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MemberTransactionRefundReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     * 2：会员交易接口【直接支付】退款
     * 针对【6006/6034/6101】funcflag=6、9-直接支付的退款
     * 6：会员资金支付接口的退款
     * 针对【6163/6165/6166】会员资金支付的退款
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 原交易流水号 Y 20
     */
    @JsonProperty(value = "OldTranSeqNo")
    private String oldTranSeqNo;

    /*
     * 原转出见证子账户的帐号	Y 32
     */
    @JsonProperty(value = "OldOutSubAcctNo")
    private String oldOutSubAcctNo;

    /*
     * 转出方的交易网会员代码	Y 32
     */
    @JsonProperty(value = "OldOutMemberCode")
    private String oldOutMemberCode;

    /*
     * 转入方的见证子账户的账号	Y 32
     * 收款方
     */
    @JsonProperty(value = "OldInSubAcctNo")
    private String oldInSubAcctNo;

    /*
     * 转入方的交易网会员代码	Y 32
     * 默认为RMB
     */
    @JsonProperty(value = "OldInMemberCode")
    private String oldInMemberCode;

    /*
     * 原订单号	Y 32
     */
    @JsonProperty(value = "OldOrderNo")
    private String oldOrderNo;

    /*
     * 交易金额	Y 15
     * 包含退款手续费，即退款金额=实际会员到账退款金额+退款手续费
     */
    @JsonProperty(value = "ReturnAmt")
    private String returnAmt;

    /*
     * 交易费用	Y 15
     * 退款手续费
     */
    @JsonProperty(value = "ReturnCommission")
    private String returnCommission = "0";

    /*
     * 备注	Y 2
     */
    @JsonProperty(value = "Remark")
    private String remark;

    /*
     * 保留域  N 120
     * 退款交易时间超过三个月以上的保留域字段为必输，并注意如下规则：
     * 1.退款流水的原交易日期trandate（trandate需要以电商见证宝系统记录交易的日期为准，在跨日极端情况下trandate需要以原交易返回前置流水号的前六位记录的日期为准。）
     * 2.若平台调用6164接口报错：“未检索到原交易，正在尝试查询历史数据，请稍后再试”，需平台间隔一段时间再次尝试发起退款（建议间隔5分钟）。
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

}
