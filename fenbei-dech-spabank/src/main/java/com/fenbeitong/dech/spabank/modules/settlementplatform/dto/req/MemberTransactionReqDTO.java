package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MemberTransactionReqDTO
 * @Description: KFEJZB6034	会员间交易-不验证	MemberTransaction
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MemberTransactionReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     *  6：直接支付T+1
     *  9：直接支付T+0
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag = "9";

    /*
     * 转出方的见证子账户的账号	Y 32
     * 付款方
     */
    @JsonProperty(value = "OutSubAcctNo")
    private String outSubAcctNo;

    /*
     * 转出方的交易网会员代码	Y 32
     */
    @JsonProperty(value = "OutMemberCode")
    private String outMemberCode;

    /*
     * 转出方的见证子账户的户名	N 120
     * 户名是绑卡时上送的账户名称，如果未绑卡，就送OpenCustAcctId接口上送的用户昵称UserNickname
     */
    @JsonProperty(value = "OutSubAcctName")
    private String outSubAcctName;

    /*
     * 转入方的见证子账户的账号	Y 32
     * 收款方
     */
    @JsonProperty(value = "InSubAcctNo")
    private String inSubAcctNo;

    /*
     * 转入方的交易网会员代码	Y 32
     * 默认为RMB
     */
    @JsonProperty(value = "InMemberCode")
    private String inMemberCode;

    /*
     * 转入方的见证子账户的户名	 120
     * 户名是绑卡时上送的账户名称，如果未绑卡，就送OpenCustAcctId接口上送的用户昵称UserNickname
     */
    @JsonProperty(value = "InSubAcctName")
    private String inSubAcctName;

    /*
     * 交易金额	Y 15
     * 包含交易费用，会员的实际到账金额=交易金额-交易费用
     */
    @JsonProperty(value = "TranAmt")
    private String tranAmt;

    /*
     * 交易费用	Y 15
     * 平台收取交易费用
     */
    @JsonProperty(value = "TranFee")
    private String tranFee = "0";

    /*
     * 交易类型	Y 2
     * 01：普通交易
     * SafeTrade_T0：下单预支付T0（1分支才有用）
     * T0：白名单维护转账T0（4分支维护白名单时才有用）
     */
    @JsonProperty(value = "TranType")
    private String tranType = "01";

    /*
     * 币种	Y 3
     * 默认为RMB
     */
    @JsonProperty(value = "Ccy")
    private String ccy = "RMB";

    /*
     * 订单号	Y 30
     * 功能标志为1,2,3时必输，全局唯一，不能与6139/6007/6135/6134订单号相同
     */
    @JsonProperty(value = "OrderNo")
    private String orderNo;

    /*
     * 订单内容	N 500
     */
    @JsonProperty(value = "OrderContent")
    private String orderContent;

    /*
     * 备注	N 120
     * 建议可送订单号，可在对账文件的备注字段获取到
     */
    @JsonProperty(value = "Remark")
    private String Remark;

    /*
     * 保留域  N 120
     * 若需短信验证码则此项必输短信指令号
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

    /*
     * 网银签名	N 256
     * 若需短信验证码则此项必输
     */
    @JsonProperty(value = "WebSign")
    private String webSign;

}
