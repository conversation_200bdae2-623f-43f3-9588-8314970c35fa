package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:51:17
 * @Version 1.0
 **/
@Data
public class TransferInApplySignReqDTO implements Serializable {
    // businessNo String 是 32 业务请求流水号 业务请求流水号 OB8870a9462e00202101086764888186
    private String businessNo;
    // cardNo String 是 32 卡号 卡号  6230580000001422877
    private String cardNo;
    // agreementNo String 是 - 协议号 账户开户/开户结果查询/账户信息查询/授权/授权查询返回的协议号
    private String agreementNo;
}
