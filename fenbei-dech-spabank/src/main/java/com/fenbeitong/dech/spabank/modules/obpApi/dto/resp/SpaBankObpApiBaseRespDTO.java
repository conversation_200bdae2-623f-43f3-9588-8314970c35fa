package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class SpaBankObpApiBaseRespDTO implements Serializable {
    // 响应码，000000成功；其它为异常	必须
    private String	responseCode;
    // 响应信息	必须
    private String	responseMsg;
    // 地址信息返回码，responseCode返回000000时才需要查看此参数	非必须
    private String	bizCode;
    // 地址响应信息	非必须
    private String	bizMsg;
}
