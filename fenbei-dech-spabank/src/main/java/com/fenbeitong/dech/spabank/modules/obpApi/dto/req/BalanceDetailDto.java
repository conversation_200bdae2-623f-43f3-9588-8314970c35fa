package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class BalanceDetailDto implements Serializable {

    // 子账号
    private String accNum;

    // 账户类型
    private String accType;

    // 账户余额 该子账户账面余额
    private String availBalance;

    // 可用余额 该账户的可用余额，账户余额 - 冻结支付金额
    private String balance;

    // 冻结金额 购买稳赢产品成功之后，在T+1日确认份额之前会有冻结金额
    private String breakFreezeAmt;

    private String breakStopPaymentAmt;

    private String freezeAmt;

}
