package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName QuerySingleOrderReqDTO
 * @Description: 3.11查询单笔订单
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Data
public class QuerySingleOrderReqDTO extends SpaCloudReceiptBaseReqDTO {

    /*
     * 商户订单号	Y 100
     * 商户系统生成的订单号
     */
    @JsonProperty(value = "TraderOrderNo")
    private String traderOrderNo;

    /*
     * 订单发送时间	Y 20
     * 商户系统订单生成时间 格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "OrderSendTime")
    private String orderSendTime;

    /*
     * 外部批次号	N 32
     * 外部批次号
     */
    @JsonProperty(value = "OutBatchNo")
    private String outBatchNo;
}
