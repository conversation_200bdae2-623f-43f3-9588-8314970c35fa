package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName TradeEBankPayOrderRemarkDTO
 * @Description:
 * <AUTHOR>
 * @Date 2021/10/27
 **/
@Data
public class TradeEBankPayOrderRemarkListDTO implements Serializable {

    @JsonProperty(value = "SubAccNo")
    private String subAccNo;

    // 元
    private String subamount;

    @JsonProperty(value = "PayModel")
    private String payModel = "1";

    private String object;

    private String suborderId;

    @JsonProperty(value = "TranFee")
    private String tranFee = "0";
}
