package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.spabank.vo.ErrorVo;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SpaSettlementPlatBaseRespDTO
 * @Description: 结算通
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SpaBankObpApiErrorRespDTO implements Serializable {

    @JsonProperty(value = "Code")
    private String code;

    @JsonProperty(value = "Message")
    private String message;

    @JsonProperty(value = "Errors")
    private ErrorVo errors;


}
