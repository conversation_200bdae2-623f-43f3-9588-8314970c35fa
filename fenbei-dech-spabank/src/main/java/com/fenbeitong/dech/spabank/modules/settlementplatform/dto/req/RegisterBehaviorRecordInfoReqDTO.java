package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName RegisterBehaviorRecordInfoReqDTO
 * @Description: KFEJZB6244	登记行为记录信息	RegisterBehaviorRecordInfo
 * <AUTHOR>
 * @Date 2021/10/21
 **/
@Data
public class RegisterBehaviorRecordInfoReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     * 1-登记行为记录信息
     * 2-查询补录信息
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 见证子账户的账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 交易网会员代码	Y 32
     * "交易网会员代码即会员在平台端系统的会员编号
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 操作点击的时间	Y 14
     * 功能标志FunctionFlag=1时必输
     * **************
     */
    @JsonProperty(value = "OpClickTime")
    private String opClickTime;

    /*
     * IP地址	Y 20
     * 功能标志FunctionFlag=1时必输
     * *************
     */
    @JsonProperty(value = "IpAddress")
    private String ipAddress;

    /*
     * MAC地址	Y 32
     * 功能标志FunctionFlag=1时必输
     * 5E:E3:90:4R:E5:33
     */
    @JsonProperty(value = "MacAddress")
    private String macAddress;

    /*
     * 签约渠道	Y 1
     * 1-app 2-平台H5网页 3-公众号 4-小程序    功能标志FunctionFlag=1时必输
     */
    @JsonProperty(value = "SignChannel")
    private String signChannel;

    /*
     * 保留域1	Y 1
     */
    @JsonProperty(value = "ReservedMsgOne")
    private String reservedMsgOne;

    /*
     * 保留域2	Y 1
     */
    @JsonProperty(value = "ReservedMsgTwo")
    private String reservedMsgTwo;
}
