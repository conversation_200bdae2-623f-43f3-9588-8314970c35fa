package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName ApplyRefundReqDTO
 * @Description: 3.7.4 申请退款
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Data
public class ApplyRefundReqDTO extends SpaCloudReceiptBaseReqDTO {

    /*
     * 商户编号	Y 32
     * 商户在云收款系统的编号
     */
    @JsonProperty(value = "TraderNo")
    private String traderNo;

    /*
     * 退款订单号	Y 100
     * 商户系统生成的订单号，需要保证在商户系统唯一
     */
    @JsonProperty(value = "ReturnOrderNo")
    private String returnOrderNo;

    /*
     * 原商户订单号 Y 32
     * 支付接口商户的订单号
     */
    @JsonProperty(value = "OldMerOrderNo")
    private String oldMerOrderNo;

    /*
     * 原订单发送时间	Y 20
     * 支付接口商户订单的发送时间，格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "OldOrderSendTime")
    private String oldOrderSendTime;

    /*
     * 退款订单发送时间	Y 20
     * 商户系统订单生成时间 格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "ReturnOrderSendTime")
    private String returnOrderSendTime;

    /*
     * 退款金额	Y 20
     * 整数 单位为分
     */
    @JsonProperty(value = "ReturnAmt")
    private String returnAmt;

    /*
     * 退款备注	N 300
     * 退款备注
     */
    @JsonProperty(value = "RefundRemark")
    private String refundRemark;

}
