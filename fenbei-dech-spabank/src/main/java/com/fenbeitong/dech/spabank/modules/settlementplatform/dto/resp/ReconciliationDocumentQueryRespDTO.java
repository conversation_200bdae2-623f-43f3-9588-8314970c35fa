package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ReconciliationDocumentQueryRespDTO
 * @Description: KFEJZB6103	查询对账文件信息	ReconciliationDocumentQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class ReconciliationDocumentQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 本次交易返回查询结果记录数	Y 8
     */
    @JsonProperty(value = "ResultNum")
    private String resultNum;

    @JsonProperty(value = "TranItemArray")
    private List<ReconciliationDocumentItemDTO> tranItemArray;

    /*
     * 保留域	N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
