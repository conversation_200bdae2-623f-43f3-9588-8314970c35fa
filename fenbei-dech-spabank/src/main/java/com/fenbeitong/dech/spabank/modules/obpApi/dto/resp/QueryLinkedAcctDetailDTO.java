package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * https://openbank.pingan.com/retail/index.html#/docs/api-documentation/04fndjc7vbj0g968?category=1&fid=03hisucyugdtubr4&docType=2
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 11:08:02
 * @Version 1.0
 **/
@Data
public class QueryLinkedAcctDetailDTO extends SpaBankObpApiBaseRespDTO {

    // 账户列表
    private List<Account> accountList;

    @Data
    public static class Account{
        // 三类户双边超5万状态码	三类户才会返回此字段，三类户双边超5万状态码：0-不超额，1- 超额且小于七天，2-超额且大于等于七天；二类户该
        private	String	thirdAcctOverdraftFlag;
        // 三类户激活状态	三类户才会返回此字段，三类户是否激活(是否有绑定卡入金)：0:未激活(没有做过绑定卡入金) 1:己激活（已做过绑定卡入金）；二类户该字段为空
        private	String	activeStatus;
        // 掩码卡号
        private	String	accountNoMask;
        // 账户类型	2：二类户；3：三类户
        private	String	accountType;
        // 卡号
        private	String	accountNo;
        // 账户预留手机号
        private	String	mobileNo;
        // 账户状态	账户状态：0:正常 1：异常 2：锁定
        // （锁定特指仅有六个月无交易被锁定的异常状态）
        // 返回为“1-异常”时，可以重走开户流程
        // 返回为“2-锁定”时，需要调用去除半年无交易状态
        private	String	status;
        // 原卡进出控制标志	0：不做原卡进出 1：做原卡进出 2：存量暂不做原卡进出
        private	String	cardControlFlag;
        // 绑定卡列表
        private List<BindCard> bindcardList;
    }

    @Data
    public static class BindCard{
        // 银行名称
        private	String	bankName;
        // 绑定卡卡号
        private	String	bindCardNo;
        // 掩码卡号
        private	String	bindMaskCardNo;
        // 绑卡等级	绑卡等级0-信用卡 1-安全卡(主绑卡) 2-普通卡
        private	String	bindCardLevel;
        // 超级卡标志 是否超级卡N-否 Y-是
        private	String	isSuperCard;
    }

}
