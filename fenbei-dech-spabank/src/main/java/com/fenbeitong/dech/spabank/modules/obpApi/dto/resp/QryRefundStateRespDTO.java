package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2023-02-20 19:58:19
 * @Version 1.0
 **/
@Data
public class QryRefundStateRespDTO extends SpaBankObpApiBaseRespDTO {
    // retCode String 是 - 返回编码 - 0
    private String retCode;
    // retMsg String 是 - 返回信息 - success
    private String retMsg;
    // tranResultStatusCode String 否 - 交易结果状态码
    private String tranResultStatusCode;
    // oldAcceptSeqNo String 否 - 原申请受理流水号 - OB655e6116be20220418165845
    private String oldAcceptSeqNo;
}
