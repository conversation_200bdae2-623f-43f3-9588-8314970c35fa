package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName PlatformAccountSupplyReqDTO
 * @Description: KFEJZB6147	平台补账-见证收单	PlatformAccountSupply
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class PlatformAccountSupplyReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 收单渠道类型	Y 2
     * "根据不同收单渠道上送
     *  01-橙E收款
     *  YST1-云收款"
     */
    @JsonProperty(value = "AcquiringChannelType")
    private String acquiringChannelType;

    /*
     * 订单号	Y 30
     * 根据所填渠道所返回的订单号，这里是总订单号，详见《电商见证宝开发说明V1.0.docx》的2.4章节
     */
    @JsonProperty(value = "OrderNo")
    private String orderNo;

    /**
     * 金额单位：分。 必填:Y
     */
    @JsonProperty(value = "Amt")
    private String Amt;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
