package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-11-16 18:10:43
 * @Version 1.0
 **/
@Data
public class BackFillDynamicPasswordRespDTO extends SpaSettlementPlatBaseRespDTO {
    // 保留域string(20)
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
