package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.spabank.vo.ErrorVo;
import com.google.gson.JsonObject;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @ClassName SpaSettlementPlatBaseRespDTO
 * @Description: 结算通
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SpaSettlementPlatBaseRespDTO implements Serializable {

    /**
     * 返回码
     */
    @JsonProperty(value = "TxnReturnCode")
    private String txnReturnCode;

    /**
     * 返回信息
     */
    @JsonProperty(value = "TxnReturnMsg")
    private String txnReturnMsg;

    @JsonProperty(value = "Code")
    private String code;

    @JsonProperty(value = "Message")
    private String message;

    @JsonProperty(value = "Errors")
    private List<ErrorVo> errors;

    @JsonProperty(value = "Data")
    private String data;

}
