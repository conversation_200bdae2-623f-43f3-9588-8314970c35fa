package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @ClassName QuerySingleRefundRespDTO
 * @Description: 3.7.5 查询单笔退款
 * <AUTHOR>
 * @ate 2021/10/14
 **/
public class QuerySingleRefundRespDTO extends SpaBaseRespDTO{

    /*
     * 退款订单号	Y 100
     * 商户系统生成的订单号，需要保证在商户系统唯一
     */
    @JsonProperty(value = "ReturnOrderNo")
    private String returnOrderNo;

    /*
     * 银行订单号	Y 50
     * 云收款系统订单号
     */
    @JsonProperty(value = "BankOrderNo")
    private String bankOrderNo;

    /*
     * 退款金额	Y 20
     * 整数 单位为分
     */
    @JsonProperty(value = "ReturnAmt")
    private String returnAmt;

    /*
     * 退款订单状态 Y 20
     * 0 已受理;1 交易成功 ;2 交易中; 3 用户支付中;  4 交易关闭; 9 已撤销
     * SpaOrderStatusEnum
     */
    @JsonProperty(value = "ReturnOrderStatus")
    private String returnOrderStatus;

    /*
     * 退款成功时间 Y 20
     * 银行订单退款成功时间 格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "RefundSuccessTime")
    private String refundSuccessTime;

}
