package com.fenbeitong.dech.spabank.modules.cz.service.impl;

import com.fenbeitong.dech.spabank.modules.cz.client.SpaCZApiClient;
import com.fenbeitong.dech.spabank.modules.cz.dto.*;
import com.fenbeitong.dech.spabank.modules.cz.enums.SpaCzTransCodeEnum;
import com.fenbeitong.dech.spabank.modules.cz.service.SpaCzTradeService;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-08-15 下午3:30
 */
@Service
public class SpaCzTradeServiceImpl implements SpaCzTradeService {

    @Autowired
    SpaCZApiClient spaCZApiClient;

    @Override
    public SpaCzPay001RespDTO pay001(SpaCzPay001ReqDTO spaCzPay001ReqDTO) {
        SpaCzPay001RespDTO request = spaCZApiClient.request(JsonUtils.toJson(spaCzPay001ReqDTO), SpaCzPay001RespDTO.class, SpaCzTransCodeEnum.STAR_PAY001.getTxnCode());
        return request;
    }

    @Override
    public SpaCzPayResultDetailRespDTO payResult001(SpaCzPayResultReqDTO spaCzPayResultReqDTO) {
        SpaCzPayResultDetailRespDTO request = spaCZApiClient.request(JsonUtils.toJson(spaCzPayResultReqDTO), SpaCzPayResultDetailRespDTO.class, SpaCzTransCodeEnum.STAR_PAYRESULT001.getTxnCode());
        return request;
    }

    @Override
    public SpaCzPayRoll001RespDTO payRoll001(SpaCzPayRoll001ReqDTO spaCzPayRoll001ReqDTO) {
        SpaCzPayRoll001RespDTO request = spaCZApiClient.request(JsonUtils.toJson(spaCzPayRoll001ReqDTO), SpaCzPayRoll001RespDTO.class, SpaCzTransCodeEnum.STAR_PAYROLL001.getTxnCode());
        return request;
    }

    @Override
    public SpaCzPayRollResultRespDTO payRollResult001(SpaCzPayRollResultReqDTO spaCzPayRollResultReqDTO) {
        SpaCzPayRollResultRespDTO request = spaCZApiClient.request(JsonUtils.toJson(spaCzPayRollResultReqDTO), SpaCzPayRollResultRespDTO.class, SpaCzTransCodeEnum.STAR_PAYROLLRESULT001.getTxnCode());
        return request;
    }
}
