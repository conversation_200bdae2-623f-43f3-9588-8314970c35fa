package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName ChargeDetailQueryReqDTO
 * @Description: KFEJZB6146	查询充值明细-见证收单	ChargeDetailQuery
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class ChargeDetailQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 订单号	Y 32
     * 全局唯一，不能与6034/6101/6006/6007/6135/6134订单号相同。
     */
    @JsonProperty(value = "OrderNo")
    private String orderNo;

    /*
     * 收单渠道类型	Y 32
     * "根据不同收单渠道上送
     *  01-橙E收款
     *  YST1-云收款"
     * 例如：YST1-云收款送YST1
     */
    @JsonProperty(value = "AcquiringChannelType")
    private String acquiringChannelType = "YST1";

    /*
     * 保留域	Y 1
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
