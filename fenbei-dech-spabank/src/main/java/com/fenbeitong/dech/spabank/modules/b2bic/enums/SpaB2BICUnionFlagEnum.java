package com.fenbeitong.dech.spabank.modules.b2bic.enums;

/**
 * @ClassName SpaB2BICUnionFlagEnum
 * @Description: 行内跨行标志
 * <AUTHOR>
 * @Date 2022/1/25
 **/
public enum SpaB2BICUnionFlagEnum {

    /**
     * 1：行内转账，0：跨行转账
     */
    INTRA_BANK_TRANSFER("1","行内转账"),
    INTER_BANK_TRANSFER("0","跨行转账")
    ;

    private String unionFlag;
    private String desc;

    public String getUnionFlag() {
        return unionFlag;
    }

    public void setUnionFlag(String unionFlag) {
        this.unionFlag = unionFlag;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    SpaB2BICUnionFlagEnum(String unionFlag, String desc) {
        this.unionFlag = unionFlag;
        this.desc = desc;
    }
}
