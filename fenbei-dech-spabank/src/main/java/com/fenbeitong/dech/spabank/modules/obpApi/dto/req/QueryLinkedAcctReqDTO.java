package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 联动账户信息查询
 * @author: jian.liu
 * @create: 2023-12-04 10:54:56
 * @Version 1.0
 **/
@Data
public class QueryLinkedAcctReqDTO implements Serializable {

    // businessNo String 是 业务请求流水号
    private String businessNo;
    // agreementNo String 是 协议号 联动二三开户/开户结果查询/账户信息查询会返回协议号 81ae29bfbf3543da8249d8e1624d6c5f
    private String agreementNo;
    // 账户类型 0：查二、三类户；2：仅查二类户；3：仅查三类户
    private String accountType = "0";

}
