package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName QueryCustAcctIdReqDTO
 * @Description: KFEJZB6037 查询会员子账号 QueryCustAcctId
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class QueryCustAcctIdReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 交易网会员代码	Y 32
     * 交易网会员代码
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
