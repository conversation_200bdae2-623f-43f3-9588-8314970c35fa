package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryTransferStatusReqDTO
 * @Description: 3.5单笔转账指令查询[4005]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryTransferStatusRespDTO extends SpaB2BICBaseRespDTO {

    /**
     * 转账凭证号	C(20)	必输
     */
    private String OrigThirdVoucher;

    /**
     * 	银行流水号	C(32)	必输
     */
    private String FrontLogNo;

    /**
     * 	客户自定义凭证号	C(20)	非必输	客户上送则返回
     */
    private String CstInnerFlowNo;

    /**
     * 	货币类型	C(3)	必输
     */
    private String CcyCode;

    /**
     * 	转出账户开户网点名	C(60)	非必输
     */
    private String OutAcctBankName;

    /**
     * 	转出账户	C(20)	必输
     */
    private String OutAcctNo;

    /**
     * 	转入账户网点名称	C(60)	非必输
     */
    private String InAcctBankName;

    /**
     * 	转入账户	C(32)	必输
     */
    private String InAcctNo;

    /**
     * 	转入账户户名	C(60)	必输
     */
    private String InAcctName;

    /**
     * 	交易金额	C(13)	必输
     */
    private String TranAmount;

    /**
     * 	行内跨行标志	C(1)	必输	1：行内转账，0：跨行转账
     */
    private String UnionFlag;

    /**
     * 交易状态标志	C(2)	必输
     * 20：成功
     * 30：失败
     * 其他为银行受理成功处理中
     */
    private String Stt;

    /**
     * 	转账退票标志	C(1)	非必输
     * 	0:未退票; 默认为0
     *  1:退票;
     */
    private String IsBack;

    /**
     * 支付失败或退票原因描述	C(20)	非必输
     * 如果是超级网银则返回如下信息:
     * RJ01对方返回：账号不存在
     * RJ02对方返回：账号、户名不符
     * 大小额支付则返回失败描述
     */
    private String BackRem;

    /**
     * 	银行处理结果	C(40)	必输
     *  格式为：“六位代码:中文描述”。冒号为半角。如：000000：转账成功
     *  处理中的返回(以如下返回开头)：
     *  MA9111:交易正在受理中
     *  000000:交易受理成功待处理
     *  000000:交易处理中
     *  000000:交易受理成功处理中
     *  成功的返回：
     *  000000:转账交易成功
     *  其他的返回都为失败:
     *  MA9112:转账失败
     */
    private String Yhcljg;

    /**
     * 	转账加急标志	C(1) 必输
     * Y：加急
     * N：普通
     * S：特急
     */
    private String SysFlag;

    /**
     * 	转账手续费	C(13)	必输
     */
    private String Fee;

    /**
     * 	转账代码类型	C(4)	必输
     * 	4004：单笔转账；
     *  4014：单笔批量；
     *  4034：汇总批量
     */
    private String TransBsn;

    /**
     * 	交易受理时间	C(14)	非必输	交易受理时间
     */
    private String submitTime;

    /**
     * 	记账日期	C(8)	非必输	主机记账日期
     */
    private String AccountDate;

    /**
     * 	主机记账流水号	C(32)	非必输	主机记账流水
     */
    private String hostFlowNo;

    /**
     * 	错误码	C(20)	非必输	交易失败的错误代码
     */
    private String hostErrorCode;

    /**
     * 	代理人户名	C(60)	非必输	用于代理行支付功能
     */
    private String ProxyPayName;

    /**
     * 	代理人账号	C(30)	非必输	用于代理行支付功能
     */
    private String ProxyPayAcc;

    /**
     * 	代理人银行名称	C(30)	非必输	用于代理行支付功能
     */
    private String ProxyPayBankName;

}
