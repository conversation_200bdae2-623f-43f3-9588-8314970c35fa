package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaFundTransferReqDTO
 * @Description: 3.3企业单笔资金划转[4004]
 * <AUTHOR>
 * @Date 2022/1/10
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaFundTransferReqDTO implements Serializable {

    /**
     * 	转账凭证号	C(20)，最少10位长度	必输
     *  标示交易唯一性，同一客户上送的不可重复，建议格式：yyyymmddHHSS+8位系列
     *  要求6个月内唯一
     */
    private String ThirdVoucher;

    /**
     * 	客户自定义凭证号	C(20)	非必输
     *  用于客户转账登记和内部识别，通过转账结果查询可以返回。银行不检查唯一性
     */
    private String CstInnerFlowNo;

    /**
     * 	货币类型	C(3)	必输
     *  RMB-人民币
     */
    private String CcyCode = "RMB";

    /**
     * 	付款人账户	C(20)	必输
     * 	扣款账户
     */
    private String OutAcctNo;

    /**
     * 	付款人名称	C(60)	必输
     * 	付款账户户名
     */
    private String OutAcctName;

    /**
     * 	付款人地址	C(60)	非必输
     * 	建议填写付款账户的分行、网点名称
     */
    private String OutAcctAddr;

    /**
     * 	收款人开户行行号	C(12)	非必输
     * 	跨行转账建议必输。
     *  为人行登记在册的商业银行号，
     *  若输入则长度必须在4~12位之间；
     *  若
     */
    private String InAcctBankNode;

    /**
     * 	接收行行号	C(12)	非必输
     * 	建议同收款人开户行行号
     */
    private String InAcctRecCode;

    /**
     * 	收款人账户	C(32)	必输
     */
    private String InAcctNo;

    /**
     * 	收款人账户户名	C(60)	必输
     */
    private String InAcctName;

    /**
     * 	收款人开户行名称	C(60)	非必输
     * 	建议格式：xxx银行
     *  若跨行不输入，银行按收款方账户模糊匹配，若匹配失败则交易拒绝。建议转入他行对公户必须输入，转入银联卡可以不用输入。
     *
     */
    private String InAcctBankName;

    /**
     * 	收款账户银行开户省代码或省名称	C(10)	非必输
     * 	建议跨行转账输入；对照码参考“附录-省对照表”；也可输入“附录-省对照表”中的省名称。
     */
    private String InAcctProvinceCode;

    /**
     * 	收款账户开户市	C(12)	非必输
     * 	建议跨行转账输入；
     *
     */
    private String InAcctCityName;

    /**
     * 	转出金额	C(13,2)	必输
     * 	如为XML报文，则直接输入输出以元为单位的浮点数值，如2.50 (两元五角)
     */
    private String TranAmount;

    /**
     * 	资金用途	C(100)	非必输
     * 	100个汉字，对方能否看到此用途视收款方银行的支持。
     */
    private String UseEx;

    /**
     * 	行内跨行标志	C(1)	非必输
     * 	1：行内转账，0：跨行转账
     *  若跨行不输入，银行按收款方账户模糊匹配，若匹配失败则交易拒绝。建议转入他行对公户必须输入，转入银联卡可以不用输入。当银行匹配联行号失败会拒绝指令，提示输入非法。
     *  若客户上送则要输入正确。
     */
    private String UnionFlag;

    /**
     * 	转账加急标志	C(1) 非必输
     * 	N：普通（大小额自动选择），默认值；
     *  Y：加急 （大额）；
     *  S：特急(超级网银)；
     *  默认为N
     */
    private String SysFlag;

    /**
     * 	同城/异地标志	C(1) 必输
     * 	“1”—同城   “2”—异地；若无法区分，可默认送1-同城。
     */
    private String AddrFlag = "1";

    /**
     * 	付款虚子账户	C(32)	非必须
     * 	必须签约了银行现金管理合约才能使用此域；
     *  用于现金管理代理结算（不同与代理行支付功能）：填写虚子账号。虚子账户代理主账户付款。
     */
    private String MainAcctNo;
    /**
     * 	收款人证件类型	C(2)	非必输
     * 	参考附录-证件号码对照表
     *  上送则验证证件号码是否一致，
     *  只对行内个人借记卡收款账户有效（不支持信用卡）
     */
    private String InIDType;

    /**
     * 	收款人证件号码	C(30)	非必输
     * 	上送则验证证件号码是否一致
     */
    private String InIDNo;

    public void useExToSbc() {
        if (ObjUtils.isNotBlank(this.UseEx)) {
            char[] c = this.UseEx.toCharArray();
            for (int i = 0; i < c.length; i++) {
                if (c[i] == 32) {
                    c[i] = (char) 12288;
                    continue;
                }
                if (c[i] < 127) {
                    c[i] = (char) (c[i] + 65248);
                }
            }
            this.UseEx = new String(c);
        }

    }
}
