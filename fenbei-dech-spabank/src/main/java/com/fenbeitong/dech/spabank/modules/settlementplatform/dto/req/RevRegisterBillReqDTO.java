package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName RevRegisterBillRespDTO
 * @Description: KFEJZB6140	登记挂账撤销	RevRegisterBillSupportWithdraw
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class RevRegisterBillReqDTO extends SpaSettlementPlatBaseReqDTO{

    /*
     * 见证子账户的账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 交易网会员代码	Y 32
     * "交易网会员代码即会员在平台端系统的会员编号
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 原订单号	Y 32
     * 原【6139】登记挂账的订单号
     */
    @JsonProperty(value = "OldOrderNo")
    private String oldOrderNo;

    /*
     * 撤销金额	Y 32
     * 支持部分撤销，不能大于原订单可用金额，包含交易费用，即撤销金额=实际会员到账的撤销金额+交易费用
     */
    @JsonProperty(value = "CancelAmt")
    private String cancelAmt;

    /*
     * 交易费用	Y 32
     * 平台收取用户的费用
     */
    @JsonProperty(value = "TranFee")
    private String tranFee;

    /*
     * 备注	Y 32
     */
    @JsonProperty(value = "Remark")
    private String remark;

    /*
     * 保留域1	Y 1
     */
    @JsonProperty(value = "ReservedMsgOne")
    private String reservedMsgOne;

    /*
     * 保留域2	Y 1
     */
    @JsonProperty(value = "ReservedMsgTwo")
    private String reservedMsgTwo;

    /*
     * 保留域3	Y 32
     */
    @JsonProperty(value = "ReservedMsgThree")
    private String reservedMsgThree;
}
