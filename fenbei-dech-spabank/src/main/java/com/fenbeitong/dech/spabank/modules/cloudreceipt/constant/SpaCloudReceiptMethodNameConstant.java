package com.fenbeitong.dech.spabank.modules.cloudreceipt.constant;

/**
 * @ClassName CloudReceiptMethodNameConstant
 * @Description: 平安银行-云收款-接口名称常量
 * <AUTHOR>
 * @Date 2021/10/15
 **/
public class SpaCloudReceiptMethodNameConstant {


    /**
     * 3.7.2 企业网银(B2B)支付
     */
    public static final String TRADE_E_BANK_PAY = "V1.0/order/tradeEBankPay";

    /**
     * 3.7.3 查询单笔订单
     */
    public static final String QUERY_SINGLE_ORDER = "/V1.0/order/querySingleOrder";

    /**
     * 3.7.4 申请退款
     */
    public static final String APPLY_REFUND = "/V1.0/order/applyRefund";

    /**
     * 3.7.5 查询单笔退款
     */
    public static final String QUERY_SINGLE_REFUND = "/V1.0/order/querySingleRefund";

    /**
     * 3.14 下载对账文件
     */
    public static final String DOWNLOAD_RECONCILIATION_FILE = "/V1.0/order/downloadReconciliationFile";

}
