package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName SpaOrderStatusEnum
 * @Description: 订单状态
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum SpaOrderStatusEnum {
    /**
     * 0 已受理;1 交易成功 ;2 交易中; 3 用户支付中;  4 交易关闭; 9 已撤销
     */
    ACCEPTED("0","已受理"),
    TRADE_SUCCESSFUL("1","交易成功"),
    TRADE_PROCESSING("2","交易中"),
    USER_PAYMENT_PROCESSING("3","用户支付中"),
    TRADE_CLOSED("4","交易关闭"),
    RESCINDED("9","已撤销")
    ;

    SpaOrderStatusEnum(String orderStatus, String desc){
        this.orderStatus = orderStatus;
        this.desc = desc;
    }

    private String orderStatus;
    private String desc;

    public String getOrderStatus() {
        return orderStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Boolean isSuccess(String status) {
        return StringUtils.isNotBlank(status) && TRADE_SUCCESSFUL.getOrderStatus().equals(status) ? true : false;
    }

}
