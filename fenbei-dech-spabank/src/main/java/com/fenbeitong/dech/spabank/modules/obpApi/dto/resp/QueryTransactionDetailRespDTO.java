package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

import java.util.List;

/**
 * @description: 查询交易明细
 * @author: yanqiu.hu
 * @create: 2022-10-11 10:54:38
 * @Version 1.0
 **/
@Data
public class QueryTransactionDetailRespDTO extends SpaBankObpApiBaseRespDTO {
    // totalSize String 否 - 总数量 - 50
    private String totalSize;
    // transactionList	Array<Object>	否	-	交易明细详情
    private List<QueryTransactionDetailDTO> transactionList;
}
