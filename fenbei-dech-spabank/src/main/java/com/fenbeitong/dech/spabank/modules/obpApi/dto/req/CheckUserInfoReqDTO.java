package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class CheckUserInfoReqDTO implements Serializable {
    // 	商户号	必须
    private String	appId;
    // 	商户唯一，会用做并发去重处理	必须
    private String	requestId;
    // 	客户姓名(入参agreementNo，可以不传)	非必须
    private String	trueName;
    // 	证件号码(入参agreementNo，可以不传)	非必须
    private String	idNo;
    // 	证件类型(入参agreementNo，可以不传)	非必须
    private String	idType;
    // 	协议号(入参trueNem、idNo和idType，可以不传)	非必须
    private String	agreementNo;
    // 	职业代码，（建议商户维护职业列表），银行线下提供职业列表。只能填写职业小类示例值：21000	必须
    private String	occupation;
    // 	工作单位	非必须
    private String	workOrgName;
    // 	住址地址-省份	非必须
    private String	homeProvince;
    // 	住址地址-省份编码	必须
    private String	homeProvinceCode;
    // 	住址地址-城市	非必须
    private String	homeCity;
    // 	住址地址-城市编码	必须
    private String	homeCityCode;
    // 	住址地址-区县	非必须
    private String	homeCounty;
    // 	住址地址-区县编码	必须
    private String	homeCountyCode;
    // 	住址地址-街道地址	必须
    private String	homeStreetAddress;
    // 	住址地址-详细地址	非必须
    private String	homeAddressDetail;
    // 	场景码。RH：去除半年无交易，NE：证件过期	非必须
    private String	scene;
}
