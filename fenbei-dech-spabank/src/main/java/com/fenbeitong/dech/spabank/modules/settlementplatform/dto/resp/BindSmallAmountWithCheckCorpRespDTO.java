package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName BindSmallAmountWithCheckCorpReqDTO
 * @Description: KFEJZB6240 会员绑定提现账户小额鉴权-校验法人 BindSmallAmountWithCheckCorp
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class BindSmallAmountWithCheckCorpRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 保留域	Y 4
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

}
