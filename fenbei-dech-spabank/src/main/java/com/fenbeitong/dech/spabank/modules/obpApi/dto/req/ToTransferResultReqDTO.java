package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ToTransferResultReqDTO implements Serializable {

    // 是否虚拟卡业务 目前业务报销卡需要上送subAppId， 虚拟卡不需要上送subAppId
    private Boolean isBankCard;
    //	100	转账发起方流水号	Y	转账发起方流水号【长度100以内】，需要和OTP发送接口中的bussinessNo的值一样
    private String businessNo;
    //	转账指令号
    private String orderSerialNo;
    //	协议号
    private String agreementNo;
}
