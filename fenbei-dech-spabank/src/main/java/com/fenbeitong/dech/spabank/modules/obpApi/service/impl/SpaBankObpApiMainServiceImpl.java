package com.fenbeitong.dech.spabank.modules.obpApi.service.impl;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.common.until.IDGen;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.dech.spabank.modules.obpApi.client.SpaBankObpApiClient;
import com.fenbeitong.dech.spabank.modules.obpApi.constant.SpaObpApiCommonConstant;
import com.fenbeitong.dech.spabank.modules.obpApi.constant.SpaObpApiMethodNameConstant;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.req.*;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.*;
import com.fenbeitong.dech.spabank.modules.obpApi.service.SpaBankObpApiMainService;
import com.fenbeitong.dech.spabank.utils.SpaSignUtil;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName SpaBankObpApiMainServiceImpl
 * @Description: 商户联营卡基础service
 * <AUTHOR>
 * @Date 2022/07/28
 **/
@Service
public class SpaBankObpApiMainServiceImpl implements SpaBankObpApiMainService {

    @Autowired
    private SpaBankObpApiClient obpApiClient;

    @Value("${spaBank.obpApi.appId}")
    private String obpApiAppId;

    @Value("${spaBank.settlementPlat.appId}")
    private String settlementPlatAppId;


    @Override
    public CheckUserInfoRespDTO checkUserInfo(CheckUserInfoReqDTO checkUserInfoReqDTO) {
        checkUserInfoReqDTO.setAppId(SpaSignUtil.convertAppId(settlementPlatAppId));
        String requestId = IDGen.genId(IDGen.SPABANK_OPEN_PERSON_ACCT_PFX);
        checkUserInfoReqDTO.setRequestId(requestId);
        return requestCommon(SpaObpApiMethodNameConstant.CHECK_USER_INFO, JsonUtils.toJson(checkUserInfoReqDTO), CheckUserInfoRespDTO.class, settlementPlatAppId);
    }

    @Override
    public OpenAccountPreRespDTO openAccountPre(OpenAccountPreReqDTO openAccountPreReqDTO) {
        // 非虚拟卡业务需要上送
        if (!openAccountPreReqDTO.getIsBankCard()) {
            openAccountPreReqDTO.setSubAppId(SpaSignUtil.convertAppId(obpApiAppId));
        }
        return requestCommon(SpaObpApiMethodNameConstant.OPEN_ACCOUNT_PRE, JsonUtils.toJson(openAccountPreReqDTO), OpenAccountPreRespDTO.class, settlementPlatAppId);
    }

    @Override
    public ObtainFileTokenRespDTO obtainFileToken(ObtainFileTokenReqDTO obtainFileTokenReqDTO) {
        obtainFileTokenReqDTO.setAppId(SpaSignUtil.convertAppId(settlementPlatAppId));
        String requestId = IDGen.genId(IDGen.SPABANK_PERSON_ACCT_UPLOAD_PFX);
        obtainFileTokenReqDTO.setRequestId(requestId);
        //  要求全局唯一， OB（固定字母，标记开放银行渠道） +
        //  appId（10位，不够10位的在左边补0）+
        //  两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） +
        //  yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）
        obtainFileTokenReqDTO.setBusinessNo(IDGen.businessNoForSpaBank(SpaObpApiCommonConstant.BUSINESS_NO_PFX, SpaSignUtil.convertAppId(settlementPlatAppId)));
        return requestCommon(SpaObpApiMethodNameConstant.OBTAIN_FILE_TOKEN, JsonUtils.toJson(obtainFileTokenReqDTO), ObtainFileTokenRespDTO.class, settlementPlatAppId);
    }

    @Override
    public SubmitImageRespDTO submitImage(SubmitImageReqDTO submitImageReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.SUBMIT_IMAGE, JsonUtils.toJson(submitImageReqDTO), SubmitImageRespDTO.class, settlementPlatAppId);
    }

    @Override
    public LinkedSendOtpRespDTO linkedSendOtp(LinkedSendOtpReqDTO linkedSendOtpReqDTO) {
        String openAcctBusinessNo = IDGen.genId(IDGen.SPABANK_PERSON_ACCT_OTP_PFX);
        linkedSendOtpReqDTO.setBusinessNo(openAcctBusinessNo);
        // 非虚拟卡业务需要上送
        if (!linkedSendOtpReqDTO.getIsBankCard()) {
            linkedSendOtpReqDTO.setSubAppId(SpaSignUtil.convertAppId(obpApiAppId));
        }
        LinkedSendOtpRespDTO linkedSendOtpRespDTO = requestCommon(SpaObpApiMethodNameConstant.LINKED_SEND_OTP, JsonUtils.toJson(linkedSendOtpReqDTO), LinkedSendOtpRespDTO.class, settlementPlatAppId);
        return linkedSendOtpRespDTO;
    }

    @Override
    public OpenLinkedAccountRespDTO openLinkedAccount(OpenLinkedAccountReqDTO openLinkedAccountReqDTO) {
        //  要求全局唯一， OB（固定字母，标记开放银行渠道） +
        //  appId（10位，不够10位的在左边补0）+
        //  两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） +
        //  yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）
        String businessNo = IDGen.businessNoForSpaBank(SpaObpApiCommonConstant.BUSINESS_NO_PFX, SpaSignUtil.convertAppId(settlementPlatAppId));
        openLinkedAccountReqDTO.setBusinessNo(businessNo);
        if (!openLinkedAccountReqDTO.getIsBankCard()) {
            openLinkedAccountReqDTO.setSubAppId(SpaSignUtil.convertAppId(obpApiAppId));
        }
        OpenLinkedAccountRespDTO openLinkedAccountRespDTO = requestCommon(SpaObpApiMethodNameConstant.OPEN_LINKED_ACCOUNT, JsonUtils.toJson(openLinkedAccountReqDTO), OpenLinkedAccountRespDTO.class, settlementPlatAppId);
        openLinkedAccountRespDTO.setSpaOpenBusinessNo(businessNo);
        return openLinkedAccountRespDTO;
    }

    @Override
    public QueryOpenLinkAccountResultRespDTO queryOpenLinkedAcctStatus(QueryOpenLinkedAccountResultReqDTO queryOpenLinkedAccountResultReqDTO) {
        queryOpenLinkedAccountResultReqDTO.setAppId(SpaSignUtil.convertAppId(settlementPlatAppId));
        return requestCommon(SpaObpApiMethodNameConstant.QUERY_OPEN_ACCOUNT_RESULT, JsonUtils.toJson(queryOpenLinkedAccountResultReqDTO), QueryOpenLinkAccountResultRespDTO.class, settlementPlatAppId);
    }

    @Override
    public QueryAccountStatusRespDTO queryAccountStatus(QueryAccountStatusReqDTO queryAccountStatusReqDTO) {
        // 非虚拟卡业务需要上送
        if (!queryAccountStatusReqDTO.getIsBankCard()) {
            queryAccountStatusReqDTO.setSubAppId(SpaSignUtil.convertAppId(obpApiAppId));
        }
        return requestCommon(SpaObpApiMethodNameConstant.QUERY_ACCOUNT_STATUS, JsonUtils.toJson(queryAccountStatusReqDTO), QueryAccountStatusRespDTO.class, settlementPlatAppId);
    }

    @Override
    public CommonSendOtpRespDTO commonSendOtp(CommonSendOtpReqDTO commonSendOtpReqDTO) {
        //  要求全局唯一， OB（固定字母，标记开放银行渠道） +
        //  appId（10位，不够10位的在左边补0）+
        //  两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） +
        //  yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）
        String businessNo = IDGen.businessNoForSpaBank(SpaObpApiCommonConstant.BUSINESS_NO_PFX, SpaSignUtil.convertAppId(obpApiAppId));
        String appId = obpApiAppId;
        if (commonSendOtpReqDTO.getIsBankCard()) {
            businessNo = IDGen.businessNoForSpaBank(SpaObpApiCommonConstant.BUSINESS_NO_PFX, SpaSignUtil.convertAppId(settlementPlatAppId));
            appId = settlementPlatAppId;
        }
        commonSendOtpReqDTO.setBusinessNo(businessNo);
        CommonSendOtpRespDTO commonSendOtpRespDTO = requestCommon(SpaObpApiMethodNameConstant.COMMON_SEND_OTP, JsonUtils.toJson(commonSendOtpReqDTO), CommonSendOtpRespDTO.class, appId);
        commonSendOtpRespDTO.setBusinessNo(businessNo);
        return commonSendOtpRespDTO;
    }

    @Override
    public QueryBalanceRespDTO queryBalance(QueryBalanceReqDTO queryBalanceReqDTO) {
        //  要求全局唯一， OB（固定字母，标记开放银行渠道） +
        //  appId（10位，不够10位的在左边补0）+
        //  两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） +
        //  yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）
//        queryBalanceReqDTO.setBusinessNo(IDGen.businessNoForSpaBank(SpaObpApiCommonConstant.BUSINESS_NO_PFX, SpaSignUtil.convertAppId(obpApiAppId)));
        queryBalanceReqDTO.setRequestTime(DateUtils.formatTime(new Date()));
        String appId = obpApiAppId;
        if (queryBalanceReqDTO.getIsBankCard()) {

            // 虚拟卡查询余额
            appId = settlementPlatAppId;
        }
        return requestCommon(SpaObpApiMethodNameConstant.QUERY_BALANCE, JsonUtils.toJson(queryBalanceReqDTO), QueryBalanceRespDTO.class, appId);
    }

    @Override
    public ToTransferWithOtpRespDTO toTransferWithOtp(ToTransferWithOtpReqDTO toTransferWithOtpReqDTO) {
        String appId = obpApiAppId;
        String bussinessScence = SpaSignUtil.convertAppId(obpApiAppId);
        if (toTransferWithOtpReqDTO.getIsBankCard()) {
            // 虚拟卡
            bussinessScence = SpaSignUtil.convertAppId(settlementPlatAppId);
            appId = settlementPlatAppId;
        }
        toTransferWithOtpReqDTO.setBussinessScence(bussinessScence);
        return requestCommon(SpaObpApiMethodNameConstant.TO_TRANSFER, JsonUtils.toJson(toTransferWithOtpReqDTO), ToTransferWithOtpRespDTO.class, appId);
    }

    @Override
    public ToTransferWithOtpRespDTO toTransferWithOtpNew(ToTransferWithOtpNewReqDTO toTransferWithOtpReqDTO) {
        String appId = obpApiAppId;
        String bussinessScence = SpaSignUtil.convertAppId(obpApiAppId);
        if (toTransferWithOtpReqDTO.getIsBankCard()) {
            // 虚拟卡
            bussinessScence = SpaSignUtil.convertAppId(settlementPlatAppId);
            appId = settlementPlatAppId;
        }
        toTransferWithOtpReqDTO.setBussinessScence(bussinessScence);
        return requestCommon(SpaObpApiMethodNameConstant.TO_TRANSFER, JsonUtils.toJson(toTransferWithOtpReqDTO), ToTransferWithOtpRespDTO.class, appId);
    }

    @Override
    public ToTransferResultRespDTO toTransferResult(ToTransferResultReqDTO toTransferResultReqDTO) {
        String appId = obpApiAppId;
        if (toTransferResultReqDTO.getIsBankCard()) {
            // 虚拟卡
            appId = settlementPlatAppId;
        }
        return requestCommon(SpaObpApiMethodNameConstant.TO_TRANSFER_RESULT, JsonUtils.toJson(toTransferResultReqDTO), ToTransferResultRespDTO.class, appId);
    }

    @Override
    public TransferInPreCheckRespDTO transferInPreCheck(TransferInPreCheckReqDTO transferInPreCheckReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.TRANSFER_IN_PRE_CHECK, JsonUtils.toJson(transferInPreCheckReqDTO), TransferInPreCheckRespDTO.class, settlementPlatAppId);
    }

    @Override
    public TransferInApplySignRespDTO transferInApplySign(TransferInApplySignReqDTO transferInApplySignReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.TRANSFER_IN_APPLY_SIGN, JsonUtils.toJson(transferInApplySignReqDTO), TransferInApplySignRespDTO.class, settlementPlatAppId);
    }

    @Override
    public TransferInApplySignVerifyRespDTO transferInApplySignVerify(TransferInApplySignVerifyReqDTO transferInApplySignVerifyReqDTO) {
        //  要求全局唯一， OB（固定字母，标记开放银行渠道） +
        //  appId（10位，不够10位的在左边补0）+
        //  两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） +
        //  yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）
        transferInApplySignVerifyReqDTO.setBusinessNo(IDGen.businessNoForSpaBank(SpaObpApiCommonConstant.BUSINESS_NO_PFX, SpaSignUtil.convertAppId(settlementPlatAppId)));
         return requestCommon(SpaObpApiMethodNameConstant.TRANSFER_IN_APPLY_SIGN_VERIFY, JsonUtils.toJson(transferInApplySignVerifyReqDTO), TransferInApplySignVerifyRespDTO.class, settlementPlatAppId);
    }

    @Override
    public QueryTransferInApplySignResultSmartRespDTO queryTransferInApplySignResultSmart(QueryTransferInApplySignResultSmartReqDTO queryTransferInApplySignResultSmartReqDTO) {
         return requestCommon(SpaObpApiMethodNameConstant.QUERY_TRANSFER_IN_APPLY_SIGN_RESULT_SMART, JsonUtils.toJson(queryTransferInApplySignResultSmartReqDTO), QueryTransferInApplySignResultSmartRespDTO.class, settlementPlatAppId);
    }

    @Override
    public QueryBankTransferLimitSmartRespDTO queryBankTransferLimitSmart(QueryBankTransferLimitSmartReqDTO queryBankTransferLimitSmartReqDTO) {
         return requestCommon(SpaObpApiMethodNameConstant.QUERY_BANK_TRANSFER_LIMIT_SMART, JsonUtils.toJson(queryBankTransferLimitSmartReqDTO), QueryBankTransferLimitSmartRespDTO.class, settlementPlatAppId);
    }

    @Override
    public TransferInRespDTO transferIn(TransferInReqDTO transferInReqDTO) {
         return requestCommon(SpaObpApiMethodNameConstant.TRANSFER_IN, JsonUtils.toJson(transferInReqDTO), TransferInRespDTO.class, settlementPlatAppId);
    }

    @Override
    public TransferInResultQueryRespDTO transferInResultQuery(TransferInResultQueryReqDTO transferInResultQueryReqDTO) {
         return requestCommon(SpaObpApiMethodNameConstant.TRANSFER_IN_RESULT_QUERY, JsonUtils.toJson(transferInResultQueryReqDTO), TransferInResultQueryRespDTO.class, settlementPlatAppId);
    }

    @Override
    public QryTransListOutLoginRespDTO qryTransListOutLogin(QryTransListOutLoginReqDTO qryTransListOutLoginReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.QRY_TRANS_LIST_OUT_LOGIN, JsonUtils.toJson(qryTransListOutLoginReqDTO), QryTransListOutLoginRespDTO.class, settlementPlatAppId);
    }

    @Override
    public GetH5FaceIdRespDTO getH5FaceId(GetH5FaceIdReqDTO getH5AceIdReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.GET_H5_FACE_ID, JsonUtils.toJson(getH5AceIdReqDTO), GetH5FaceIdRespDTO.class, settlementPlatAppId);
    }

    @Override
    public GetH5AuthResultRespDTO getH5AuthResult(GetH5AuthResultReqDTO getH5AuthResultReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.GET_H5_AUTH_RESULT, JsonUtils.toJson(getH5AuthResultReqDTO), GetH5AuthResultRespDTO.class, settlementPlatAppId);
    }

    @Override
    public ApplyAccountRefundRespDTO applyAccountRefund(ApplyAccountRefundReqDTO applyAccountRefundReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.APPLY_ACCOUNT_REFUND, JsonUtils.toJson(applyAccountRefundReqDTO), ApplyAccountRefundRespDTO.class, settlementPlatAppId);
    }

    @Override
    public QryRefundStateRespDTO qryRefundState(QryRefundStateReqDTO qryRefundStateReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.QRY_REFUND_STATE, JsonUtils.toJson(qryRefundStateReqDTO), QryRefundStateRespDTO.class, settlementPlatAppId);
    }

    @Override
    public QueryTransactionDetailRespDTO queryTransactionDetail(QueryTransactionDetailReqDTO queryTransactionDetailReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.QUERY_TRANSACTION_DETAIL, JsonUtils.toJson(queryTransactionDetailReqDTO), QueryTransactionDetailRespDTO.class, settlementPlatAppId);
    }

    @Override
    public QueryLinkedAcctDetailDTO queryLinkedAcct(QueryLinkedAcctReqDTO queryLinkedAcctReqDTO) {
        return requestCommon(SpaObpApiMethodNameConstant.QUERY_LINKED_ACCT, JsonUtils.toJson(queryLinkedAcctReqDTO), QueryLinkedAcctDetailDTO.class, settlementPlatAppId);
    }

    /*
     * @MethodName: requestCommon
     * @Description: 公共请求封装
     * @Param: [interfaceName, request]
     * @Return: T
     * @Author: Jarvis.li
     * @Date: 2021/10/15
     **/
    public  <T> T requestCommon(String interfaceName, String request, Class<T> responseT, String key)  {
        T response = null;
        long end = 0L;
        long start = 0L;
        try {
            start = System.currentTimeMillis();
            response =  obpApiClient.request(key, request, responseT, interfaceName);
            end = System.currentTimeMillis();
            return response;
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【平安商户联营卡公共封装请求异常】【接口名{}】【耗时{}ms】【请求报文{}】【返回报文{}】,exception=",interfaceName, end-start, request, JSON.toJSON(response), e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_EXCEPTION);
        }
    }
}
