package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName DownloadReconciliationFileRespDTO
 * @Description: 3.14 下载对账文件
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Data
public class DownloadReconciliationFileRespDTO extends SpaBaseRespDTO {

    /*
     * 生成对账单状态	Y 20
     * 对账单状态(0初始状态,1对账成功,2,对账中,4对账失败
     * CreateBillStatusEnum
     */
    @JsonProperty(value = "CreateBillStatus")
    private String createBillStatus;

    /*
     * 收入笔数	N 10
     * 收入笔数,当status为1时有值
     */
    @JsonProperty(value = "IncomeCount")
    private String incomeCount;

    /*
     * 收入金额	N 20
     * 收入金额,当status为1时有值
     */
    @JsonProperty(value = "IncomeAmt")
    private String incomeAmt;

    /*
     * 退款笔数	N 20
     * 退款笔数,当status为1时有值
     */
    @JsonProperty(value = "ReturnNum")
    private String returnNum;

    /*
     * 退款金额	N 20
     * 退款金额,当status为1时有值
     */
    @JsonProperty(value = "ReturnAmt")
    private String returnAmt;

    /*
     * 撤销笔数	N 10
     * 撤销笔数,当status为1时有值
     */
    @JsonProperty(value = "CancelNum")
    private String cancelNum;

    /*
     * 撤销金额	N 20
     * 撤销金额,当status为1时有值
     */
    @JsonProperty(value = "CancelAmt")
    private String cancelAmt;

    /*
     * 提取码	N 64
     * 提取码,当status为1时有值
     */
    @JsonProperty(value = "DrawCode")
    private String drawCode;

    /*
     * 加密标志	N 1
     * 加密标志,当status为1时有值
     */
    @JsonProperty(value = "EncryptFlag")
    private String encryptFlag;

    /*
     * 解压码	N 64
     * 解压码,当status为1时有值
     */
    @JsonProperty(value = "UnzipCode")
    private String unzipCode;

    /*
     * 文件路径	N 512
     * 文件路径,当status为1时有值
     */
    @JsonProperty(value = "FilePath")
    private String filePath;

}
