package com.fenbeitong.dech.spabank.modules.b2bic.constant;

import com.luastar.swift.base.utils.ObjUtils;

/**
 * <AUTHOR>
 * @Date 2021/4/1
 * @Description 中信接口地址
 */
public class SpaB2BICInterfaceConstant {

    /**
     * 3.3企业单笔资金划转[4004]
     */
    public static final String FUND_TRANSFER = "4004";

    /**
     * 3.5单笔转账指令查询[4005]
     */
    public static final String QUERY_TRANSFER_STATUS = "4005";

    /**
     * 3.9查询账户当日历史交易明细[4013]
     */
    public static final String QUERY_TRADE_DETAILS = "4013";

    /**
     * 3.11支付退票查询[4019]
     */
    public static final String QUERY_RETURN_REMITTANCE_DETAILS = "4019";

    /**
     * 3.2历史单笔PDF回单查询接口[ELC002]
     */
    public static final String QUERY_RECEIPT = "ELC002";
    /**
     * 3.6当日历史回单数据查询接口[ELC009]
     */
    public static final String QUERY_RECEIPT_OF_HISTORY_QUERY = "ELC009";
    /**
     * 3.7单笔或多笔回单PDF合并下载[ELC010]
     */
    public static final String QUERY_RECEIPT_OF_HISTORY_GENERATE = "ELC010";

    /**
     * 3.8历史余额查询[4012]
     */
    public static final String QUERY_HISTORY_BALANCE = "4012";

    public static final String UNABLE_TO_CONFIRM = "GW3002,EBLN00,YQ9999,AFE004,E00008,MA0103";

    public static final String SUCCESS_CODE = "000000";

    public static Boolean isFail(String status) {
        if(ObjUtils.isBlank(status)){
            return true;
        }
        if(status.equals(SUCCESS_CODE)){
            return false;
        }
        return true;
    }
}
