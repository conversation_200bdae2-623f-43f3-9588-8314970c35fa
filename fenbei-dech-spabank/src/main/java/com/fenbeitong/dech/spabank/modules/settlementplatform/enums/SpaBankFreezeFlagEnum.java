package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName SpaBankFreezeFlagEnum
 * @Description: 冻结标识
 * <AUTHOR>
 * @Date 2021/11/3
 **/
public enum SpaBankFreezeFlagEnum {

    FREEZE("5","可提现冻结（会员→担保）"),
    UNFREEZE("6","可提现解冻（担保→会员） "),
    ;

    private String freezeFlag;
    private String desc;

    SpaBankFreezeFlagEnum(String freezeFlag, String desc) {
        this.freezeFlag = freezeFlag;
        this.desc = desc;
    }

    public String getFreezeFlag() {
        return freezeFlag;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isFreeze(String freezeFlag){
        return FREEZE.getFreezeFlag().equals(freezeFlag) ? true:false;
    }
}
