package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MembershipWithdrawCashReqDTO
 * @Description: KFEJZB6033	会员提现-不验证	MembershipWithdrawCash
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MembershipWithdrawCashReqDTO  extends SpaSettlementPlatBaseReqDTO {

    /*
     * 交易网名称	Y 120
     * 平台自定义名称或填写4位的市场代码（MrchCode）
     */
    @JsonProperty(value = "TranWebName")
    private String tranWebName;

    /*
     * 见证子账户的账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 会员证件类型	N 2
     * 见“会员证件类型说明”页
     */
    @JsonProperty(value = "MemberGlobalType")
    private String memberGlobalType;

    /*
     * 会员证件号码	N 20
     */
    @JsonProperty(value = "MemberGlobalId")
    private String memberGlobalId;

    /*
     * 交易网会员代码	Y 32
     *
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 会员名称	Y 120
     */
    @JsonProperty(value = "MemberName")
    private String memberName;

    /*
     * 资金汇总账号	Y 32
     */
    @JsonProperty(value = "FundSummaryAcctNo")
    private String fundSummaryAcctNo;

    /*
     * 提现账号	Y 32
     * 银行卡
     */
    @JsonProperty(value = "TakeCashAcctNo")
    private String takeCashAcctNo;

    /*
     * 出金账户名称	Y 120
     * 银行卡户名
     */
    @JsonProperty(value = "OutAmtAcctName")
    private String outAmtAcctName;

    /*
     * 币种	Y 3
     * 默认为RMB
     */
    @JsonProperty(value = "Ccy")
    private String ccy = "RMB";

    /*
     * 可提现金额	Y 15
     * 不包含手续费，实际到账金额=申请提现的金额
     */
    @JsonProperty(value = "CashAmt")
    private String cashAmt;

    /*
     * 备注	N 120
     * 建议可送订单号，可在对账文件的备注字段获取到。
     */
    @JsonProperty(value = "remark")
    private String Remark;

    /*
     * 手续费	N 120
     * 提现手续费，格式0.00
     */
    @JsonProperty(value = "ReservedMsg")
    private String ReservedMsg;

    /*
     * 网银签名	N 256
     *
     */
    @JsonProperty(value = "WebSign")
    private String webSign;
}
