package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MemberBindQueryReqDTO
 * @Description: KFEJZB6098	会员绑定信息查询	MemberBindQuery
 * <AUTHOR>
 * @Date 2022/08/01
 **/
@Data
public class MemberBindQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 1：全部会员
     * 2：单个会员"
     */
    @JsonProperty(value = "QueryFlag")
    private String queryFlag;

    /*
     * 子账户账号	Y 32
     * 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”(右侧)进行分隔。
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 页码
     */
    @JsonProperty(value = "PageNum")
    private String ppageNum;

    /*
     * 保留域	N 120
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
