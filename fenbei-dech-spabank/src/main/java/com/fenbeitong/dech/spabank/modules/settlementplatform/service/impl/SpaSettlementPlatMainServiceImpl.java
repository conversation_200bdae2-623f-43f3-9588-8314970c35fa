package com.fenbeitong.dech.spabank.modules.settlementplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.req.BindLinkedAccountReqDTO;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.BindLinkedAccountRespDTO;
import com.fenbeitong.dech.spabank.modules.settlementplatform.client.SettlementPlatformClient;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req.*;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp.*;
import com.fenbeitong.dech.spabank.modules.settlementplatform.enums.SpaSettlementPlatTxnCodeEnum;
import com.fenbeitong.dech.spabank.modules.settlementplatform.service.SpaSettlementPlatMainService;
import com.fenbeitong.dech.spabank.utils.SpaSignUtil;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @ClassName SpaSettlementPlatMainServiceImpl
 * @Description: 结算通基础service
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Service
public class SpaSettlementPlatMainServiceImpl implements SpaSettlementPlatMainService {

    @Autowired
    private SettlementPlatformClient settlementPlatformClient;

    @Value("${spaBank.settlementPlat.appId}")
    private String settlementPlatAppId;

    @Override
    public AutonymOpenCustAcctIdRespDTO autonymOpenCustAcctId(AutonymOpenCustAcctIdReqDTO autonymOpenCustAcctIdReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.AUTONYM_OPEN_CUST_ACCT_ID, JsonUtils.toJson(autonymOpenCustAcctIdReqDTO), AutonymOpenCustAcctIdRespDTO.class);
    }

    @Override
    public BankWithdrawCashBackQueryRespDTO bankWithdrawCashBackQuery(BankWithdrawCashBackQueryReqDTO bankWithdrawCashBackQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.BANK_WITHDRAW_CASH_BACK_QUERY, JsonUtils.toJson(bankWithdrawCashBackQueryReqDTO), BankWithdrawCashBackQueryRespDTO.class);
    }

    @Override
    public BindSmallAmountWithCheckCorpRespDTO bindSmallAmountWithCheckCorp(BindSmallAmountWithCheckCorpReqDTO bindSmallAmountWithCheckCorpReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.BIND_SMALL_AMOUNT_WITH_CHECK_CORP, JsonUtils.toJson(bindSmallAmountWithCheckCorpReqDTO), BindSmallAmountWithCheckCorpRespDTO.class);
    }
    
    @Override
    public MemberInformationChangeRespDTO updateCompanyInfor(MemberInformationChangeRequestDTO request) {
    	return requestCommon(SpaSettlementPlatTxnCodeEnum.MEMBER_INFORMAT_CHANGE, JsonUtils.toJson(request), MemberInformationChangeRespDTO.class);
    }

    @Override
    public CheckAmountWithCorpRespDTO checkAmountWithCorp(CheckAmountWithCorpReqDTO checkAmountWithCorpReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.CHECK_AMOUNT_WITH_CORP, JsonUtils.toJson(checkAmountWithCorpReqDTO), CheckAmountWithCorpRespDTO.class);
    }

    @Override
    public DetailVerifiedCodeQueryRespDTO detailVerifiedCodeQuery(DetailVerifiedCodeQueryReqDTO detailVerifiedCodeQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.DETAIL_VERIFIED_CODE_QUERY, JsonUtils.toJson(detailVerifiedCodeQueryReqDTO), DetailVerifiedCodeQueryRespDTO.class);
    }
//
//    @Override
//    public MembershipTrancheFreezeRespDTO membershipTrancheFreeze(MembershipTrancheFreezeReqDTO membershipTrancheFreezeReqDTO) {
//        return requestCommon(SpaSettlementPlatTxnCodeEnum.MEMBERSHIP_TRANCHE_FREEZE, JsonUtils.toJson(membershipTrancheFreezeReqDTO), MembershipTrancheFreezeRespDTO.class);
//    }
//
//    @Override
//    public MembershipTranchePayRespDTO membershipTranchePay(MembershipTranchePayReqDTO membershipTranchePayReqDTO) {
//        return requestCommon(SpaSettlementPlatTxnCodeEnum.MEMBERSHIP_TRANCHE_PAY, JsonUtils.toJson(membershipTranchePayReqDTO), MembershipTranchePayRespDTO.class);
//    }

    @Override
    public MembershipWithdrawCashRespDTO membershipWithdrawCash(MembershipWithdrawCashReqDTO membershipWithdrawCashReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.MEMBERSHIP_WITHDRAW_CASH, JsonUtils.toJson(membershipWithdrawCashReqDTO), MembershipWithdrawCashRespDTO.class);
    }

    @Override
    public MemberTransactionRefundRespDTO memberTransactionRefund(MemberTransactionRefundReqDTO memberTransactionRefundReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.MEMBER_TRANSACTION_REFUND, JsonUtils.toJson(memberTransactionRefundReqDTO), MemberTransactionRefundRespDTO.class);
    }

    @Override
    public MemberTransactionRespDTO memberTransaction(MemberTransactionReqDTO memberTransactionReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.MEMBER_TRANSACTION, JsonUtils.toJson(memberTransactionReqDTO), MemberTransactionRespDTO.class);
    }

    @Override
    public QueryCustAcctIdRespDTO queryCustAcctId(QueryCustAcctIdReqDTO queryCustAcctIdReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.QUERY_CUST_ACCT_ID, JsonUtils.toJson(queryCustAcctIdReqDTO), QueryCustAcctIdRespDTO.class);
    }

    @Override
    public ReconciliationDocumentQueryRespDTO reconciliationDocumentQuery(ReconciliationDocumentQueryReqDTO reconciliationDocumentQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.RECONCILIATION_DOCUMENT_QUERY, JsonUtils.toJson(reconciliationDocumentQueryReqDTO), ReconciliationDocumentQueryRespDTO.class);
    }

    @Override
    public SingleTransactionStatusQueryRespDTO singleTransactionStatusQuery(SingleTransactionStatusQueryReqDTO singleTransactionStatusQueryQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.SINGLE_TRANSACTION_STATUS_QUERY, JsonUtils.toJson(singleTransactionStatusQueryQueryReqDTO), SingleTransactionStatusQueryRespDTO.class);
    }

    @Override
    public SupAcctIdBalanceQueryRespDTO supAcctIdBalanceQuery(SupAcctIdBalanceQueryReqDTO supAcctIdBalanceQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.SUP_ACCT_ID_BALANCE_QUERY, JsonUtils.toJson(supAcctIdBalanceQueryReqDTO), SupAcctIdBalanceQueryRespDTO.class);
    }

    @Override
    public UnbindRelateAcctRespDTO unbindRelateAcct(UnbindRelateAcctReqDTO unbindRelateAcctReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.UNBIND_RELATE_ACCT, JsonUtils.toJson(unbindRelateAcctReqDTO), UnbindRelateAcctRespDTO.class);
    }

    @Override
    public MntMbrBindRelateAcctBankCodeRespDTO mntMbrBindRelateAcctBankCode(MntMbrBindRelateAcctBankCodeReqDTO mntMbrBindRelateAcctBankCodeReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.MNT_MBR_BIND_RELATE_ACCT_BANK_CODE, JsonUtils.toJson(mntMbrBindRelateAcctBankCodeReqDTO), MntMbrBindRelateAcctBankCodeRespDTO.class);
    }

    @Override
    public MemberBindQueryRespDTO memberBindQuery(MemberBindQueryReqDTO memberBindQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.MEMBER_BIND_QUERY, JsonUtils.toJson(memberBindQueryReqDTO), MemberBindQueryRespDTO.class);
    }

    @Override
    public RegisterBehaviorRecordInfoRespDTO registerBehaviorRecordInfo(RegisterBehaviorRecordInfoReqDTO registerBehaviorRecordInfoReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.REGISTER_BEHAVIOR_RECORD_INFO, JsonUtils.toJson(registerBehaviorRecordInfoReqDTO), RegisterBehaviorRecordInfoRespDTO.class);
    }

    @Override
    public RegisterBillRespDTO registerBillSupportWithdraw(RegisterBillReqDTO registerBillReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.REGISTER_BILL_SUPPORT_WITHDRAW, JsonUtils.toJson(registerBillReqDTO), RegisterBillRespDTO.class);
    }

    @Override
    public RevRegisterBillRespDTO revRegisterBillSupportWithdraw(RevRegisterBillReqDTO revRegisterBillReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.REV_REGISTER_BILL_SUPPORT_WITHDRAW, JsonUtils.toJson(revRegisterBillReqDTO), RevRegisterBillRespDTO.class);
    }

    @Override
    public SmallAmountTransferQueryRespDTO smallAmountTransferQuery(SmallAmountTransferQueryReqDTO smallAmountTransferQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.SMALL_AMOUNT_TRANSFER_QUERY, JsonUtils.toJson(smallAmountTransferQueryReqDTO), SmallAmountTransferQueryRespDTO.class);
    }

    @Override
    public ChargeDetailQueryRespDTO chargeDetailQuery(ChargeDetailQueryReqDTO chargeDetailQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.CHARGE_DETAIL_QUERY, JsonUtils.toJson(chargeDetailQueryReqDTO), ChargeDetailQueryRespDTO.class);
    }

    @Override
    public AccountRegulationRespDTO accountRegulation(AccountRegulationReqDTO accountRegulationReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.ACCOUNT_REGULATION, JsonUtils.toJson(accountRegulationReqDTO), AccountRegulationRespDTO.class);
    }

    @Override
    public PlatformAccountSupplyRespDTO platformAccountSupply(PlatformAccountSupplyReqDTO platformAccountSupplyReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.PLATFORM_ACCOUNT_SUPPLY, JsonUtils.toJson(platformAccountSupplyReqDTO), PlatformAccountSupplyRespDTO.class);
    }

    @Override
    public CustAcctIdBalanceQueryRespDTO custAcctIdBalanceQuery(CustAcctIdBalanceQueryReqDTO custAcctIdBalanceQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.CUST_ACCT_ID_BALANCE_QUERY, JsonUtils.toJson(custAcctIdBalanceQueryReqDTO), CustAcctIdBalanceQueryRespDTO.class);
    }

    @Override
    public BankTransDetailsQueryRespDTO bankTransactionDetailsQuery(BankTransDetailsQueryReqDTO bankTransDetailsQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.BANK_TRANSACTION_DETAILS_QUERY, JsonUtils.toJson(bankTransDetailsQueryReqDTO), BankTransDetailsQueryRespDTO.class);
    }

    @Override
    public CommonTransRechargeQueryRespDTO commonTransferRechargeQuery(CommonTransRechargeQueryReqDTO commonTransRechargeQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.COMMON_TRANSFER_RECHARGE_QUERY, JsonUtils.toJson(commonTransRechargeQueryReqDTO), CommonTransRechargeQueryRespDTO.class);
    }

    @Override
    public BindUnionPayWithCheckCorpRespDTO bindUnionPayWithCheckCorp(BindUnionPayWithCheckCorpReqDTO bindUnionPayWithCheckCorpReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.BIND_UNION_PAY_WITH_CHECK_CORP, JsonUtils.toJson(bindUnionPayWithCheckCorpReqDTO), BindUnionPayWithCheckCorpRespDTO.class);

    }

    @Override
    public CheckMsgCodeWithCorpRespDTO checkMsgCodeWithCorp(CheckMsgCodeWithCorpReqDTO checkMsgCodeWithCorpReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.CHECK_MSG_CODE_WITH_CORP, JsonUtils.toJson(checkMsgCodeWithCorpReqDTO), CheckMsgCodeWithCorpRespDTO.class);
    }

    @Override
    public BindLinkedAccountRespDTO bindLinkedAccount(BindLinkedAccountReqDTO bindLinkedAccountReqDTO) {
        bindLinkedAccountReqDTO.setTraderCode(SpaSignUtil.convertAppId(settlementPlatAppId));
        return requestCommon(SpaSettlementPlatTxnCodeEnum.BIND_TWO_LEVEL_OF_ACCOUNT, JsonUtils.toJson(bindLinkedAccountReqDTO), BindLinkedAccountRespDTO.class);
    }

    @Override
    public ApplyForChangeOfCellPhoneNumRespDTO applyForChangeOfCellPhoneNum(ApplyForChangeOfCellPhoneNumReqDTO applyForChangeOfCellPhoneNumReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.APPLY_FOR_CHANGE_OF_CELL_PHONE_NUM, JsonUtils.toJson(applyForChangeOfCellPhoneNumReqDTO), ApplyForChangeOfCellPhoneNumRespDTO.class);
    }

    @Override
    public BackFillDynamicPasswordRespDTO backFillDynamicPassword(BackFillDynamicPasswordReqDTO backFillDynamicPasswordReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.BACK_FILL_DYNAMIC_PASSWORD, JsonUtils.toJson(backFillDynamicPasswordReqDTO), BackFillDynamicPasswordRespDTO.class);
    }

    @Override
    public EJZBCustInfomationQueryRespDTO eJZBCustInformationQuery(EJZBCustInfomationQueryReqDTO ejzbCustInfomationQueryReqDTO) {
        return requestCommon(SpaSettlementPlatTxnCodeEnum.EJZB_CUST_INFORMATION_QUERY, JsonUtils.toJson(ejzbCustInfomationQueryReqDTO), EJZBCustInfomationQueryRespDTO.class);
    }

    /**
     * @MethodName: requestCommon
     * @Description: 公共请求封装
     * @Param: [interfaceName, request]
     * @Return: T
     * @Author: Jarvis.li
     * @Date: 2021/10/15
     */
    public <T> T requestCommon(SpaSettlementPlatTxnCodeEnum interfaceEnum, String request, Class<T> responseT) {
        String response = null;
        long end = 0L;
        long start = 0L;
        try {
            start = System.currentTimeMillis();
            response = settlementPlatformClient.request(request, interfaceEnum.getTxnCode(), interfaceEnum.getServiceId());
            end = System.currentTimeMillis();
            SpaSettlementPlatBaseRespDTO baseRespDTO = JSON.parseObject(response, SpaSettlementPlatBaseRespDTO.class);
            if(ObjUtils.isEmpty(baseRespDTO.getErrors())){
                return JSON.parseObject(baseRespDTO.getData(), responseT);
            } else {
                FinhubLogger.info("【平安-结算通公共封装请求失败】【接口名{}】【耗时{}ms】【请求报文{}】【{}】",interfaceEnum.getServiceId(), end-start, JSON.toJSON(request), baseRespDTO.getErrors().get(0).getErrorMessage());
                return JSON.parseObject(response, responseT);
            }
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【平安-结算通公共封装请求异常】【接口名{}】【耗时{}ms】【请求报文{}】【返回报文{}】,exception=",interfaceEnum.getServiceId(), end-start, JSON.toJSON(request), JSON.toJSON(response), e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_CLOUD_RECEIPT_EXCEPTION);
        }
    }

}
