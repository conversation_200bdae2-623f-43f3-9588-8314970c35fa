package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @ClassName SpaQueryReceiptRespDTO
 * @Description: 3.2历史单笔PDF回单查询接口[ELC002]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaHistoryReceiptGenerateRespDTO extends SpaB2BICBaseRespDTO{

    /**
     * 		文件名称	C(30)	必输	回单文件名
     */
    private String FileName;

    /**
     * 		随机密码	C(200)	必输
     */
    private String RandomPwd;

    /**
     * 	记录起始值
     */
    private String ReturnBeginNo;

    /**
     *  记录数
     */
    private String ResultNum;
    /**
     * 本次是否全部完成
     */
    private String EndFlag;

}
