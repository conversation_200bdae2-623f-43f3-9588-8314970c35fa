package com.fenbeitong.dech.spabank.modules.obpApi.constant;

import org.apache.commons.lang3.StringUtils;

public class SpaObpApiCommonConstant {

    public static final String RESPONSE_CODE_SUCCESS = "000000";

    public static final String RESPONSE_CODE_******** = "********";
    /**
     * 开户状态查询，银行返回错误状态码，实际还是表示处理中
     */
    public static final String RESPONSE_CODE_******** = "********";
    public static final String RESPONSE_CODE_******** = "********";
    public static final String RESPONSE_CODE_******** = "********";

    public static final String RESPONSE_CODE_******** = "********";

    public static final String BUSINESS_NO_PFX = "OB";

    public static final String IMAGE_CHECK_PROCESSING = "processing";

    public static final String IMAGE_CHECK_FAILED = "failed";

    public static final String IMAGE_CHECK_SUCCEEDED = "succeeded";

    public static final String BANK_CARD_TYPE_I = "1";

    public static final String BANK_CARD_TYPE_II = "2";

    public static final String BANK_LINKED_ACCOUNT_ERROR = "绑定II、III类户失败";

    public static final String SEND_OTP_SCENE_QUERY_BALANCE = "QB";
    public static final String SEND_OTP_SCENE_TO_TRANSFER = "BC001";
    public static final String SEND_OTP_SCENE_TO_TRANSFER_IN = "TI001";

    public static Boolean isSuccess(String status) {
        if (StringUtils.isNotBlank(status)) {
            return RESPONSE_CODE_SUCCESS.equals(status);
        }
        return false;
    }


}
