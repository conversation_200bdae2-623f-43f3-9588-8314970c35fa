package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName BookingFlagEnum
 * @Description: 记账标志
 * <AUTHOR>
 * @Date 2021/10/15
 **/
public enum BookingFlagEnum {

    /**
     * 记账标志（1：登记挂账 2：支付 3：提现 4：清分5:下单预支付 6：确认并付款 7：退款 8：支付到平台 N:其他）
     */
    REGISTER_CHARGE("1","登记挂账"),
    PAYMENT("2","支付"),
    WITHDRAWAL("3","提现"),
    SORTING("4","清分"),
    ORDER_ADVANCE_PAYMENT("5","下单预支付"),
    CONFIRM_PAY("6","确认并付款"),
    REFUND("7","退款"),
    PAY_TO_PLATFORM("8","支付到平台"),
    OTHER("N","其他")
    ;

    BookingFlagEnum(String flag, String desc){
        this.flag = flag;
        this.desc = desc;
    }

    private String flag;
    private String desc;

    public String getFlag() {
        return flag;
    }

    public String getDesc() {
        return desc;
    }

}
