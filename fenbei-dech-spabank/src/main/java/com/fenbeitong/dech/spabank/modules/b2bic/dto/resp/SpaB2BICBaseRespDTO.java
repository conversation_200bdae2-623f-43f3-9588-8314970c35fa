package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaFundTransferRespDTO
 * <AUTHOR>
 * @Date 2022/1/10
 **/
@Data
public class SpaB2BICBaseRespDTO implements Serializable {

    private String code;

    private String message;

    public void buildSuccessCode(){
        code = "000000";
    }

}
