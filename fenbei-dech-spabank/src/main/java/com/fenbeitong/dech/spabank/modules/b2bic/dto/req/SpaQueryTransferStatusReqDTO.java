package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryTransferStatusReqDTO
 * @Description: 3.5单笔转账指令查询[4005]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryTransferStatusReqDTO implements Serializable {

    /**
     * 转账凭证号 c(20)
     * 推荐使用；
     * 使用4004接口上送的ThirdVoucher或者4014上送的SThirdVoucher
     */
    private String OrigThirdVoucher;

    /**
     * 银行流水号 c(32)
     * 不推荐使用；银行返回的转账流水号
     */
    private String OrigFrontLogNo;

}
