package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName CheckAmountWithCorpRespdto
 * @Description: KFEJZB6241	小额鉴权回填金额-校验法人	CheckAmountWithCorp
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class CheckMsgCodeWithCorpRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 前置流水号	Y 16
     * 系统返回的前置流水号
     */
    @JsonProperty(value = "FrontSeqNo")
    private String frontSeqNo;

    /*
     * 保留域	Y 4
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
