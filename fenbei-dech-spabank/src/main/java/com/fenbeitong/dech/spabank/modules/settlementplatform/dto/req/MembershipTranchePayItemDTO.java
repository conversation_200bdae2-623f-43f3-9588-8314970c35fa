package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MembershipTranchePayReqDTO
 * @Description: KFEJZB6163	会员资金支付-不验证	MembershipTranchePay
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MembershipTranchePayItemDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 交易流水号	Y 20
     * （用于退款处理）（自定义）
     */
    @JsonProperty(value = "TranSeqNo")
    private String tranSeqNo;

    /*
     * 转入见证子账户的账号	Y 32
     */
    @JsonProperty(value = "InSubAcctNo")
    private String inSubAcctNo;

    /*
     * 转入交易网会员代码	Y 32
     */
    @JsonProperty(value = "InTranNetMemberCode")
    private String inTranNetMemberCode;

    /*
     * 交易金额	Y 15
     * 转入子账号的到账金额，不包含手续费
     */
    @JsonProperty(value = "TranAmt")
    private String tranAmt;
}
