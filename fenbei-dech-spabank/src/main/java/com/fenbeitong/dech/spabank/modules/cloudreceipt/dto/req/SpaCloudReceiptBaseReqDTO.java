package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/14
 * @Description
 */
@Data
public class SpaCloudReceiptBaseReqDTO implements Serializable {

    /*
     * 商户编号	Y 32
     * 商户在云收款系统的编号
     */
    @JsonProperty(value = "TraderNo")
    private String traderNo;

    /*
     * 系统流水号	Y 32
     * 必填 规范：6位uid(文件传输用户短号)+6位系统日期(YYMMDD)+10位随机数
     */
    @JsonProperty(value = "CnsmrSeqNo")
    private String cnsmrSeqNo;

}
