package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:50:58
 * @Version 1.0
 **/
@Data
public class TransferInRespDTO  extends SpaBankObpApiBaseRespDTO{
    // errCode String  代扣错误码
    private String errCode;
    // errMsg String  失败信息  代扣失败的原因
    private String errMsg;
    // orderNo String  流水号
    private String orderId;
    // orderStatus String  指令状态  指令状态 0:初始值,1:已提交,2:未知,3:失败,4:成功。orderStatus不等于3或4都需要反查“入金(代扣)结果查询”
    private String orderStatus;
}
