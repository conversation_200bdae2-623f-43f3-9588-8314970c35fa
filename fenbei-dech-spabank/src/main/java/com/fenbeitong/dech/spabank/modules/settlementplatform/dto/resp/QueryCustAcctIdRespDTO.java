package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName QueryCustAcctIdRespDTO
 * @Description: KFEJZB6037 查询会员子账号 QueryCustAcctId
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class QueryCustAcctIdRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 见证子账户的账号	Y 32
     * 交易网会员代码
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 见证子账户可提现余额	Y 15
     * 见证子账户可提现余额
     */
    @JsonProperty(value = "SubAcctCashBal")
    private String subAcctCashBal;

    /*
     * 见证子账户可用余额	Y 15
     * 见证子账户可用余额
     */
    @JsonProperty(value = "SubAcctAvailBal")
    private String subAcctAvailBal;

    /*
     * 见证子账户冻结金额	Y 15
     * 见证子账户冻结金额
     */
    @JsonProperty(value = "SubAcctFreezeAmt")
    private String subAcctFreezeAmt;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
