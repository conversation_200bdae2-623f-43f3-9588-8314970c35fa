package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName MemberGlobalTypeEnum
 * @Description: 接口证件类型说明
 * <AUTHOR>
 * @Date 2021/10/26
 **/
public enum MemberGlobalTypeEnum {
    /**
     * 52	组织机构代码证
     * 68	营业执照
     * 73	统一社会信用代码
     * 1	身份证
     * 3	港澳台居民通行证（即回乡证）
     * 4	中国护照
     * 5	台湾居民来往大陆通行证（即台胞证）
     * 19	外国护照
     */
    ORGANIZATION_CODE_CERTIFICATE("52","组织机构代码证"),
    BUSINESS_LICENSE("68","营业执照"),
    UNIFIED_SOCIAL_CREDIT_CODE("73","统一社会信用代码"),
    ID_CARD("1","身份证"),
    RE_ENTRY_PERMIT("3","港澳台居民通行证（即回乡证）"),
    CHINESE_PASSPORT("4","中国护照"),
    TAIWANESE_SYNDROME("5","台湾居民来往大陆通行证（即台胞证）"),
    FOREIGN_PASSPORT("19","外国护照"),
    ;

    private String type;
    private String desc;

    MemberGlobalTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
