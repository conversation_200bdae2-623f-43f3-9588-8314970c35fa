package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName RegisterBehaviorRecordInfoRespDTO
 * @Description: KFEJZB6244	登记行为记录信息	RegisterBehaviorRecordInfo
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class RegisterBehaviorRecordInfoRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 补录是否成功标志	Y 16
     * 功能标志为2时必输，S-成功 F-失败
     */
    @JsonProperty(value = "ReinSuccessFlag")
    private String reinSuccessFlag;

    /*
     * 单位名称	Y 16
     * 功能标志为2时且商户为非自然人必输。
     */
    @JsonProperty(value = "CompanyName")
    private String companyName;

    /*
     * 公司证件类型	Y 16
     * 功能标志为2时且商户为非自然人必输。73-统一社会信用代码证号
     */
    @JsonProperty(value = "CompanyGlobalType")
    private String companyGlobalType;

    /*
     * 公司证件号码	Y 16
     * 功能标志为2时且商户为非自然人必输。
     */
    @JsonProperty(value = "CompanyGlobalId")
    private String companyGlobalId;

    /*
     * 法人名称	Y 16
     * 功能标志为2时且商户为非自然人必输。
     */
    @JsonProperty(value = "ReprName")
    private String reprName;

    /*
     * 法人证件类型	Y 16
     * 功能标志为2时且商户为非自然人必输。1-身份证号
     */
    @JsonProperty(value = "ReprGlobalType")
    private String reprGlobalType;

    /*
     * 法人证件号码	Y 16
     * 功能标志为2时且商户为非自然人必输。
     */
    @JsonProperty(value = "ReprGlobalId")
    private String reprGlobalId;

    /*
     * 保留域	Y 4
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
