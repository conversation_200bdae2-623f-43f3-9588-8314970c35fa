package com.fenbeitong.dech.spabank.modules.b2bic.client;

import com.fenbeitong.dech.spabank.modules.b2bic.Exception.CodeAndMsgException;
import com.fenbeitong.dech.spabank.modules.b2bic.Exception.InvalidDataException;
import com.fenbeitong.dech.spabank.modules.b2bic.Http.HttpRspVo;
import com.fenbeitong.dech.spabank.modules.b2bic.Sign.SignFactory;
import com.fenbeitong.dech.spabank.modules.b2bic.Sign.sign.ISign;
import com.fenbeitong.dech.spabank.modules.b2bic.Util.Service;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.log4j.xml.DOMConfigurator;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.*;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fenbeitong.dech.spabank.modules.b2bic.Util.StringTool.fillChar;
import static com.fenbeitong.dech.spabank.modules.b2bic.Util.YQUtil.*;

/**
 *      <h1>概述</h1>
 *      <ol>
 *      <li>SDK工具类入口方法，调用本类sendTobank静态方法进行输入校验，和银行通讯，并实时返回结果。</li>
 *      <li>与银行服务端之间采用HTTPS加密传输，交易数据采用客户证书签名，同时服务端验证数据一致性和客户的身份，可以确保通讯传输的安全，交易数据无法被篡改。</li>
 *      <li>与银行通讯时，验证服务端证书公钥，可以确保银行的身份，以防假冒的服务提供者。</li>
 *      <li>最大支持50个文件证书签名并发。</li>
 *      <li>若校验模板文件不存在，不进行校验，直接转发。</li>
 *      <li>暂只接受Map报文体，并返回Map报文体,不支持携带附件。</li>
 *      <li>暂只支持A001规范封装报文。</li>
 *      <li>与银行上行通讯暂只支持HTTPS协议。</li>
 *      <li>暂不支持FTP服务。</li>
 *      </ol>
 *      <hr>
 *      <h1>依赖五个文件夹</h1>
 *      <ol>
 *      <li>配置文件，程序目录configuration文件夹下。</li>
 *      <li>日志文件，程序目录log文件夹下。</li>
 *      <li>证书文件，程序目录cert文件夹下。</li>
 *      <li>主包及依赖包，程序目录lib文件夹下。</li>
 *      <li>校验模板文件，程序目录template文件夹下。</li>
 *      <li>国密证书签名日志文件，程序目录signstore文件夹下。</li>
 *      </ol>
 *      <hr>
 *      <h1>配置文件说明</h1>
 *      <h2>日志配置文件，log4j.xml</h2>
 *      <ol>
 *      <li>level value="debug"            ---- 默认日志打印级别，日志级别划分为：debug、info、warn、error、fatal五个级别，debug级别日志输出最详细，fatal级别日志输出最简略。</li>
 *      <li>com.pingan.b2bic.B2BICUtils    ---- 该节点需保持打开，保持info级别。</li>
 *      <li>com.pingan.b2bic.Util          ---- 该节点需保持打开，保持info级别。</li>
 *      <li>其余节点配置建议保持默认。</li>
 *      <li>按天生成，API使用如有问题请提供当天日志文件。</li>
 *      </ol>
 *      <h2>签名配置文件，cfgsign.xml</h2>
 *      <ol>
 *      <li>signMode                       ---- 签名模式，支持RSA_SOFT文件证书和SM2_SOFT国密文件证书。</li>
 *      <li>pfxPath                        ---- 证书路径。</li>
 *      <li>pfxPwd                         ---- 证书密码。</li>
 *      <li>caCertPath                     ---- ca证书路径，指向工作目录cert文件夹ebankprd.p7b文件。</li>
 *      <li>isUpSignAllTx                  ---- 全部交易签名，保持默认。</li>
 *      <li>其他请保持默认。</li>
 *      </ol>
 *      <h2>国密证书签名配置文件，netsign.properties</h2>
 *      <ol>
 *      <li>请保持默认。</li>
 *      </ol>
 *      <h2>上行配置文件，cfgbank.xml</h2>
 *      <ol>
 *      <li>ips                            ---- 上行交易行方服务器IP地址，行方提供。</li>
 *      <li>ports                              ----银行服务端端口，行方提供。</li>
 *      <li>测试环境：域名my-uat1.orangebank.com.cn(**************)，端口462/466，建议配置域名+端口。</li>
 *      <li>生产环境：域名ebank.sdb.com.cn	(**************、*************)，端口469，建议配置域名+端口。</li>
 *      <li>其他请保持默认。</li>
 *      </ol>
 *      <hr>
 *      <h1>主jar包：middleware.jar</h1>
 *      <h2>依赖包及版本</h2>
 *      <ol>
 *      <li>jdk1.6.0_45_x64</li>
 *      <li>dom4j-1.6.1.jar</li>
 *      <li>commons-logging-1.1.1.jar</li>
 *      <li>CFCACertKitJS.jar</li>
 *      <li>PKIBASE.jar</li>
 *      <li>bcmail-jdk14-146.jar</li>
 *      <li>bcprov-jdk14-146.jar</li>
 *      <li>ISFJ_v2_0_139_15_BAISC_JDK15.jar</li>
 *      <li>com.sun.jna.jar</li>
 *      <li>org_apache_commons_configuration.jar</li>
 *      <li>edu_emory_mathcs_backport_java_util_concurrent.jar</li>
 *      <li>org_apache_commons_httpclient.jar</li>
 *      <li>netsign18.jar</li>
 *      <li>netsignderutil.jar</li>
 *      </ol>
 *
 * <AUTHOR>
 */
public class B2BICUtils {

    private static final Log log = LogFactory.getLog(B2BICUtils.class);

    static {
        DOMConfigurator.configureAndWatch("./configuration/log4j.xml");
    }

    
    public static void main(String[] args) {

        try {
                String test2 = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                        "<Result>\n" +
                        "    <ThirdVoucher>360102c3-96ed-440a-a5e7-9a788af8d78e</ThirdVoucher>\n" +
                        "    <CcyCode>RMB</CcyCode>\n" +
                        "    <OutAcctNo>15000100714429</OutAcctNo>\n" +
                        "    <OutAcctName>前端数据支持测试北京拓诊有限公司</OutAcctName>\n" +
                        "    <InAcctNo>6228480890267788514</InAcctNo>\n" +
                        "    <InAcctName>杨枫</InAcctName>\n" +
                        "    <TranAmount>1.00</TranAmount>\n" +
                        "    <AddrFlag>1</AddrFlag>\n" +
                        "</Result>";
                
                String signStr=sign(test2.getBytes("GBK"),"GBK","SPABANK/cert/testrsa2020_4_10.pfx","********");
                System.out.println(signStr);
                Map map = sendTobank(xml2map(test2), "01","02", "4004", "00901080000001111000","reqMsg=xxx|ZuID=xxxxA","SPABANK/cert/testrsa2020_4_10.pfx","********");
                System.out.println("map = " + map);
//                
//                Map map2 = sendTobank(xml2map(xmlFile), "01","02", "4004", "00401079800001002000","cert/testrsa2020_4_10.pfx","********");
//                System.out.println("map = " + map2);
                
        }catch (Exception e){
            e.getMessage();
        }
    }

    private B2BICUtils() {
    }

    /**
     * 此方法为向银行端请求使用，统一采用A001报文规范自动拼接报文头，不支持附件，请求返回报文体均为Map
     * <P>
     * 示例  系统ID为01，编码格式为01(GBK)，交易代码为4004，企业代码为********900987654321
     * <P>
     * Map result = sendTobank( body, "01", "01", "4004", "********900987654321")
     * <P>
     *
     * <AUTHOR>
     * @param  body       报文体，Map，以下使用toString方法打印，非实际实参,一级value支持List<br>
     *         <p>
     *         <i>示例一，key为String，部分value为List</i>
     *         <p>
     *         {HOResultSet4018R=<br>
     *                   [{SThirdVoucher=yyyyMMdd4018SV001, CstInnerFlowNo=yyyyMMdd4018CIF001},<br>
     *                   {SThirdVoucher=yyyyMMdd4018SV001, CstInnerFlowNo=yyyyMMdd4018CIF001}],<br>
     *          ThirdVoucher=ZXL4018yyyyMMddTV001,<br>
     *          totalCts=1}<br>
     *         <p>
     *          <i>示例二，key为String，value为String</i>
     *         <p>
     *         {CcyCode=RMB,<br>
     *         Account=**************}<br>
     *         <P>
     * @param  systemId   交易代码  2位，01 银企直联
     * @param  systemId   编码格式  2位，01 GBK 02 UTF-8
     * @param  trancode   交易代码  不超过6位
     * @param  yqdm       银企代码  不超过20位
     *         <hr>
     * @return          包含报文头（一定返回，键为head，值为Map）和报文体（错误不返回，键为body，值为Map）
     *         <p>
     *         <i>示例</i>
     *         <p>
     *         {head={systemId=01, signTag=0, errMsg=:交易受理成功
     *         , tradeCode=4001  , packetNo=000, tradeDate=********, encoding=01, corpCode=00901079800000036000, signDataLen=**********, operator=12345, signFormat=1, reqSn=YQTEST2018, mode=02, tradeTime=185610, protocol=01, attachNum=0,
     *         dataBodyLen=**********, errCode=000000, toContinue=00, signAlgorithm=            },<br>
     *         body={BankName=, Account=**************, CcyType=, AccountStatus=A, HoldBalance=0.00, LastBalance=********.34, TotalAmount=***********.05, CcyCode=RMB, Balance=***********.05, AccountType=, AccountName=辽宁嘉禾河韦力电缆股份有限公司}}
     *         </P>
     * @since           2018/4/27
     */
    public static Map sendTobank(Map body, String systemId, String encoding, String trancode, String yqdm, String reqMsg, String pfxPath, String pfxPwd) {

        Service service = Service.getInstance();
        String threadId = service.nextSn();

        FinhubLogger.info("["+threadId+"]"+"Call interface start..." );
        FinhubLogger.info("["+threadId+"]"+"trancode= " + trancode);
        FinhubLogger.info("["+threadId+"]"+"yqdm = " + yqdm);
        FinhubLogger.info("["+threadId+"]"+"System Id= " + systemId);
        FinhubLogger.info("["+threadId+"]"+"Charseset= " + encoding);


        //encoding不合法强制置为01 GBK
        if(encoding==null||encoding.equals("")||encoding.length()!=2){encoding="01";}
        String encode = "GBK";
        if (encoding.equals("02")){
            encode="UTF-8";
        } else if (encoding.equals("03")) {
            encode="unicode";
        } else if (encoding.equals("04")) {
            encode="iso-8859-1";
        }
        if(encode.equals("GBK")){encoding="01";}

        //systemId不合法强制置为01 银企直连
        if(systemId==null||systemId.equals("")||systemId.length()!=2){systemId = "01";}

        //最终返回Map，一定有mapHead，不成功无mapbody
        Map result = new HashMap();
        Map mapHead = new HashMap();
        //默认返回错误码和错误信息
        mapHead.put("errCode","-1");
        mapHead.put("errMsg","Return Body Null,Check config.properties");

        if(body.isEmpty()){
            FinhubLogger.error("Request body is empty，Please check");
            mapHead.put("errCode","-2");
            mapHead.put("errMsg","Request Body Null");
            result.put("head",mapHead);
            return result;
        }

        boolean checkResult = false;
        try {

            checkResult = getTemplate(body, trancode);
            if (checkResult==true) {
                String xmlFile = map2xml(body,encode).asXML();

                String resquest = asemblyPackets(systemId,encoding,encode,fillString(yqdm,20),fillString(trancode,6),reqMsg,xmlFile);

                // 签名
                ISign signTool = new SignFactory().createSignTool(pfxPath,pfxPwd);
                FinhubLogger.info("signTool=" + signTool.hashCode());
                byte[] src = signTool.hashAndSign(resquest.getBytes(encode));

                String signData = new String(src,encode);
                String signDataLength = String.valueOf(signData.getBytes(encode).length);

                HttpRspVo rspVo = send((resquest + fillChar(signDataLength, '0', 6, true) + signData).getBytes(encode),encode,createReqestStream());

                String resHead = new String(rspVo.getBody(),0,222,encode);
                FinhubLogger.info( "The content of the request header is " + resHead);
                if(resHead.trim().equals("")){
                    throw new SocketException();
                }
                mapHead = head2Map(resHead.getBytes(encode),encode);
                String resBody = new String(rspVo.getBody(),222,rspVo.getBody().length-222,encode);
                Map mapBody=null;
                if(resBody!=null&&!resBody.equals("")) {
                    mapBody = xml2map(resBody);
                }
                FinhubLogger.info("The content of the request body is " + resBody);
                result.put("body",mapBody);
            }
            else {
                FinhubLogger.error( "Verification failed...");
                mapHead.put("errCode","-3");
                mapHead.put("errMsg","Check Failed");
            }
        }
        catch (DocumentException e){
            FinhubLogger.error(  "Template acquisition failed..." + e.getMessage());
            mapHead.put("errCode","-4");
            mapHead.put("errMsg","Trancode Error");
        }
        catch (UnsupportedEncodingException e){
            FinhubLogger.error("Encoding is unsupported..." + e.getMessage());
            mapHead.put("errCode","-5");
            mapHead.put("errMsg","Encode Error");
        }
        catch (NullPointerException e){
            FinhubLogger.error("Response body is empty,pleace check parameter...");
        }
        catch (ConnectException e){
            FinhubLogger.error("connection exception...");
            mapHead.put("errCode","-6");
            mapHead.put("errMsg","Connection Refused");
        }
        catch (SocketException e){
            FinhubLogger.error("Protocol connection exception, please convert protocol request...");
            mapHead.put("errCode","-7");
            mapHead.put("errMsg","Protocol Error");
        }
        catch (InvalidDataException e){
            FinhubLogger.error("Verification failed...");
            mapHead.put("errCode","-8");
            mapHead.put("errMsg",e.getMessage());
        }
        catch (FileNotFoundException e){
            FinhubLogger.error("Fail to Read Doc...");
            mapHead.put("errCode","-9");
            mapHead.put("errMsg",e.getMessage());
        }
        catch (UnknownHostException e){
            FinhubLogger.error("Failed to get client IP and MAC address...");
            mapHead.put("errCode","-10");
            mapHead.put("errMsg",e.getMessage());
        }
        catch(CodeAndMsgException e) {
        	FinhubLogger.error("CodeAndMsgException..."+e.getErrorCode()+e.getErrorMsg()+e.getMessage());
            throw e;
        }
        catch (Exception e){
            FinhubLogger.error("System error..." + e.getMessage() + "\n");
            mapHead.put("errCode","-11");
            mapHead.put("errMsg","System Failed");
        }
        result.put("head",mapHead);
        return result;
    }

    public static String sign(byte[] src, String encoding, String pfxPath, String pfxPwd) throws Exception{
    	  // 签名
        ISign signTool = new SignFactory().createSignTool(pfxPath,pfxPwd);
        FinhubLogger.info("signTool=" + signTool.hashCode());
        byte[] srcAfterSign = signTool.hashAndSign(src);

        return new String(srcAfterSign,encoding);
    }
    
    /**
     * <AUTHOR>
     * @param mapCheck    待校验Map
     * @param trancode    交易代码
     * @return            boolean
     * @throws DocumentException         XML转换到Document失败时，抛出此异常
     * @throws IllegalArgumentException  校验失败时抛出此异常
     * @since             2018/4/27
     */
    private static boolean getTemplate(Map mapCheck, String trancode) throws DocumentException,InvalidDataException {

        //校验模板,也用Map保存
        String file = System.getProperty("user.dir") + File.separator + "template" + File.separator +trancode + ".xml";
        FinhubLogger.info("Loading template file......" + file);
        InputStream inputStream =null;
        try {
            inputStream = new FileInputStream(file);
        } catch (FileNotFoundException e){
            FinhubLogger.info("The template file does not exist, and it will be forwarded to the bank directly..." );
            return true;
        }
        SAXReader reader = new SAXReader();
        Document document = reader.read(inputStream);
        FinhubLogger.info("template content is: " + document.asXML());
        List<Element> listTemplate = document.getRootElement().elements();
        return forwardValidate(mapCheck, listTemplate);
    }

    /**
     * <AUTHOR>
     * @param mapCheck      待校验Map
     * @param listTemplate  模板List
     * @return              void
     * @exception
     * @since               2018/4/27
     */
    private static boolean forwardValidate(Map mapCheck, List<Element> listTemplate) throws InvalidDataException {

        FinhubLogger.info("Pre check start");
        for (Element e : listTemplate) {
            //字段域下嵌套字段
            if(e.content().size()!=0){
                FinhubLogger.info(e.attributeValue("id") + "There is a secondary menu, enter the nested loop");
                //按照map list map的数据结构 嵌套执行
                List<Map> listTem = (List) mapCheck.get(e.attributeValue("id"));
                for (Map mapChe : listTem) {
                    forwardValidate((mapChe) ,e.elements());
                }
            }
            //获取所有的校验参数
            List<Attribute> attributes = e.attributes();
            Map mapAttributes = new HashMap();
            //用map保存所有校验参数
            for (Attribute attribute : attributes) {
                mapAttributes.put(attribute.getName(), attribute.getValue());
                FinhubLogger.info("The field parameter is " + attribute.getName()+" value is " + attribute.getValue());
            }
            //实际校验方法  map待校验文件 map1所有校验参数
            validate(mapCheck, mapAttributes);
        }
        return true;
    }

    /**
     * <AUTHOR>
     * @param xmlMap       待校验Map
     * @param templateMap  模板Map
     * @return             boolean
     * @throws
     * @since              2018/4/27
     */
    private static boolean validate(Map xmlMap, Map templateMap) throws InvalidDataException {

        FinhubLogger.info("check start");
        boolean result = false;
        //字段名称
        String id = (String) templateMap.get("id");
        //字段值
        String value = (String) xmlMap.get(id);
        FinhubLogger.info("check field: " + id);
        //校验必输字段
        if (templateMap.get("must").equals("true")) {
            if (!xmlMap.containsKey(id)) {
                FinhubLogger.info(id + "It is a required field, please add");
                throw new InvalidDataException("formatError!The \""+id+"\" must input");
            }
        }
        //必输字段或者非必输但有值，作校验
        if(templateMap.get("must").equals("true")||xmlMap.containsKey(id)) {
            if (templateMap.containsKey("enumeration")) {
                boolean enumResult = false;
                String[] strings = templateMap.get("enumeration").toString().split("\\|");
                for (String str : strings) {
                    if (str.equals(xmlMap.get(id))) {
                        enumResult = true;
                        break;
                    }
                }
                if (enumResult == false) {
                    FinhubLogger.info(id + "value should be one of" + templateMap.get("enumeration").toString() + ",please check！");
                    throw new InvalidDataException("formatError!The \"" + id + "\" must in " + templateMap.get("enumeration").toString());
                }
            }

            //校验数据类型  "X":字母数字;"9":数字;"A":大写字母（缺省值为X）
            String type = (String) templateMap.get("dataType");
            if ("9".equals(type)) {
                if (value.indexOf(".") == -1) {
                    for (int i = 0; i < value.length(); i++) {
                        char c = value.charAt(i);
                        if (!('0' <= c && c <= '9' || " ".indexOf(c) != -1))
                            throw new InvalidDataException("formatError!The \"" + value + "\" contains " + c + "which is not between \"0\" and \"9\" or [" + " " + "] while the type is \"9\".");
                    }
                }
            } else if ("A".equals(type)) {
                for (int i = 0; i < value.length(); i++) {
                    char c = value.charAt(i);
                    if (!('A' <= c && c <= 'Z' || " ".indexOf(c) != -1))
                        throw new InvalidDataException("formatError!The \"" + value + "\" contains " + c + "which is not between \"A\" and \"Z\" or [" + " " + "] while the type is \"A\".");
                }
            }

            //校验字符串长度
            int min = Integer.parseInt(templateMap.get("minlength").toString());
            int max = Integer.parseInt(templateMap.get("maxlength").toString());
            if (value.indexOf(".") != -1 && (type.equals("9"))) {
                String num[] = value.split("\\.");
                if (num[0].length() > max) {
                    throw new InvalidDataException("rangeError!The value[" + num[0] + "]'s length is more than " + max + ".");
                }
                if (num[1].length() > min) {
                    throw new InvalidDataException("rangeError!The value[" + num[1] + "]'s length is more than " + min + ".");
                }
            } else {
                int length = xmlMap.get(id).toString().length();
                if (length < min) {
                    FinhubLogger.info(id + "Length should be greater than or equal to" + min + "，please check！");
                    throw new InvalidDataException("rangeError!The value[" + value + "]'s length is less than " + min + ".");

                }
                if (length > max) {
                    FinhubLogger.info(id + "Length should be less than or equal to" + max + "，please check！");
                    throw new InvalidDataException("rangeError!The value[" + value + "]'s length is more than " + max + ".");
                }
            }
        }
        return true;
    }

    public static Map sendTobank(String xml, String trancode, String yqdm, String pfxPath, String pfxPwd) throws Exception {
        Map body = xml2map(xml);
        String reqMsg = "reqMsg=xxx|ZuID=xxxxA";
        Service service = Service.getInstance();
        String systemId = "01";
        String encoding = "02";
        String threadId = service.nextSn();
        String encode = "UTF-8";

        //最终返回Map，一定有mapHead，不成功无mapbody
        Map result = new HashMap();
        Map mapHead = new HashMap();
        //默认返回错误码和错误信息
        mapHead.put("errCode","-1");
        mapHead.put("errMsg","Return Body Null,Check config.properties");

        if(body.isEmpty()){
            FinhubLogger.error("Request body is empty，Please check");
            mapHead.put("errCode","-2");
            mapHead.put("errMsg","Request Body Null");
            result.put("head",mapHead);
            return result;
        }
        boolean checkResult = false;
        try {

            checkResult = getTemplate(body, trancode);
            if (checkResult==true) {
                String xmlFile = map2xml(body,encode).asXML();

                String resquest = asemblyPackets(systemId,encoding,encode,fillString(yqdm,20),fillString(trancode,6),reqMsg,xmlFile);

                // 签名
                ISign signTool = new SignFactory().createSignTool(pfxPath,pfxPwd);
                FinhubLogger.info("signTool=" + signTool.hashCode());
                byte[] src = signTool.hashAndSign(resquest.getBytes(encode));

                String signData = new String(src,encode);
                String signDataLength = String.valueOf(signData.getBytes(encode).length);

                HttpRspVo rspVo = send((resquest + fillChar(signDataLength, '0', 6, true) + signData).getBytes(encode),encode,createReqestStream());

                String resHead = new String(rspVo.getBody(),0,222,encode);
                if(resHead.trim().equals("")){
                    throw new SocketException();
                }
                mapHead = head2Map(resHead.getBytes(encode),encode);
                String resBody = new String(rspVo.getBody(),222,rspVo.getBody().length-222,encode);
                FinhubLogger.info("The content of the response body is " + resBody);
                result.put("body",resBody);
            }
            else {
                FinhubLogger.error( "Verification failed...");
                mapHead.put("errCode","-3");
                mapHead.put("errMsg","Check Failed");
            }
        }
        catch (DocumentException e){
            FinhubLogger.error(  "Template acquisition failed..." + e.getMessage());
            mapHead.put("errCode","-4");
            mapHead.put("errMsg","Trancode Error");
        }
        catch (UnsupportedEncodingException e){
            FinhubLogger.error("Encoding is unsupported..." + e.getMessage());
            mapHead.put("errCode","-5");
            mapHead.put("errMsg","Encode Error");
        }
        catch (NullPointerException e){
            FinhubLogger.error("Response body is empty,pleace check parameter...");
        }
        catch (ConnectException e){
            FinhubLogger.error("connection exception...");
            mapHead.put("errCode","-6");
            mapHead.put("errMsg","Connection Refused");
        }
        catch (SocketException e){
            FinhubLogger.error("Protocol connection exception, please convert protocol request...");
            mapHead.put("errCode","-7");
            mapHead.put("errMsg","Protocol Error");
        }
        catch (InvalidDataException e){
            FinhubLogger.error("Verification failed...");
            mapHead.put("errCode","-8");
            mapHead.put("errMsg",e.getMessage());
        }
        catch (FileNotFoundException e){
            FinhubLogger.error("Fail to Read Doc...");
            mapHead.put("errCode","-9");
            mapHead.put("errMsg",e.getMessage());
        }
        catch (UnknownHostException e){
            FinhubLogger.error("Failed to get client IP and MAC address...");
            mapHead.put("errCode","-10");
            mapHead.put("errMsg",e.getMessage());
        }
        catch(CodeAndMsgException e) {
            FinhubLogger.error("CodeAndMsgException..."+e.getErrorCode()+e.getErrorMsg()+e.getMessage());
            throw e;
        }
        catch (Exception e){
            FinhubLogger.error("System error..." + e.getMessage() + "\n");
            mapHead.put("errCode","-11");
            mapHead.put("errMsg","System Failed");
        }
        result.put("head",mapHead);
        return result;
    }


    public static String sign(String reqStr, String pfxPath, String pfxPwd) throws Exception{
        byte[] src = reqStr.getBytes("GBK");
        String encoding = "GBK";
        // 签名
        ISign signTool = new SignFactory().createSignTool(pfxPath,pfxPwd);
        FinhubLogger.info("signTool=" + signTool.hashCode());
        byte[] srcAfterSign = signTool.hashAndSign(src);
        return new String(srcAfterSign,encoding);
    }
}