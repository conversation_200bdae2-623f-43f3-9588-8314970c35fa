package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryTradeDetailsReqDTO
 * @Description: 3.9查询账户当日历史交易明细[4013]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryTradeDetailsReqDTO implements Serializable {

    /**
     * 	账号	Char(20)	Y
     */
    private String AcctNo;

    /**
     * 	币种	Char(3)	Y
     */
    private String CcyCode;

    /**
     * 	开始日期	Char(8)	Y
     * 	若查询当日明细，开始、结束日期必须为当天；若查询历史明细，开始、结束日期必须是历史日期。
     */
    private String BeginDate;

    /**
     * 	结束日期	Char(8)	Y
     */
    private String EndDate;

    /**
     * 	查询页码	Char(6)	Y
     * 	1：第一页，依次递增
     */
    private String PageNo;

    /**
     *  每页明细数量	Char(6)	N
     *  当日明细默认每页30条记录，支持最大每页100条，若上送PageSize>100无效，等同100；
     *  历史明细默认每页30条记录，支持最大每页1000条，若上送PageSize>1000则提示输入错误；
     *  且每次查询必须固定为此值，否则出现明细遗漏
     */
    private String PageSize ;

    /**
     * 	预留字段	Char(120)
     */
    private String Reserve;

    /**
     * 	记录排序标志	C(3)	N
     * 	001：按交易时间降序；
     *  002：按交易时间升序；
     *     说明：
     *             ①当为历史交易明细查询时，默认按照001：按交易时间降序；
     *             ②当为当日明细查询时，默认按照002：按交易时间升序；
     *             （注：当日明细在交易量大的情况下，必须采用正序查询，否则会导致交易遗漏和重复）
     */
    private String OrderMode = "002";

    /**
     * 	银行交易流水号	Char(30)	N
     * 	输入则查询过滤指定条件的记录
     */
    private String BankTranFlowNo;

    /**
     * 	交易对手账号	Char(30)	N
     * 	输入则查询过滤指定条件的记录
     */
    private String OppAcctNo;

}
