package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**  
 * @Description: 
 * <AUTHOR>
 * @date 2024-03-01 05:41:56 
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class MemberInformationChangeRequestDTO implements Serializable {

	/**
	 * 系统返回的子账户帐号
	 */
	@JsonProperty("SubAcctNo")
    private String subAcctNo;
	/**
	 * 交易网会员代码即会员在平台端系统的会员编号
	 */
	@JsonProperty("TranNetMemberCode")
    private String tranNetMemberCode;
	
	/**
	 * 账户为企业时公司名称、法人名称、法人证件类型、法人证件号码必输,且公司名称与会员名称一致
	 */
	@JsonProperty("MemberName")
    private String memberName;
	
	/**
	 * 账户为企业时公司名称、法人名称、法人证件类型、法人证件号码必输,且公司名称与会员名称一致
	 */
	@JsonProperty("CompanyName")
    private String companyName;
	
	/**
	 * 法人名称
	 */
	@JsonProperty("ReprName")
    private String reprName;
	
	/**
	 * 法人证件类型
	 */
	@JsonProperty("ReprGlobalType")
    private String reprGlobalType;
	
	/**
	 * 法人证件号码
	 */
	@JsonProperty("ReprGlobalId")
    private String reprGlobalId;
	
	/**
	 * 保留域
	 */
	@JsonProperty("ReservedMsg")
    private String reservedMsg;
	
}
