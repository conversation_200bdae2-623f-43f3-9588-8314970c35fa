package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName MembershipTranchePayReqDTO
 * @Description: KFEJZB6163	会员资金支付-不验证	MembershipTranchePay
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MembershipTranchePayReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 转出见证子账户的账号	Y 32
     * 普通子账户只能付款，商户子账户只能收款
     */
    @JsonProperty(value = "OutSubAcctNo")
    private String outSubAcctNo;

    /*
     * 转出交易网会员代码	Y 32
     */
    @JsonProperty(value = "OutTranNetMemberCode")
    private String outTranNetMemberCode;

    /*
     * 手续费	Y 15
     * 归于平台的收益
     */
    @JsonProperty(value = "Commission")
    private String commission = "0";

    /*
     * 币种	Y 3
     * RMB
     */
    @JsonProperty(value = "Ccy")
    private String ccy = "RMB";

    /*
     * 订单号	Y 32
     * 6007接口送的订单号
     */
    @JsonProperty(value = "OrderNo")
    private String orderNo;

    /*
     * 转入见证子账户数	Y 8
     * 仅支持1-5笔
     */
    @JsonProperty(value = "InSubAcctNum")
    private String inSubAcctNum;

    @JsonProperty(value = "TranItemArray")
    private List<MembershipTranchePayItemDTO> tranItemArray;

    /*
     * 备注	N 120
     */
    @JsonProperty(value = "Remark")
    private String remark;

    /*
     * 保留域	N 120
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
