package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

/**
 * @ClassName SpaOrderTypeEnum
 * @Description: 订单类型
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum SpaOrderTypeEnum {
    /**
     * 1支付 2退款 3撤销
     */
    PAYMENT("1","支付"),
    REFUND("2","退款"),
    REVOKE("3","撤销")
    ;

    SpaOrderTypeEnum(String orderType, String desc){
        this.orderType = orderType;
        this.desc = desc;
    }

    private String orderType;
    private String desc;

    public String getOrderType() {
        return orderType;
    }

    public String getDesc() {
        return desc;
    }
}
