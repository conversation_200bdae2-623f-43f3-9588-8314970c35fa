package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ToTransferWithOtpReqDTO implements Serializable {
    // 是否虚拟卡业务 目前业务报销卡需要上送subAppId， 虚拟卡不需要上送subAppId
    private Boolean isBankCard;
    //	32	第三方会员ID	Y	必输，
    private String thirdId;
    //		收款方卡号	Y	必填，即绑定卡卡号（需要调状态查询接口返回绑定卡密文）。测试环境请联系银行配置收款卡号（绑定卡卡号）
    private String bindCardNo;
    //	50	收款方户名	Y	必填
    private String accountName;
    //	10	收款方银行编码	N	非必填
    private String bankCode;
    //	30	收款方银行名称	N	非必填
    private String bankName;
    //	100	转账发起方流水号	Y	转账发起方流水号【长度100以内】，需要和OTP发送接口中的bussinessNo的值一样
    private String bussinessNo;
    //	10	发起方业务场景	Y	业务场景，银行分配（商户号）
    private String bussinessScence;
    //	2	转账渠道	Y	必填：固定值：H5
    private String channelType = "H5";
    //	10	收款方城市编码	N	非必填
    private String cityCode;
    //	100	一账通客户ID	N	非必填
    private String clientNo;
    //	10	币种	Y	必填：RMB
    private String currType = "RMB";
    //	10	设备绑定标识	N	非必填
    private String deviceBindFlag;
    //	100	设备指纹	N	非必填，反洗钱需要，建议上送
    private String deviceFingerPrint;
    //	100	设备ID	N	非必填，反洗钱需要，建议上送
    private String deviceId;
    //	2	重复转账确认	Y	必填：同一个转出方向同一个转入方转账相同金额时，如果继续上送Y，否则上送N【收款方为本行信用卡时卡交易会校验该字段值】
    private String duplicateConfirmFlag = "N";
    //	2	转账方式	Y	3：实时、4：普通、6：次日。默认填：3
    private String executeType;
    //		转账金额	Y	必填
    private String amount;
    //		账户开户/开户结果查询/账户信息查询/授权/授权查询返回的协议号，与"fromAccountNo、fromAccountName"二选一
    private String agreementNo;
    //		转出卡号	Y	必填，即二类户卡号
    private String fromAccountNo;
    //	100	转出户名	Y	必填
    private String fromAccountName;
    //	20	ip地址	Y	必填，反洗钱需要
    private String ipAddr;
    //	30	经度	N	非必填，反洗钱需要，建议上送
    private String latData;
    //	2	30w确认标识	Y	必填转账超过30w时，如果继续上送Y，否则上送N【针对转出与转入非同行同名户】。商户后端自己做逻辑判断
    private String limitConfirmFlag;
    //	30	纬度	N	非必填，反洗钱需要，建议上送
    private String lngData;
    //	100	新会员ID	N	非必填
    private String newMid;
    //	10	收款方省份编码	N
    private String provinceCode;
    //	100	会话ID	N	非必填，反洗钱需要
    private String sessionTicket;
    //	100	收款方分行名	N
    private String subBranchBankName;
    //	20	联行号	N	非必填
    private String unionBankCode;
    //	50	用户备注	N
    private String userRemark;
    //	30	otp流水号	Y	发OTP接口返回的OTP订单号
    private String otpOrderNo;
    //	6	短信动态码	Y
    private String otpValue;
}
