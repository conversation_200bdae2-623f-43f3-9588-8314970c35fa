package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2023-02-20 19:59:08
 * @Version 1.0
 **/
@Data
public class QryRefundStateReqDTO implements Serializable {
    // marketCode String 否 - 市场代码 市场代码和资金汇总账号2选1必送，若2个都上送了必须检查一致性 qydm
    private String marketCode;
    // fundSummaryAcctNo String 否 - 资金汇总账号 市场代码和资金汇总账号2选1必送，若2个都上送了必须检查一致性 ****************
    private String fundSummaryAcctNo;
    // oldAcceptSeqNo String 否 - 原申请受理流水号 原返回妥收流水和原申请流水必输其中一个
    private String oldAcceptSeqNo;
    // oldReqTranSeqNo String 否 - 原请求交易流水号 原返回妥收流水和原申请流水必输其中一个
    private String oldReqTranSeqNo;
    // reservedMsg String 否 - 保留域 保留域 保留域
    private String reservedMsg;
}
