package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class OpenAccountPreReqDTO implements Serializable {
    // 是否虚拟卡业务 目前业务报销卡需要上送subAppId， 虚拟卡不需要上送subAppId
    private Boolean isBankCard;
    // 	trueName	姓名	string	必须
    private String	trueName;
    // 	idNo	证件号	string	必须
    private String	idNo;
    // 	idType	证件类型	string	必须
    private String	idType;
    // 	mobileNo	手机号	string	必须
    private String	mobileNo;
    // 	bindCardNo	绑定卡号	string	必须
    private String	bindCardNo;
    // 	subAppId	子商户号	string	必须
    private String	subAppId;
}
