package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName TransactionRefundFlagEnum
 * @Description: 会员间交易退款-功能标识枚举
 * <AUTHOR>
 * @Date 2021/10/27
 **/
public enum TransactionRefundFlagEnum {

    /*
     * 功能标志	Y 1
     * 2：会员交易接口【直接支付】退款
     * 针对【6006/6034/6101】funcflag=6、9-直接支付的退款
     * 6：会员资金支付接口的退款
     * 针对【6163/6165/6166】会员资金支付的退款
     */
    TRADE("2","会员交易接口【直接支付】退款"),
    PAY("6","会员资金支付接口的退款")
    ;

    private String flag;
    private String desc;

    TransactionRefundFlagEnum(String flag, String desc) {
        this.flag = flag;
        this.desc = desc;
    }

    public String getFlag() {
        return flag;
    }

    public String getDesc() {
        return desc;
    }
}
