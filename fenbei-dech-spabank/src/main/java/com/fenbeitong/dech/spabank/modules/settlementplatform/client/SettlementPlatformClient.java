package com.fenbeitong.dech.spabank.modules.settlementplatform.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.dech.spabank.utils.SpaDateUtil;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.utils.ObjUtils;
import com.pingan.openbank.api.sdk.OpenBankApiClient;
import com.pingan.openbank.api.sdk.common.http.HttpResult;
import com.pingan.openbank.api.sdk.exception.OpenBankSdkException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Random;

/**
 * @ClassName SettlementPlatformClient
 * @Description: 结算通交互请求类
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Component
public class SettlementPlatformClient {

	private Random random = new Random();

    @Value("${spaBank.settlementPlat.fundSummaryAcctNo}")
    private String fundSummaryAcctNo;

    @Value("${spaBank.settlementPlat.mrchCode}")
    private String mrchCode;

    @Value("${spaBank.settlementPlat.appId}")
    private String settlementPlatAppId;

    public String request(String request, String txnCode, String serviceId) throws OpenBankSdkException {
        FinhubLogger.info("平安银行-结算通接口请求参数,appId={},url={},request={}", settlementPlatAppId, serviceId, request);
        String response = null;
        JSONObject head = JSON.parseObject(request);
        if(ObjUtils.isBlank(head.get("CnsmrSeqNo"))){
            //交易流水号:系统流水号，建议规范：用户短号（6位）+日期（6位）+随机编号（10位）例：C256341801183669951236平台也可自行定义，满足长度即可
            String seq = SpaDateUtil.dateFormat(new Date()) + random.nextInt(********);
            head.put("CnsmrSeqNo", seq);
        }
        //交易码
        head.put("TxnCode", txnCode);
        //发送时间:格式为YYYYMMDDHHmmSSNNN后三位固定000
        head.put("TxnTime", SpaDateUtil.dateFormat(new Date()));
        //商户号:签约客户号，见证宝产品此字段为必输
        head.put("MrchCode", mrchCode);
        head.put("FundSummaryAcctNo", fundSummaryAcctNo);
        try {
        	long start = System.currentTimeMillis();
            HttpResult result = OpenBankApiClient.invoke(settlementPlatAppId, serviceId, head, null);
            long end = System.currentTimeMillis();
            FinhubLogger.info("平安银行-结算通接口返回,url={},耗时{}ms,request={},response={}", serviceId, end-start, head.toJSONString(), result);
            if (ObjUtils.isNull(result) || ObjUtils.isBlank(result.getData()) || result.getCode() != 200){
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_SETTLEMENT_PLATFORM_RESPONSE_NULL);
            }
            response = result.getData();
            return response;
        } catch (OpenBankSdkException e) {
            FinhubLogger.error("【平安银行-结算通接口请求异常】【url={}】【request={}】,exception=", serviceId, head.toJSONString(), e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_SETTLEMENT_PLATFORM_EXCEPTION);
        }
    }
}
