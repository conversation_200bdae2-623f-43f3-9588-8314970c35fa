package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class EJZBCustInfomationQueryReqDTO extends SpaSettlementPlatBaseReqDTO{
    /*
     * 子台账账号	Y 32
     */
    @JsonProperty(value = "CustAcctId")
    private String custAcctId;

    /*
     * 交易网会员代码	Y 32
     */
    @JsonProperty(value = "ThirdCustId")
    private String thirdCustId;

    /*
     * 保留域	N 100
     */
    @JsonProperty(value = "Reserve")
    private String reserve;
}
