package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-11-16 18:11:29
 * @Version 1.0
 **/
@Data
public class BackFillDynamicPasswordReqDTO extends SpaSettlementPlatBaseReqDTO {

    // 交易网会员代码string(32)Y
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    // 见证子账户的账号string(32)Y
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    // 修改方式string(2)Y
    @JsonProperty(value = "ModifyType")
    private String modifyType;

    // 短信指令号string(32)Y需与申请短信码时一致。
    @JsonProperty(value = "MessageOrderNo")
    private String messageOrderNo;

    // 短信验证码string(7)Y查询短信码
    @JsonProperty(value = "MessageCheckCode")
    private String messageCheckCode;

    // 保留域string(120)N
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

}
