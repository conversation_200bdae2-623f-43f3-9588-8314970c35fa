package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName TranStatusEnum
 * @Description: 交易状态
 * <AUTHOR>
 * @Date 2021/10/15
 **/
public enum TranStatusEnum {

    /**
     * （0：成功，1：失败，2：待确认, 5：待处理，6：处理中）
     */
    SUCCESS("0","成功"),
    FAIL("1","失败"),
    TO_BE_CONFIRMED("2","待确认"),
    PENDING("5","待处理"),
    PROCESSING("6","处理中"),
    ;

    TranStatusEnum(String tranStatus, String desc){
        this.tranStatus = tranStatus;
        this.desc = desc;
    }

    private String tranStatus;
    private String desc;

    public String getTranStatus() {
        return tranStatus;
    }

    public String getDesc() {
        return desc;
    }

}
