package com.fenbeitong.dech.spabank.modules.cz.service.impl;

import com.fenbeitong.dech.spabank.modules.cz.client.SpaCZApiClient;
import com.fenbeitong.dech.spabank.modules.cz.dto.*;
import com.fenbeitong.dech.spabank.modules.cz.enums.SpaCzTransCodeEnum;
import com.fenbeitong.dech.spabank.modules.cz.service.SpaCzSearchService;
import com.finhub.framework.core.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by FBT on 2023/8/15.
 */
@Service
public class SpaCzSearchServiceImpl implements SpaCzSearchService{

    @Autowired
    SpaCZApiClient spaCZApiClient;

    @Override
    public SpaCzQueryBatchTransferRespDto queryTradeDetails(SpaCzQueryBatchTransferReqDto spaCzQueryBatchTransferReqDto) {
        SpaCzQueryBatchTransferRespDto request = spaCZApiClient.request(JsonUtils.toJson(spaCzQueryBatchTransferReqDto), SpaCzQueryBatchTransferRespDto.class, SpaCzTransCodeEnum.STAR_ACCOUNTITEM001.getTxnCode());
        return request;
    }

    @Override
    public SpaCzReceiptRespDto getReceiptRequest(SpaCzReceiptReqDto spaCzReceiptReqDto) {
        SpaCzReceiptRespDto request = spaCZApiClient.request(JsonUtils.toJson(spaCzReceiptReqDto), SpaCzReceiptRespDto.class, SpaCzTransCodeEnum.STAR_RECEIPT001.getTxnCode());
        return request;
    }

    @Override
    public SpaCzReceiptDownLoadRespDTO getReceiptDownload(SpaCzReceiptDownLoadReqDto spaCzReceiptDownLoadReqDto) {
        SpaCzReceiptDownLoadRespDTO request = spaCZApiClient.request(JsonUtils.toJson(spaCzReceiptDownLoadReqDto), SpaCzReceiptDownLoadRespDTO.class, SpaCzTransCodeEnum.STAR_RECEIPT002.getTxnCode());
        return request;
    }

}
