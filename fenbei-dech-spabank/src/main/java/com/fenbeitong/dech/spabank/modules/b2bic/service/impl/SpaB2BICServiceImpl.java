package com.fenbeitong.dech.spabank.modules.b2bic.service.impl;

import com.fenbeitong.dech.common.until.XmlUtils;
import com.fenbeitong.dech.spabank.modules.b2bic.client.SpaB2BICClient;
import com.fenbeitong.dech.spabank.modules.b2bic.constant.SpaB2BICInterfaceConstant;
import com.fenbeitong.dech.spabank.modules.b2bic.dto.req.*;
import com.fenbeitong.dech.spabank.modules.b2bic.dto.resp.*;
import com.fenbeitong.dech.spabank.modules.b2bic.service.SpaB2BICService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName SpaB2BICServiceImpl
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@Service
public class SpaB2BICServiceImpl implements SpaB2BICService {

    @Autowired
    private SpaB2BICClient spaB2BICClient;

    @Override
    public SpaQueryTransferStatusRespDTO queryTransferStatus(SpaQueryTransferStatusReqDTO reqDTO) {
        SpaQueryTransferStatusRespDTO respDTO = new SpaQueryTransferStatusRespDTO();
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(reqDTO), SpaB2BICInterfaceConstant.QUERY_TRANSFER_STATUS, SpaQueryTransferStatusRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            String code = e1.getMessage().split(":")[0];
            // 无法确认
            if(SpaB2BICInterfaceConstant.UNABLE_TO_CONFIRM.contains(code)){
                respDTO.setStt(code);
                respDTO.buildSuccessCode();
                return respDTO;
            }
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-单笔转账指令查询接口请求异常,reqDTO={}", reqDTO, e);
        }
        return respDTO;
    }

    @Override
    public SpaFundTransferRespDTO fundTransfer(SpaFundTransferReqDTO reqDTO) {
        SpaFundTransferRespDTO respDTO = null;
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(reqDTO), SpaB2BICInterfaceConstant.FUND_TRANSFER, SpaFundTransferRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            respDTO = new SpaFundTransferRespDTO();
            String code = e1.getMessage().split(":")[0];
            // 无法确认
            if(SpaB2BICInterfaceConstant.UNABLE_TO_CONFIRM.contains(code)){
                respDTO.setFrontLogNo(e1.getMessage());
                respDTO.buildSuccessCode();
                return respDTO;
            }
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-企业单笔资金划转接口请求异常,reqDTO={}", reqDTO, e);
        }
        return respDTO;
    }

    @Override
    public SpaQueryTradeDetailsRespDTO queryTradeDetails(SpaQueryTradeDetailsReqDTO reqDTO) {
        SpaQueryTradeDetailsRespDTO respDTO = new SpaQueryTradeDetailsRespDTO();
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(reqDTO), SpaB2BICInterfaceConstant.QUERY_TRADE_DETAILS, SpaQueryTradeDetailsRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-查询账户当日历史交易明细接口请求异常,reqDTO={}", reqDTO, e);
        }
        return respDTO;
    }

    @Override
    public SpaQueryReturnRemittanceDetailsRespDTO queryReturnRemittanceDetails(SpaQueryReturnRemittanceDetailsReqDTO reqDTO) {
        SpaQueryReturnRemittanceDetailsRespDTO respDTO = new SpaQueryReturnRemittanceDetailsRespDTO();
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(reqDTO), SpaB2BICInterfaceConstant.QUERY_RETURN_REMITTANCE_DETAILS, SpaQueryReturnRemittanceDetailsRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-查询支付退票明细接口请求异常,reqDTO={}", reqDTO, e);
        }
        return respDTO;
    }

    @Override
    public SpaQueryReceiptRespDTO queryReceiptDetails(SpaQueryReceiptReqDTO reqDTO) {
        SpaQueryReceiptRespDTO respDTO = new SpaQueryReceiptRespDTO();
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(reqDTO), SpaB2BICInterfaceConstant.QUERY_RECEIPT, SpaQueryReceiptRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-查询历史单笔PDF回单接口请求异常,reqDTO={}", reqDTO, e);
        }
        return respDTO;
    }

    @Override
    public SpaQueryHistoryBalanceRespDTO queryHistoryBalance(SpaQueryHistoryBalanceReqDTO reqDTO) {
        SpaQueryHistoryBalanceRespDTO respDTO = new SpaQueryHistoryBalanceRespDTO();
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(reqDTO), SpaB2BICInterfaceConstant.QUERY_HISTORY_BALANCE, SpaQueryHistoryBalanceRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-查询历史余额接口请求异常,reqDTO={}", reqDTO, e);
        }
        return respDTO;
    }

    @Override
    public SpaHistoryReceiptQueryRespDTO historyReceiptQuery(SpaHistoryReceiptQueryReqDTO spaHistoryReceiptQueryReqDTO) {
        SpaHistoryReceiptQueryRespDTO respDTO = new SpaHistoryReceiptQueryRespDTO();
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(spaHistoryReceiptQueryReqDTO), SpaB2BICInterfaceConstant.QUERY_RECEIPT_OF_HISTORY_QUERY, SpaHistoryReceiptQueryRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-查询历史余额接口请求异常,reqDTO={}", spaHistoryReceiptQueryReqDTO, e);
        }
        return respDTO;
    }

    @Override
    public SpaHistoryReceiptGenerateRespDTO historyReceiptGenerate(SpaHistoryReceiptGenerateReqDTO spaHistoryReceiptGenerateReqDTO) {
        SpaHistoryReceiptGenerateRespDTO respDTO = new SpaHistoryReceiptGenerateRespDTO();
        try {
            respDTO = spaB2BICClient.request(XmlUtils.convertToXml(spaHistoryReceiptGenerateReqDTO), SpaB2BICInterfaceConstant.QUERY_RECEIPT_OF_HISTORY_GENERATE, SpaHistoryReceiptGenerateRespDTO.class);
            respDTO.buildSuccessCode();
        } catch (FinhubException e1) {
            respDTO.setCode(e1.getCode().toString());
            respDTO.setMessage(e1.getMessage());
            return respDTO;
        } catch (Exception e) {
            FinhubLogger.error("平安银行-查询历史余额接口请求异常,reqDTO={}", spaHistoryReceiptGenerateReqDTO, e);
        }
        return respDTO;
    }
}
