package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName SpaBankMemberType
 * @Description: 平安会员类型1：企业 2：平台
 * <AUTHOR>
 * @Date 2021/10/28
 **/
public enum SpaBankMemberTypeEnum {

    COMPANY(1,"企业会员"),
    PLATFORM(2,"平台会员"),
    PERSON(3,"个人会员"),
    ;

    // 类型1：企业 2：平台

    private Integer type;

    private String desc;

    SpaBankMemberTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
