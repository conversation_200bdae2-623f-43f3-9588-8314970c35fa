package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName AutonymOpenCustAcctIdReqDTO
 * @Description: KFEJZB6248 实名开户 AutonymOpenCustAcctId
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class AutonymOpenCustAcctIdReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     * 1:开户 2:销户
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 交易网会员代码	Y 32
     * 交易网会员代码即会员在平台端系统的会员编号
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 会员名称	Y 120
     * 客户真实姓名
     */
    @JsonProperty(value = "MemberName")
    private String memberName;

    /*
     * 会员证件类型	Y 2
     * 详见《接口证件类型说明》举例：1-身份证；52-组织机构代码证；68-营业执照 ；73-统一社会信用代码
     */
    @JsonProperty(value = "MemberGlobalType")
    private String memberGlobalType;

    /*
     * 会员证件号码	Y 20
     * 证件号码
     */
    @JsonProperty(value = "MemberGlobalId")
    private String memberGlobalId;

    /*
     * 用户昵称	N 120
     * 用户昵称
     */
    @JsonProperty(value = "UserNickname")
    private String userNickname;

    /*
     * 手机号码	N 32
     * 手机号码（测试环境送11个1）
     */
    @JsonProperty(value = "Mobile")
    private String mobile;

    /*
     * 会员属性	Y 2
     * SH-商户子账户
     */
    @JsonProperty(value = "MemberProperty")
    private String memberProperty = "00";

    /*
     * 保留域	N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
