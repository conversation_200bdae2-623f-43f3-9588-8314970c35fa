package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName RegisterBillReqDTO
 * @Description: KFEJZB6139	登记挂账(支持撤销)	RegisterBillSupportWithdraw\
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class RegisterBillReqDTO extends SpaSettlementPlatBaseReqDTO{

    /*
     * 见证子账户的账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 交易网会员代码	Y 32
     * "交易网会员代码即会员在平台端系统的会员编号
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 订单号	Y 32
     * 全局唯一，不能与6034/6101/6006/6007/6135/6134订单号相同。
     */
    @JsonProperty(value = "OrderNo")
    private String orderNo;

    /*
     * 挂账金额	Y 32
     * 包含交易费用，即挂账金额=会员实际到账金额+交易费用
     */
    @JsonProperty(value = "SuspendAmt")
    private String suspendAmt;

    /*
     * 交易费用	Y 32
     * 平台收取用户的费用
     */
    @JsonProperty(value = "TranFee")
    private String tranFee;

    /*
     * 备注	Y 32
     */
    @JsonProperty(value = "Remark")
    private String remark;

    /*
     * 保留域1	Y 1
     */
    @JsonProperty(value = "ReservedMsgOne")
    private String reservedMsgOne;

    /*
     * 保留域2	Y 1
     */
    @JsonProperty(value = "ReservedMsgTwo")
    private String reservedMsgTwo;

    /*
     * 保留域3	Y 32
     */
    @JsonProperty(value = "ReservedMsgThree")
    private String reservedMsgThree;
}
