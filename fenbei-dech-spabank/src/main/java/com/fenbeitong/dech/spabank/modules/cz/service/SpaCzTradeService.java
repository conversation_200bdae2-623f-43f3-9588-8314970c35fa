package com.fenbeitong.dech.spabank.modules.cz.service;

import com.fenbeitong.dech.spabank.modules.cz.dto.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-08-15 下午3:01
 */

public interface SpaCzTradeService {

    /**
     * pay001
     */
    SpaCzPay001RespDTO  pay001(SpaCzPay001ReqDTO spaCzPay001ReqDTO);

    /**
     * pay001result
     */
    SpaCzPayResultDetailRespDTO payResult001(SpaCzPayResultReqDTO spaCzPayResultReqDTO);

    /**
     * payroll001
     */
    SpaCzPayRoll001RespDTO payRoll001(SpaCzPayRoll001ReqDTO spaCzPayRoll001ReqDTO);

    /**
     * payrollresult001
     */
    SpaCzPayRollResultRespDTO payRollResult001(SpaCzPayRollResultReqDTO spaCzPayRollResultReqDTO);
}
