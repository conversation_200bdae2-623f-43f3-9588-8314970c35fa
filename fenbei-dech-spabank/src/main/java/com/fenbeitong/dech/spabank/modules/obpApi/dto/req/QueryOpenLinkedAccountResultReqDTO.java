package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class QueryOpenLinkedAccountResultReqDTO implements Serializable {

    //	appId	应用ID	string	必须		商户号
    private String  appId;
    //	证件号码	必须	必须为二代身份证号码，必须为18位，字母X必须大写。需要和OTP发送接口中bizKey中的idNo的值一样。	最大长度:18
    private String  idNo;
    //	业务流水号	必须	业务流水号需要和OTP发送接口中的businessNo的值一样,要求全局唯一， OB（固定字母，标记开放银行渠道） + appId（10位，不够10位的在左边补0）+ 两位业务代码（可以使用数字和字母标记现有业务接口，不使用则默认为00进行填充） + yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）	最大长度:32
    private String  businessNo;

}
