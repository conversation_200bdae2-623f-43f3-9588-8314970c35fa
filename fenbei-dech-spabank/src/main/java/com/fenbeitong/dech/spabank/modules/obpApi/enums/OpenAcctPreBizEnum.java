package com.fenbeitong.dech.spabank.modules.obpApi.enums;

import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 开户前置校验状态码枚举
 */
public enum OpenAcctPreBizEnum {

    // 1：已开通I类户 2：已开通II、III类户  3：未开通I、II、III类户  4：不符合开立条件 5：开户处理中
    BIZ_CODE_1("1", "已存在我行账户可直接使用无需开立商户联营卡", "1", "已开通I类户"),
    BIZ_CODE_2("2", "复用二类户及三类户开立商户联营卡 ", "3", "未开通I、II、III类户"),
    BIZ_CODE_3("3", "复用二类户并开立三类户开立商户联营卡", "3", "未开通I、II、III类户"),
    BIZ_CODE_4("4", "开立二类户并复用三类户开立商户联营卡", "3", "未开通I、II、III类户"),
    BIZ_CODE_5("5", "开立二类户及三类户开立商户联营卡", "3", "未开通I、II、III类户"),
    BIZ_CODE_6("6", "已开立商户联营卡", "2", "已开通II、III类户"),
    BIZ_CODE_7("7", "商户联营卡开户处理中", "5", "开户处理中"),
    BIZ_CODE_8("8", "不符合开立条件", "4", "不符合开立条件"),
    // * 其他值：不符合开立条件
    ;

    private String bizCode;

    private String bizMsg;

    private String checkStatus;

    private String checkStatusDesc;

    public String getBizCode() {
        return bizCode;
    }

    public String getBizMsg() {
        return bizMsg;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public String getCheckStatusDesc() {
        return checkStatusDesc;
    }

    OpenAcctPreBizEnum(String bizCode, String bizMsg, String checkStatus, String checkStatusDesc) {
        this.bizCode = bizCode;
        this.bizMsg = bizMsg;
        this.checkStatus = checkStatus;
        this.checkStatusDesc = checkStatusDesc;
    }

    public static OpenAcctPreBizEnum match(String status) {
        OpenAcctPreBizEnum resultEnum = null;
        if (StringUtils.isNotBlank(status)) {
            for (OpenAcctPreBizEnum value : OpenAcctPreBizEnum.values()) {
                if (value.getBizCode().equals(status)) {
                    resultEnum = value;
                }
            }
            return ObjUtils.isNull(resultEnum) ? BIZ_CODE_8 : resultEnum;
        }
        return BIZ_CODE_8;
    }
}
