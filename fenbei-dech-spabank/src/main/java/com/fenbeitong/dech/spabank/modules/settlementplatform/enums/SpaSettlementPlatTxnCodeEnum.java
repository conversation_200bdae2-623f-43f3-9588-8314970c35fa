package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName SpaSettlementPlatTxnCodeEnum
 * @Description: 平安银行-云收款-接口名称枚举
 * <AUTHOR>
 * @Date 2021/10/15
 **/
public enum  SpaSettlementPlatTxnCodeEnum {

    /**
     * KFEJZB6248 实名开户 AutonymOpenCustAcctId
     */
    AUTONYM_OPEN_CUST_ACCT_ID("KFEJZB6248","V1.0/AutonymOpenCustAcctId"),

    /**
     * KFEJZB6048 查询银行提现退单信息	BankWithdrawCashBackQuery
     */
    BANK_WITHDRAW_CASH_BACK_QUERY("KFEJZB6048","V1.0/BankWithdrawCashBackQuery"),

    /**
     * KFEJZB6240 会员绑定提现账户小额鉴权-校验法人 BindSmallAmountWithCheckCorp
     */
    BIND_SMALL_AMOUNT_WITH_CHECK_CORP("KFEJZB6240","V1.0/BindSmallAmountWithCheckCorp"),

    /**
     * KFEJZB6241 小额鉴权回填金额-校验法人	CheckAmountWithCorp
     */
    CHECK_AMOUNT_WITH_CORP("KFEJZB6241","V1.0/CheckAmountWithCorp"),

    /**
     * KFEJZB6142 查询明细单验证码	DetailVerifiedCodeQuery
     */
    DETAIL_VERIFIED_CODE_QUERY("KFEJZB6142","V1.0/DetailVerifiedCodeQuery"),

    /**
     * KFEJZB6007	会员资金冻结-不验证	MembershipTrancheFreeze
     */
//    MEMBERSHIP_TRANCHE_FREEZE("KFEJZB6007","V1.0/MembershipTrancheFreeze"),

    /**
     * KFEJZB6163	会员资金支付-不验证	MembershipTranchePay
     */
//    MEMBERSHIP_TRANCHE_PAY("KFEJZB6163","V1.0/MembershipTranchePay"),

    /**
     * KFEJZB6033	会员提现-不验证	MembershipWithdrawCash
     */
    MEMBERSHIP_WITHDRAW_CASH("KFEJZB6033","V1.0/MembershipWithdrawCash"),

    /**
     * KFEJZB6164	会员间交易退款-不验证	MemberTransactionRefund
     */
    MEMBER_TRANSACTION_REFUND("KFEJZB6164","V1.0/MemberTransactionRefund"),

    /**
     * KFEJZB6034	会员间交易-不验证	MemberTransaction
     */
    MEMBER_TRANSACTION("KFEJZB6034","V1.0/MemberTransaction"),

    /**
     * KFEJZB6037 查询会员子账号 QueryCustAcctId
     */
    QUERY_CUST_ACCT_ID("KFEJZB6037","V1.0/QueryCustAcctId"),

    /**
     * KFEJZB6103	查询对账文件信息	ReconciliationDocumentQuery
     */
    RECONCILIATION_DOCUMENT_QUERY("KFEJZB6103","V1.0/ReconciliationDocumentQuery"),

    /**
     * KFEJZB6110	查询银行单笔交易状态	SingleTransactionStatusQuery
     */
    SINGLE_TRANSACTION_STATUS_QUERY("KFEJZB6110","V1.0/SingleTransactionStatusQuery"),

    /**
     * KFEJZB6011	查询资金汇总账户余额	SupAcctIdBalanceQuery
     */
    SUP_ACCT_ID_BALANCE_QUERY("KFEJZB6011","V1.0/SupAcctIdBalanceQuery"),

    /**
     * KFEJZB6065	会员解绑提现账户	UnbindRelateAcct
     */
    UNBIND_RELATE_ACCT("KFEJZB6065","V1.0/UnbindRelateAcct"),

    /**
     * KFEJZB6138	维护会员绑定提现账户联行号	MntMbrBindRelateAcctBankCode
     */
    MNT_MBR_BIND_RELATE_ACCT_BANK_CODE("KFEJZB6138","V1.0/MntMbrBindRelateAcctBankCode"),

    /**
     * KFEJZB6098	会员绑定信息查询	MemberBindQuery
     */
    MEMBER_BIND_QUERY("KFEJZB6098","V1.0/MemberBindQuery"),

    /**
     * KFEJZB6244	登记行为记录信息	RegisterBehaviorRecordInfo
     */
    REGISTER_BEHAVIOR_RECORD_INFO("KFEJZB6244","V1.0/RegisterBehaviorRecordInfo"),

    /**
     * KFEJZB6139	登记挂账(支持撤销)	RegisterBillSupportWithdraw
     */
    REGISTER_BILL_SUPPORT_WITHDRAW("KFEJZB6139","V1.0/RegisterBillSupportWithdraw"),

    /**
     * KFEJZB6140	登记挂账撤销	RevRegisterBillSupportWithdraw
     */
    REV_REGISTER_BILL_SUPPORT_WITHDRAW("KFEJZB6140","V1.0/RevRegisterBillSupportWithdraw"),

    /**
     * KFEJZB6061	查询小额鉴权转账结果	SmallAmountTransferQuery
     */
    SMALL_AMOUNT_TRANSFER_QUERY("KFEJZB6061","V1.0/SmallAmountTransferQuery"),

    /**
     * KFEJZB6146	查询充值明细-见证收单	ChargeDetailQuery
     */
    CHARGE_DETAIL_QUERY("KFEJZB6146","V1.0/ChargeDetailQuery"),

    /**
     * KFEJZB6145	调账-见证收单	AccountRegulation
     */
    ACCOUNT_REGULATION("ACCOUNT_REGULATION","V1.0/AccountRegulation"),

    /**
     * KFEJZB6147	平台补账-见证收单	PlatformAccountSupply
     */
    PLATFORM_ACCOUNT_SUPPLY("KFEJZB6147","V1.0/PlatformAccountSupply"),

    /**
     * KFEJZB6010	查询银行子账户余额	CustAcctIdBalanceQuery
     */
    CUST_ACCT_ID_BALANCE_QUERY("KFEJZB6010","V1.0/CustAcctIdBalanceQuery"),

    /**
     * KFEJZB6072	查询银行时间段内交易明细	BankTransactionDetailsQuery
     */
    BANK_TRANSACTION_DETAILS_QUERY("KFEJZB6072","V1.0/BankTransactionDetailsQuery"),

    /**
     * KFEJZB6050	查询普通转账充值明细	CommonTransferRechargeQuery
     */
    COMMON_TRANSFER_RECHARGE_QUERY("KFEJZB6050","V1.0/CommonTransferRechargeQuery"),

    /**
     * KFEJZB6238	会员绑定提现账户银联鉴权-校验法人	BindUnionPayWithCheckCorp
     */
    BIND_UNION_PAY_WITH_CHECK_CORP("KFEJZB6238","V1.0/BindUnionPayWithCheckCorp"),

    /**
     * KFEJZB6239	银联鉴权回填短信码-校验法人	CheckMsgCodeWithCorp
     */
    CHECK_MSG_CODE_WITH_CORP("KFEJZB6239","V1.0/CheckMsgCodeWithCorp"),

    /**
     * KFEJZB6294 会员绑定提现账户-二类户 BindTwoLevelOfAccount
     */
    BIND_TWO_LEVEL_OF_ACCOUNT("KFEJZB6294","V1.0/BindTwoLevelOfAccount"),
    
    /**
     * 更新企业名称
     */
    MEMBER_INFORMAT_CHANGE("KFEJZB6296", "V1.0/MemberInformationChange"),

    /**
     * KFEJZB6083	申请修改手机号码	ApplyForChangeOfCellPhoneNum	用于有需要进行短信动态码验证的平台，申请修改会员手机号码。
     */
    APPLY_FOR_CHANGE_OF_CELL_PHONE_NUM("KFEJZB6083", "V1.0/ApplyForChangeOfCellPhoneNum"),
    /**
     * KFEJZB6084	回填动态码-修改手机	BackfillDynamicPassword	用于申请修改手机号码的会员，回填手机动态码发送至银行。
     */
    BACK_FILL_DYNAMIC_PASSWORD("KFEJZB6084", "V1.0/BackfillDynamicPassword"),


    /**
     * KFEJZB6324
     * 见证子台帐信息查询
     * EJZBCustInformationQuery
     * 见证子台账信息查询接口，支持产业结算通平台调用，查询子台账开立和补录的信息。
     * 备注: KFEJZB6244接口的签约结果可以通过此接口查询
     */
    EJZB_CUST_INFORMATION_QUERY("KFEJZB6324","V1.0/EJZBCustInformationQuery"),

    ;


    private String txnCode;
    private String serviceId;

    SpaSettlementPlatTxnCodeEnum(String txnCode, String serviceId) {
        this.txnCode = txnCode;
        this.serviceId = serviceId;
    }

    public String getTxnCode() {
        return txnCode;
    }

    public String getServiceId() {
        return serviceId;
    }
}
