package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName SingleTransactionStatusQueryReqDTO
 * @Description: KFEJZB6110	查询银行单笔交易状态	SingleTransactionStatusQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SingleTransactionStatusQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     *  2：会员间交易
     *  3：提现
     *  4：充值
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 交易网流水号	Y 20
     * 提现，充值或会员交易请求时的CnsmrSeqNo值,6216分账时请求时的SubOrderBussSeqNo（如果6216没有送，就用返回的前置流水号FrontSeqNo）
     */
    @JsonProperty(value = "TranNetSeqNo")
    private String tranNetSeqNo;

    /*
     * 见证子账户的账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 交易日期	Y 8
     * 格式：20181201
     * 当功能标志为2时，查询三天前的记录需上送交易日期
     */
    @JsonProperty(value = "TranDate")
    private String tranDate;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
