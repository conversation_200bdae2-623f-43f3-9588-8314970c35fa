package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:51:06
 * @Version 1.0
 **/
@Data
public class TransferInResultQueryRespDTO extends SpaBankObpApiBaseRespDTO{
    // errCode String  代扣失败错误码
    private String errCode;
    // errMsg String  失败信息
    private String errMsg;
    // orderStatus String 2 指令状态  指令状态 0:初始值,1:已提交,2:未知,3:失败,4:成功
    private String orderStatus;
    // startTime String  代扣开始时间  精确到时分秒，格式 yyyy-mm-dd hh:mm:ss
    private String startTime;
    // resultTime String  代扣结束时间  精确到时分秒，格式 yyyy-mm-dd hh:mm:ss .如果orderStatus是成功：入账时间  如果orderStatus是失败：失败时间  其它状态为空值
    private String resultTime;
}
