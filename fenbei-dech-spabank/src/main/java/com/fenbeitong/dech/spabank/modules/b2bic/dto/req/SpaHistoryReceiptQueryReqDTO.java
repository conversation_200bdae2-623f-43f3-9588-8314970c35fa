package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryReturnRemittanceDetailsRespDTO
 * @Description: 3.11支付退票查询[4019]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaHistoryReceiptQueryReqDTO implements Serializable {
    /**
     * 账号,必输
     */
    private String OutAccNo;

    /**
     * 	记账起始日期，非必输
     * 	查询当日无需输入此字段
     *  查询历史回单必输(开始结束都需要输入，必须是历史日期)
     *  格式YYYYMMDD
     */
    private String AccountBeginDate;

    /**
     * 	记账结束日期,非必输
     * 	查询当日无需输入此字段
     *  查询历史回单必输(开始结束都需要输入，必须是历史日期)
     *  格式YYYYMMDD
     */
    private String AccountEndDate;
    /**
     * 核心流水号,非必输
     * 银行核心流水号、银行主机流水号。 取4005返回的HostFlowNo，4013返回的HostFlowNo.
     */
    private String HostFlow;
    /**
     * 	起始交易金额	C(13,2)	非必输
     */
    private String StartTranAmt;
    /**
     * 	结束交易金额	C(13,2)	非必输
     */
    private String EndTranAmt;
    /**
     * 	收款帐号	C(20)	非必输	对方帐号
     */
    private String InAccNo;
    /**
     * 	借贷标志	C(2)	非必输	D:借  C:贷
     */
    private String  DcFlag;
    /**
     排序方式	C(2)	非必输	0：默认排序
         1:交易时间从近至远
         2:交易时间从远至近
         3:金额升序(从小至大)
         4:金额降序(从大至小)
         5:回单号升序
         6:回单号降序
         7:业务编号升序
         8:业务编号降序
         9:核心流水号从近到远
         10:核心流水号从远到近
     */
    private String  SortType;
    /**
     * 币种	C(3)	非必输	默认RMB
     */
    private String  CCY;
    /**
     * 	回单类型	非必输
     * 	默认为ALL 全部，具体参考3.9代码对照表
     */
    private String  ReceiptType;
    /**
     * 子回单类型 非必输
     * 默认为ALL 全部，具体参考3.9代码对照表
     */
    private String  SubReceiptType;
    /**
     * 	记录起始号		非必输
     * 	记录起始号 用于分页
     *     默认：1
     *
     */
    private String  RecordStartNo;
    /**
     * 请求记录数		非必输
     * 分页条数最大100条
     * 默认：100
     */
    private String RecordNumber;
    /**
     * 	顺序号		非必输
     */
    private String SerialNo;
    /**
     * 	业务编码		非必输
     */
    private String BussNo;
    /**
     * 	打印标识		非必输
     */
    private String PrintFlag;
    /**
     * 	打印网点		非必输
     */
    private String PrintBranchId;
}
