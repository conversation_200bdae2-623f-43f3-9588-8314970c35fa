package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName UnbindRelateAcctReqDTO
 * @Description: KFEJZB6065	会员解绑提现账户	UnbindRelateAcct
 * <AUTHOR>
 * @Date 2021/10/19
 **/
@Data
public class UnbindRelateAcctReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 功能标志	Y 1
     * 1：解绑
     */
    @JsonProperty(value = "FunctionFlag")
    private String functionFlag;

    /*
     * 交易网会员代码	Y 32
     * 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的交易网代码，并且须用“|::|”(右侧)进行分隔。
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 子账户账号	Y 32
     * 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”(右侧)进行分隔。
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 待解绑的提现账户的账号	Y 32
     * 提现的银行卡
     */
    @JsonProperty(value = "MemberAcctNo")
    private String memberAcctNo;

    /*
     * 保留域	N 120
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
