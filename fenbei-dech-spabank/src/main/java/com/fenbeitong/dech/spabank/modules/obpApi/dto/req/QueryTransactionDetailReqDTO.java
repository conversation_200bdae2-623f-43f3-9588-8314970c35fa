package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 查询交易明细
 * @author: yanqiu.hu
 * @create: 2022-10-11 10:54:56
 * @Version 1.0
 **/
@Data
public class QueryTransactionDetailReqDTO implements Serializable {

    // businessNo String 是 业务请求流水号
    private String businessNo;
    // agreementNo String 是 协议号 联动二三开户/开户结果查询/账户信息查询会返回协议号 81ae29bfbf3543da8249d8e1624d6c5f
    private String agreementNo;
    // startDate String 是 开始日期 2022-10-01
    private String startDate;
    // endDate String 是 结束日期 2022-10-01
    private String endDate;
    // pageNo String 是 页码 分页参数，默认从第1页开始 1
    private String pageNo;
    // pageSize String 是 页数 分页参数，默认每页显示10条 10
    private String pageSize;

}
