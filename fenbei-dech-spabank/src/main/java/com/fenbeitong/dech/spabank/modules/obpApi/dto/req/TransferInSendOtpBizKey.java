package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-12 17:58:43
 * @Version 1.0
 **/
@Data
public class TransferInSendOtpBizKey implements Serializable {
    // {"idNo":"证件号码", "trueName":"客户姓名", "mobileNo":"预留手机号码", "fromAccNo":"扣款账号", "toAccNo":"收款账号", "transAmt":"交易金额"}
    private String idNo;
    private String trueName;
    private String mobileNo;
    private String fromAccNo;
    private String toAccNo;
    private String transAmt;
}
