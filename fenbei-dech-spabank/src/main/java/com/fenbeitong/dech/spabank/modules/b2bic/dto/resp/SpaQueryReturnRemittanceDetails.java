package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryTradeDetailsRespDTO
 * @Description: 3.9查询账户当日历史交易明细[4013]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement
@Data
public class SpaQueryReturnRemittanceDetails implements Serializable {


    /**
     * 			批次号	C(20)	N	若属于批内则返回批次号
     */
    private String ThirdVoucher;

    /**
     * 			原始交易对应转账凭证号	C(20)	Y	客户上送单笔凭证号
     */
    private String SThirdVoucher;

    /**
     * 			客户自定义凭证号	C(20)	N	用户输入则返回
     */
    private String CstInnerFlowNo;

    /**
     * 			交易日期时间	C(14)	Y	交易发送时间
     */
    private String TransDate;

    /**
     * 			本方帐号	C(20)	Y
     */
    private String PayAccNo;

    /**
     * 			对方帐号	C(30)	Y
     */
    private String OppAccNo;

    /**
     * 			收款方户名	C(30)	Y
     */
    private String OppAccName;

    /**
     * 			收款方开户行	C(30)	Y
     */
    private String OppBankName;

    /**
     * 		 交易金额	C(15)	Y
     */
    private String Amount;

    /**
     * 			币种	C(3)	Y	交易币种
     */
    private String CcyCode;

    /**
     * 			退票日期	C(8)	Y
     */
    private String RejectDate;

    /**
     * 			退票描述	C(30)	Y
     */
    private String RejectRemark;

    /**
     * 			银行流水号	C(32)	Y	银行交易流水号
     */
    private String FrontLogNo;

    /**
     * 			原始交易时间	C(15)	Y	原始交易时间
     */
    private String submitTime;

    /**
     * 			原始交易备注	C(30)	N	原始交易备注
     */
    private String Remark;


}
