package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName TradeEBankPayOrderRemarkDTO
 * @Description:
 * <AUTHOR>
 * @Date 2021/10/27
 **/
@Data
public class TradeEBankPayOrderRemarkDTO implements Serializable {

    @JsonProperty(value = "SFJOrdertype")
    private String sFJOrdertype = "1";

    private String remarktype = "JHS0100000";

    private String plantCode;

    private List<TradeEBankPayOrderRemarkListDTO> oderlist;
}
