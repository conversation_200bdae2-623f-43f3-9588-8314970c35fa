package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName DetailVerifiedCodeQueryReqDTO
 * @Description: KFEJZB6142	查询明细单验证码	DetailVerifiedCodeQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class DetailVerifiedCodeQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 原前置流水号	Y 32
     */
    @JsonProperty(value = "OldFrontSeqNo")
    private String oldFrontSeqNo;

    /*
     * 原交易类型	Y 2
     * 默认上送1-担保支付交易
     */
    @JsonProperty(value = "OldTranType")
    private String oldTranType = "1";

    /*
     * 保留域	N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
