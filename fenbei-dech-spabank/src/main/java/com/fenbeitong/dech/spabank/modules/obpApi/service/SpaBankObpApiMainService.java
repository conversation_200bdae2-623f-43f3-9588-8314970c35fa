package com.fenbeitong.dech.spabank.modules.obpApi.service;

import com.fenbeitong.dech.spabank.modules.obpApi.dto.req.*;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.*;

/**
 * @ClassName SpaBankObpApiMainService
 * @Description: 商户联营卡基础service
 * <AUTHOR>
 * @Date 2022/07/28
 **/
public interface SpaBankObpApiMainService {

    /**
     * /obp-api-ibank-acct.checkUserInfo
     * [账户]-开户前置校验职业家庭地址接口
     */
    CheckUserInfoRespDTO checkUserInfo(CheckUserInfoReqDTO checkUserInfoReqDTO);

    /**
     * /obp-api-ibank-linkedAcct.openAccountPre
     * [联动账户]-开户前置校验
     */
    OpenAccountPreRespDTO openAccountPre(OpenAccountPreReqDTO openAccountPreReqDTO);

    /**
     *  /obp-api-ibank-acct.wefileId
     * [联动账户]-获取上传影像的Token接口
     */
    ObtainFileTokenRespDTO obtainFileToken(ObtainFileTokenReqDTO obtainFileTokenReqDTO);

    /**
     *  /obp-api-ibank-acct.submitImage
     * [联动账户]-影像审核
     */
    SubmitImageRespDTO submitImage(SubmitImageReqDTO submitImageReqDTO);

    /**
     *  /obp-api-ibank-linkedAcct.linkedSendOtp
     * [联动账户]-联动账户-发送otp
     */
    LinkedSendOtpRespDTO linkedSendOtp(LinkedSendOtpReqDTO linkedSendOtpReqDTO);

    /**
     *  /obp-api-ibank-linkedAcct.openLinkedAccount
     * [联动账户]-II类户III类户联动开户
     */
    OpenLinkedAccountRespDTO openLinkedAccount(OpenLinkedAccountReqDTO openLinkedAccountReqDTO);

    /**
     * /obp-api-ibank-linkedAcct.queryOpenAccountResult
     * [联动账户]II类户III类户联动开户结果查询
     */
    QueryOpenLinkAccountResultRespDTO queryOpenLinkedAcctStatus(QueryOpenLinkedAccountResultReqDTO queryOpenLinkedAccountResultReqDTO);

    /**
     *  /obp-api-ibank-linkedAcct.queryAccountStatus
     * [联动账户]-开户状态查询
     */
    QueryAccountStatusRespDTO queryAccountStatus(QueryAccountStatusReqDTO queryAccountStatusReqDTO);

    /**
     *  /obp-api-ibank.commonSendOtp otp发送接口
     */
    CommonSendOtpRespDTO commonSendOtp(CommonSendOtpReqDTO commonSendOtpReqDTO);

    /**
     *  /obp-api-ibank-acct.qryBalanceInfo 余额查询
     */
    QueryBalanceRespDTO queryBalance(QueryBalanceReqDTO queryBalanceReqDTO);

    /**
     *  /obp-api-ibank-acct.toTransfer 平安二类户提现到绑定的他行一类户
     */
    ToTransferWithOtpRespDTO toTransferWithOtp(ToTransferWithOtpReqDTO toTransferWithOtpReqDTO);

    /**
     *  /obp-api-ibank-acct.toTransfer 平安二类户提现到绑定的他行一类户-新
     */
    ToTransferWithOtpRespDTO toTransferWithOtpNew(ToTransferWithOtpNewReqDTO toTransferWithOtpNewReqDTO);

    /**
     *  obp-api-ibank-acct.toTransferResultUpgrade 平安二类户提现到绑定的他行一类户结果查询
     */
    ToTransferResultRespDTO toTransferResult(ToTransferResultReqDTO toTransferWithOtpReqDTO);

    /**
     * /obp-api-ibank-acct.transferInPreCheck 代扣前置校验
     */
    TransferInPreCheckRespDTO transferInPreCheck(TransferInPreCheckReqDTO transferInPreCheckReqDTO);
    /**
     * obp-api-ibank-acct.transferInApplySign 代扣交易签约申请
     */
    TransferInApplySignRespDTO transferInApplySign(TransferInApplySignReqDTO transferInApplySignReqDTO);
    /**
     * obp-api-ibank-acct.transferInApplySignVerify  代扣交易签约验证
     */
    TransferInApplySignVerifyRespDTO transferInApplySignVerify(TransferInApplySignVerifyReqDTO transferInApplySignVerifyReqDTO);
    /**
     * obp-api-ibank-acct.queryTransferInApplySignResultSmart 代扣交易签约查询，
     */
    QueryTransferInApplySignResultSmartRespDTO queryTransferInApplySignResultSmart(QueryTransferInApplySignResultSmartReqDTO queryTransferInApplySignResultSmartReqDTO);
    /**
     * obp-api-ibank-acct.queryBankTransferLimitSmart 代扣银行卡限额查询
     */
    QueryBankTransferLimitSmartRespDTO queryBankTransferLimitSmart(QueryBankTransferLimitSmartReqDTO queryBankTransferLimitSmartReqDTO);
    /**
     * obp-api-ibank-acct.transferIn 代扣。从绑定卡代扣金额到平安银行二类户卡上。最低代扣2元。代扣限额不够时用户也可以通过绑定卡银行APP或网银转账到平安银行二类户卡号。一般实时返回结果。
     */
    TransferInRespDTO transferIn(TransferInReqDTO transferInReqDTO);
    /**
     * obp-api-ibank-acct.transferInResultQuery 代扣结果查询
     */
    TransferInResultQueryRespDTO transferInResultQuery(TransferInResultQueryReqDTO transferInResultQueryReqDTO);
    /**
     * obp-api-ibank-acct.qryTransListOutLogin 出入金交易明细
     */
    QryTransListOutLoginRespDTO qryTransListOutLogin(QryTransListOutLoginReqDTO qryTransListOutLoginReqDTO);
    /**
     * obp-api-ibank-acct.geth5faceid	获取腾讯H5人脸识别流水号
     */
    GetH5FaceIdRespDTO getH5FaceId(GetH5FaceIdReqDTO getH5AceIdReqDTO);
    /**
     * obp-api-ibank-acct.geth5authresult  获取腾讯h5人脸比对结果
     */
    GetH5AuthResultRespDTO getH5AuthResult(GetH5AuthResultReqDTO getH5AuthResultReqDTO);
    /**
     * applyAccountRefund 申请划扣二三类户资金至平台监管户
     */
    ApplyAccountRefundRespDTO applyAccountRefund(ApplyAccountRefundReqDTO applyAccountRefundReqDTO);
    /**
     * qryRefundState 查询划扣二三类户资金的流水状态
     */
    QryRefundStateRespDTO qryRefundState(QryRefundStateReqDTO qryRefundStateReqDTO);
    /**
     * obp-api-ibank-acct.queryTransactionDetail
     * 查询交易明细接口
     */
    QueryTransactionDetailRespDTO queryTransactionDetail(QueryTransactionDetailReqDTO queryTransactionDetailReqDTO);

    /**
     * obp-api-ibank-acct.queryLinkedAcct
     * @param queryLinkedAcctReqDTO
     * @return
     */
    QueryLinkedAcctDetailDTO queryLinkedAcct(QueryLinkedAcctReqDTO queryLinkedAcctReqDTO);

}
