package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2023-02-20 15:44:43
 * @Version 1.0
 **/
@Data
public class ApplyAccountRefundRespDTO extends SpaBankObpApiBaseRespDTO {
    // retCode String 是 - 返回编码 - 0
    private String retCode;
    // retMsg String 是 - 返回信息 - success
    private String retMsg;
    // acceptSeqNo String 是 - 受理流水号 - 4971042205060020000000
    private String acceptSeqNo;
}
