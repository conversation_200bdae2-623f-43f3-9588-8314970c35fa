package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName ReconciliationDocumentQueryRespDTO
 * @Description: KFEJZB6103	查询对账文件信息	ReconciliationDocumentQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class ReconciliationDocumentItemDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 文件名称	Y 120
     */
    @JsonProperty(value = "FileName")
    private String fileName;

    /*
     * 随机密码	Y 120
     */
    @JsonProperty(value = "RandomPassword")
    private String randomPassword;

    /*
     * 文件路径	Y 512
     */
    @JsonProperty(value = "FilePath")
    private String filePath;

    /*
     * 提取码	Y 64
     */
    @JsonProperty(value = "DrawCode")
    private String drawCode;
}
