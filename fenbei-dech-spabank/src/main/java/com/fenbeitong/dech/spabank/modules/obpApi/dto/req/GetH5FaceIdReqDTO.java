package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2023-02-22 16:50:13
 * @Version 1.0
 **/
@Data
public class GetH5FaceIdReqDTO implements Serializable {
    // backUrl	String	是	1024	人脸完成后前端回跳地址	人脸完成后前端回跳地址	https://test-b-fat.pingan.com.cn/is/pension/open-acc/open.html
    private String backUrl;
    // agreementNo	String	是	-	协议号	二三类户协议号	b634dfb8b18f49b484b1f2b64950ba82
    private String agreementNo;
    // deviceID	String	否	-	设备ID	设备标识(监控需要)	90:E1:7B:3F:B9:31
    private String deviceID;
    // clientIP	String	是	-	IP地址	客户端IP地址	*************
    private String clientIP;
    // businessNo	String	是	-	请求流水号	32位请求流水号，OB（固定字母，标记开放银行渠道） + appId（10位，不够10位的在左边补0）+ 两位业务代码（默认为00进行填充） + yyyyMMdd（年月日，8位）+ 10位序列（数字，不够10位左边补0）	OB8870a9462e00202107131884526164
    private String businessNo;
}
