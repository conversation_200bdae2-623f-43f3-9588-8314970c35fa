package com.fenbeitong.dech.spabank.modules.cloudreceipt.constant;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.spabank.vo.ErrorVo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName CloudReceiptResult
 * @Description: 返回工具
 * <AUTHOR>
 * @Date 2021/10/25
 **/
@Data
public class CloudReceiptResult<T> {


    @JsonProperty(value = "Code")
    private String code;

    @JsonProperty(value = "Message")
    private String message;

    @JsonProperty(value = "Errors")
    private List<ErrorVo> errors;

    @JsonProperty(value = "Data")
    private T data;

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public List<ErrorVo> getErrors() {
        return errors;
    }

    public T getData() {
        return data;
    }
}
