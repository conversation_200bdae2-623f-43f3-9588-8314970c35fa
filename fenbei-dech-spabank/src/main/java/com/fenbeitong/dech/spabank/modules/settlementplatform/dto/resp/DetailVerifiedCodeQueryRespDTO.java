package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName DetailVerifiedCodeQueryRespDTO
 * @Description: KFEJZB6142	查询明细单验证码	DetailVerifiedCodeQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class DetailVerifiedCodeQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 原前置流水号	Y 32
     */
    @JsonProperty(value = "OldFrontSeqNo")
    private String oldFrontSeqNo;

    /*
     * 明细单验证码	Y 20
     */
    @JsonProperty(value = "DetailCheckCode")
    private String detailCheckCode;

    /*
     * 保留域	N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
