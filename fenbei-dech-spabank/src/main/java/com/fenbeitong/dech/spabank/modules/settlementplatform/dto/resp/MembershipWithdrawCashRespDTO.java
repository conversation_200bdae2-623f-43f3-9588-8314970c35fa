package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MembershipWithdrawCashReqDTO
 * @Description: KFEJZB6033	会员提现-不验证	MembershipWithdrawCash
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class MembershipWithdrawCashRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 见证系统流水号	Y 16
     */
    @JsonProperty(value = "FrontSeqNo")
    private String frontSeqNo;

    /*
     * 转账手续费	Y 15
     * 固定返回0.00
     */
    @JsonProperty(value = "TransferFee")
    private String transferFee;

    /*
     * 保留域	Y 4
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
