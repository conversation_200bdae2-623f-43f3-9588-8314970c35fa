package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

/**
 * @ClassName TransactionStatusQueryFlagEnum
 * @Description: 查询银行单笔交易状态
 * <AUTHOR>
 * @Date 2021/10/26
 **/
public enum TransactionStatusQueryFlagEnum {

    /*
     * 功能标志	Y 1
     *  2：会员间交易
     *  3：提现
     *  4：充值
     */
    TRANSACTION("2","会员间交易"),
    WITHDRAWAL("3","提现"),
    RECHARGE("4","充值")
    ;

    private String flag;
    private String desc;

    TransactionStatusQueryFlagEnum(String flag, String desc) {
        this.flag = flag;
        this.desc = desc;
    }

    public String getFlag() {
        return flag;
    }

    public String getDesc() {
        return desc;
    }
}
