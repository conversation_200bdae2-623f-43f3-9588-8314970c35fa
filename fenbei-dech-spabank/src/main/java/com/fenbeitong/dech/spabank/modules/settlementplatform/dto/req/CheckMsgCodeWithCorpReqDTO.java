package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName CheckAmountWithCorpReqdto
 * @Description: KFEJZB6241	小额鉴权回填金额-校验法人	CheckAmountWithCorp
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class CheckMsgCodeWithCorpReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 交易网会员代码	Y 32
     * "交易网会员代码即会员在平台端系统的会员编号
若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的交易网代码，并且须用“|::|”（右侧）进行分隔。"
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 子账户账号	Y 32
     * "系统返回的子账户帐号
若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”（右侧）进行分隔。"
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 会员账号	Y 32
     * 提现的银行卡
     */
    @JsonProperty(value = "MemberAcctNo")
    private String memberAcctNo;

    /*
     * 验证码	Y 32
     * 验证码
     */
    @JsonProperty(value = "MessageCheckCode")
    private String messageCheckCode;
    /*
     * 保留域	Y 4
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
