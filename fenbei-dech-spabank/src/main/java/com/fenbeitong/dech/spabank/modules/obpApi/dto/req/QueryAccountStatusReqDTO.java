package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class QueryAccountStatusReqDTO implements Serializable {

    // 是否虚拟卡业务 目前业务报销卡需要上送subAppId， 虚拟卡不需要上送subAppId
    private Boolean isBankCard;
    //	idType	证件类型	string	非必须		1：身份证（有协议号可不传）
    private String  idType;
    //	idNo	证件号码	string	非必须		证件号码（有协议号可不传）
    private String  idNo;
    //	trueName	姓名	string	非必须		客户姓名（有协议号可不传）
    private String  trueName;
    //	agreementNo	协议号	string	非必须		二类户协议号（有客户信息可不传）
    private String  agreementNo;

    // 子商户号
    private String subAppId;

}
