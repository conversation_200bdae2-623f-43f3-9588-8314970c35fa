package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName AutonymOpenCustAcctIdReqDTO
 * @Description: KFEJZB6248 实名开户
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class AutonymOpenCustAcctIdRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 子账户账号 Y 32
     * 系统返回的子账户帐号
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 保留域	N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
