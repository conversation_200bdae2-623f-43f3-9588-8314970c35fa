package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryTradeDetailsRespDTO
 * @Description: 3.9查询账户当日历史交易明细[4013]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement
@Data
public class SpaHistoryReceiptQueryDetails implements Serializable {


    /**
     *  记录序号	Char(30)
     */
    private String SeqNo;
    /**
     * 	回单号	Char(20)
     */
    private String ReceiptNo;
    /**
     * 	验证码	Char(20)
     */
    private String ValidateNo;
    /**
     * 	回单类型	Char(20)
     */
    private String ReceiptType;
    /**
     * 	回单子类	Char(20)
     */
    private String SubReceiptType;
    /**
     *  	记账日期	Char(20)
     */
    private String AccountDate;
    /**
     * 	主张号	Char(50)
     */
    private String MainAcc;
    /**
     * 	子帐号	Char(50)
     */
    private String SubAccNo;
    /**
     * 	付款账户	Char(50)
     */
    private String OutAccNo;
    /**
     * 	付款户名	Char(100)
     */
    private String OutAccName;
    /**
     * 	付款银行名称	Char(100)
     */
    private String OutBranchName;
    /**
     * 	借贷标志	Char(100)
     */
    private String DcFlag;
    /**
     * 	收款方账户	Char(100)
     */
    private String InAccNo;
    /**
     * 	收款方户名	Char(100)
     */
    private String InAccName;
    /**
     * 	收款方银行名称	Char(100)
     */
    private String InBranchName;
    /**
     * 	交易金额	Char(100)
     */
    private String Amount;
    /**
     * 	币种	Char(100)
     */
    private String CcyCode;
    /**
     * 	备注	Char(100)
     */
    private String Remark;
    /**
     * 	主机流水	Char(100)
     */
    private String HostFlowNo;
    /**
     * 	回单名称	Char(100)
     */
    private String ReceiptName;


}
