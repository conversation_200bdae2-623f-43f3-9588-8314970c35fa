package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName MemberBindQueryRespDTO
 * @Description: KFEJZB6098	会员绑定信息查询	MemberBindQuery
 * <AUTHOR>
 * @Date 2022/08/01
 **/
@Data
public class MemberBindQueryArrayDTO implements Serializable {


    /*
     * 		资金汇总账号	string(32)
     */
    @JsonProperty(value = "FundSummaryAcctNo")
    private String fundSummaryAcctNo;

    /*
     * 		见证子账户的账号	string(32)
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 		交易网会员代码	string(32)
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 		会员名称	string(120)
     */
    @JsonProperty(value = "MemberName")
    private String memberName;

    /*
     * 		会员证件类型	string(2)		见“会员接口证件类型说明”例如身份证，送1。
     */
    @JsonProperty(value = "MemberGlobalType")
    private String memberGlobalType;

    /*
     * 		会员证件号码	string(20)
     */
    @JsonProperty(value = "MemberGlobalId")
    private String memberGlobalId;

    /*
     * 		会员绑定账户的账号	string(32)		提现的银行卡
     */
    @JsonProperty(value = "MemberAcctNo")
    private String memberAcctNo;

    /*
     * 		会员绑定账户的本他行类型	string(1)		1：本行 2：他行
     */
    @JsonProperty(value = "BankType")
    private String bankType;

    /*
     * 	会员绑定账户的开户行名称	string(120)
     */
    @JsonProperty(value = "AcctOpenBranchName")
    private String acctOpenBranchName;

    /*
     * 		会员绑定账户的开户行的联行号	string(14)
     */
    @JsonProperty(value = "CnapsBranchId")
    private String cnapsBranchId;

    /*
     * 		会员绑定账户的开户行的超级网银行号	string(14)
     */
    @JsonProperty(value = "EiconBankBranchId")
    private String eiconBankBranchId;

    /*
     * 		会员的手机号	string(12)
     */
    @JsonProperty(value = "Mobile")
    private String mobile;


}
