package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:50:26
 * @Version 1.0
 **/
@Data
public class TransferInApplySignRespDTO  extends SpaBankObpApiBaseRespDTO {
    // bankName String 否 - 发卡行名称 发卡行名称 xx银行xx支行
    private String bankName;
    // channelCode String 否 - 通道代码 通道代码 8870a9462e
    private String channelCode;
    // otpSender String 否 - Otp发送者 （该字段最终以BIB返回为准）0-中国银联或发卡行，1-中国银联，2-发卡行 3：未知 2
    private String otpSender;
    // signId String 否 - 签约ID 申请签约流水号，验证接口需要上送 ****************
    private String signId;
}
