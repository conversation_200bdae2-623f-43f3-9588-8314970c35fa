package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName SmallAmountTransferQueryReqDTO
 * @Description: KFEJZB6061 查询小额鉴权转账结果 SmallAmountTransferQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SmallAmountTransferQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 原交易流水号	Y 20
     * 小额鉴权交易请求时的CnsmrSeqNo值（第一次申请成功时的流水号）
     */
    @JsonProperty(value = "OldTranSeqNo")
    private String oldTranSeqNo;

    /*
     * 交易日期	Y 8
     * 格式：********
     */
    @JsonProperty(value = "TranDate")
    private String tranDate;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
