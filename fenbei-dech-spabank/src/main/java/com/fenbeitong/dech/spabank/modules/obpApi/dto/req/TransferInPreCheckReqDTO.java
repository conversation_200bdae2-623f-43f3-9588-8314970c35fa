package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-10 20:02:10
 * @Version 1.0
 **/
@Data
public class TransferInPreCheckReqDTO implements Serializable {

    // businessNo String 32 请求流水号 Y
    private String businessNo;
    // transAmt string 20 交易金额 Y
    private String transAmt;
    // fromAccountNo string 32 扣款帐号 Y
    private String fromAccountNo;

}
