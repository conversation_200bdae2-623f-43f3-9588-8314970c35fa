package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName BankWithdrawCashBackDTO
 * @Description: 交易信息数组
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class BankWithdrawCashBackDTO {

    /*
     * 原提现的交易流水号	Y 30
     * 退票日期为查询日当天的情况下该字段为空
     */
    @JsonProperty(value = "OldTranSeqNo")
    private String oldTranSeqNo;

    /*
     * 原提现的见证系统流水号	Y 16
     * 退票日期为查询日当天的情况下该字段为空
     */
    @JsonProperty(value = "OldFrontSeqNo")
    private String oldFrontSeqNo;

    /*
     * 原提现的市场流水号	Y 32
     * 退票日期为查询日当天的情况下该字段为空
     */
    @JsonProperty(value = "OldMarketSeqNo")
    private String oldMarketSeqNo;

    /*
     * 原提现的附言信息	Y 120
     * 退票日期为查询日当天的情况下该字段为空
     */
    @JsonProperty(value = "OldAddMsg")
    private String oldAddMsg;

    /*
     * 退票原因	Y 120
     */
    @JsonProperty(value = "rejectBillReason")
    private String RejectBillReason;

    /*
     * 退票日期	Y 8
     * YYYYMMDD
     */
    @JsonProperty(value = "RejectBillDate")
    private String rejectBillDate;

    /*
     * 退票入账的交易流水号	Y 32
     */
    @JsonProperty(value = "RejectInAcctTranSeqNo")
    private String rejectInAcctTranSeqNo;

    /*
     * 退票入账的交易金额	Y 30
     * 元为单元，保留2位小数
     */
    @JsonProperty(value = "RejectInAcctTranAmt")
    private String rejectInAcctTranAmt;

    /*
     * 退票入账的付款账号	Y 50
     */
    @JsonProperty(value = "RejectInPayerAcctNo")
    private String rejectInPayerAcctNo;

    /*
     * 退票入账的付款户名	Y 50
     */
    @JsonProperty(value = "RejectInPayerAcctName")
    private String rejectInPayerAcctName;

    /*
     * 退票入账的付款方行号	Y 50
     */
    @JsonProperty(value = "RejectInPayerBranchId")
    private String rejectInPayerBranchId;

    /*
     * 退票入账的付款方行名	Y 50
     */
    @JsonProperty(value = "RejectInPayerBranchName")
    private String rejectInPayerBranchName;

    /*
     * 退票入账的收款方见证子账户	Y 20
     */
    @JsonProperty(value = "PayeeWitnessSubAcctNo")
    private String payeeWitnessSubAcctNo;

    /*
     * 退票入账的收款方见证系统流水号	Y 16
     */
    @JsonProperty(value = "PayeeFrontSeqNo")
    private String payeeFrontSeqNo;

    /*
     * 保留域1	Y 100
     * 小额鉴权退票返回退票入账业务流水号或提现退票入账核心流水号
     */
    @JsonProperty(value = "ReservedMsgOne")
    private String reservedMsgOne;

    /*
     * 保留域2	Y 100
     */
    @JsonProperty(value = "ReservedMsgTwo")
    private String reservedMsgTwo;

    /*
     * 保留域3	Y 100
     */
    @JsonProperty(value = "ReservedMsgThree")
    private String reservedMsgThree;
}
