package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * @ClassName SmallAmountTransferStatusEnum
 * @Description: 查询小额鉴权转账结果状态枚举
 * <AUTHOR>
 * @Date 2021/10/15
 */
@Getter
public enum SmallAmountTransferStatusEnum {

    /**
     * 0：成功，1：失败，2：待确认
     */
    SUCCESS("0","成功"),
    
    FAIL("1","失败"),
    
    PROCESSING("2","待确认");

    SmallAmountTransferStatusEnum(String status, String desc){
        this.status = status;
        this.desc = desc;
    }

    private String status;
    private String desc;

    public static boolean isSuccess(String status) {
        return StringUtils.isNotBlank(status) && SUCCESS.getStatus().equals(status);
    }

    public static boolean isFail(String status) {
        return StringUtils.isNotBlank(status) && FAIL.getStatus().equals(status);
    }

    public static boolean isProcessing(String status) {
        return StringUtils.isNotBlank(status) && PROCESSING.getStatus().equals(status);
    }
}
