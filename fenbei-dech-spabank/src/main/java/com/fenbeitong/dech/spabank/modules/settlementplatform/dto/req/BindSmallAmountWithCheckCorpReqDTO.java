package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName BindSmallAmountWithCheckCorpReqDTO
 * @Description: KFEJZB6240 会员绑定提现账户小额鉴权-校验法人 BindSmallAmountWithCheckCorp
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class BindSmallAmountWithCheckCorpReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 子账户账号	Y 32
     * "系统返回的子账户帐号
若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”（右侧）进行分隔。"
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 交易网会员代码	Y 32
     * "交易网会员代码即会员在平台端系统的会员编号
若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的交易网代码，并且须用“|::|”（右侧）进行分隔。"
     */
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /*
     * 会员名称	Y 120
     * 客户真实姓名
     */
    @JsonProperty(value = "MemberName")
    private String memberName;

    /*
     * 会员证件类型	Y 2
     * 详见《接口证件类型说明》
     */
    @JsonProperty(value = "MemberGlobalType")
    private String memberGlobalType;

    /*
     * 会员证件号码	Y 20
     * 身份证号码或统一社会信用代码证号
     */
    @JsonProperty(value = "MemberGlobalId")
    private String memberGlobalId;

    /*
     * 会员账号	Y 32
     * 提现的银行卡
     */
    @JsonProperty(value = "MemberAcctNo")
    private String memberAcctNo;

    /*
     * 银行类型	Y 1
     * 1：本行 2：他行
     */
    @JsonProperty(value = "BankType")
    private String bankType;

    /*
     * 开户行名称	Y 120
     * 填超级网银号对应的银行名称
     */
    @JsonProperty(value = "AcctOpenBranchName")
    private String acctOpenBranchName;

    /*
     * 大小额行号	N 14
     * 大小额行号和超级网银行号两者二选一必填。
     */
    @JsonProperty(value = "CnapsBranchId")
    private String cnapsBranchId;

    /*
     * 超级网银行号 N 14
     * 大小额行号和超级网银行号两者二选一必填。
     */
    @JsonProperty(value = "EiconBankBranchId")
    private String eiconBankBranchId;

    /*
     * 手机号码	Y 12
     * 手机号
     */
    @JsonProperty(value = "Mobile")
    private String mobile;

    /*
     * 个体工商户标志	N 1
     * 1：是 2：否(个人必输)
     */
    @JsonProperty(value = "IndivBusinessFlag")
    private String indivBusinessFlag;

    /*
     * 公司名称	N 120
     * 个体工商户必输
     */
    @JsonProperty(value = "CompanyName")
    private String companyName;

    /*
     * 公司证件类型	N 4
     * "个体工商户必输
证件类型仅支持73-统一社会信用代码、68-营业执照 、52-组织机构代码证"
     */
    @JsonProperty(value = "CompanyGlobalType")
    private String companyGlobalType;

    /*
     * 公司证件号码	N 32
     * 个体工商户必输
     */
    @JsonProperty(value = "CompanyGlobalId")
    private String companyGlobalId;

    /*
     * 店铺id	N 32
     * 个体工商户、企业必输
     */
    @JsonProperty(value = "ShopId")
    private String shopId;

    /*
     * 店铺名称	Y 120
     * 个体工商户、企业必输
     */
    @JsonProperty(value = "ShopName")
    private String shopName;

    /*
     * 是否存在经办人	N 1
     * 1：是 2：否（企业必输，个纯个人与个体工商户无需填写）
     */
    @JsonProperty(value = "AgencyClientFlag")
    private String agencyClientFlag;

    /*
     * 经办人姓名	Y 120
     * 是否存在经办人AgencyClientFlag=1时必输
     */
    @JsonProperty(value = "AgencyClientName")
    private String agencyClientName;

    /*
     * 经办人证件类型	Y 4
     * 是否存在经办人AgencyClientFlag=1时必输，仅支持1-身份证
     */
    @JsonProperty(value = "AgencyClientGlobalType")
    private String agencyClientGlobalType;

    /*
     * 经办人证件号	N 32
     * 是否存在经办人AgencyClientFlag=1时必输
     */
    @JsonProperty(value = "AgencyClientGlobalId")
    private String agencyClientGlobalId;

    /*
     * 经办人手机号	N 4
     * 是否存在经办人AgencyClientFlag=1时必输，短信指令号优先发送至此手机号
     */
    @JsonProperty(value = "AgencyClientMobile")
    private String agencyClientMobile;

    /*
     * 会员名称是否是法人	Y 1
     * 可选	1-是  2-否（个体工商户、企业必输，纯个人无需填写）
     */
    @JsonProperty(value = "RepFlag")
    private String repFlag;

    /*
     * 法人名称	N 120
     * 会员名称是否是法人RepFlag为2时必输、企业必输
     */
    @JsonProperty(value = "ReprName")
    private String reprName;

    /*
     * 法人证件类型	N 32
     * 会员名称是否是法人RepFlag为2时必输、企业必输,仅支持1-身份证；3-港澳回乡证；5-台胞证；19-外国护照
     */
    @JsonProperty(value = "ReprGlobalType")
    private String reprGlobalType;

    /*
     * 法人证件号码	N 32
     * 会员名称是否是法人RepFlag为2时必输、企业必输
     */
    @JsonProperty(value = "ReprGlobalId")
    private String reprGlobalId;

    /*
     * 保留域	Y 4
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

}
