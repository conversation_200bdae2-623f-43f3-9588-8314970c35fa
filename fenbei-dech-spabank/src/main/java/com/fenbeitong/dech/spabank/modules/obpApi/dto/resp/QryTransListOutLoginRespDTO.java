package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

import java.util.List;

/**
 * @description: 出入金交易明细
 * @author: yanqiu.hu
 * @create: 2022-10-11 10:54:38
 * @Version 1.0
 **/
@Data
public class QryTransListOutLoginRespDTO extends SpaBankObpApiBaseRespDTO {
    // count String 否 - 总数量 - 50
    private String count;
    // result_key String 否 - - - -
    private String result_key;
    // result_value	Array<Object>	否	-	账户列表	该客户拥有的账户列表，包括I类户、II类户	-
    private List<QryTransListOutLoginDetailDTO> result_value;
}
