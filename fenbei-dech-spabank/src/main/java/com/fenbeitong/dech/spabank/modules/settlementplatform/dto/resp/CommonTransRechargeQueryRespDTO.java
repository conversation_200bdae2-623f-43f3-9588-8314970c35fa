package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName BankTransDetailsQueryRespDTO
 * @Description: 查询交易明细
 * <AUTHOR>
 * @Date 2021/11/18
 **/
@Data
public class CommonTransRechargeQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 本次交易返回查询结果记录数    string(8)
     */
    @JsonProperty(value = "ResultNum")
    private String resultNum;

    /*
     * 起始记录号    string(8)
     */
    @JsonProperty(value = "StartRecordNo")
    private String startRecordNo;

    /*
     * 结束标志    string(1);;0：否  1：是
     */
    @JsonProperty(value = "EndFlag")
    private String endFlag;

    /*
     * 符合业务查询条件的记录总数    string(4);;重复次数（一次最多返回20条记录）
     */
    @JsonProperty(value = "TotalNum")
    private String totalNum;

    /*
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

    /*
     * 数组
     */
    @JsonProperty(value = "TranItemArray")
    private List<TranItemArray> tranItemArray;


    @Data
    public class TranItemArray {

        /*
         *      入账类型    string(1)
         *      02：会员充值/退票入账
         *      03：资金挂账"
         */
        @JsonProperty(value = "InAcctType")
        private String InAcctType;

        /*
         * 交易网会员代码    string(32)
         */
        @JsonProperty(value = "TranNetMemberCode")
        private String TranNetMemberCode;

        /*
         * 见证子帐户的帐号    string(32)
         */
        @JsonProperty(value = "SubAcctNo")
        private String SubAcctNo;

        /*
         * 入金金额    string(15)
         */
        @JsonProperty(value = "TranAmt")
        private String TranAmt;

        /*
         * 入金账号    string(32)
         */
        @JsonProperty(value = "InAcctNo")
        private String InAcctNo;

        /*
         * 入金账户名称    string(120)
         */
        @JsonProperty(value = "InAcctName")
        private String InAcctName;

        /*
         * 币种    string(3)
         */
        @JsonProperty(value = "Ccy")
        private String Ccy;

        /*
         * 会计日期    string(8);;即银行主机记账日期
         */
        @JsonProperty(value = "AccountingDate")
        private String AccountingDate;

        /*
         * 银行名称    string(120);;付款账户银行名称
         */
        @JsonProperty(value = "BankName")
        private String BankName;

        /*
         * 转账备注    string(120)
         */
        @JsonProperty(value = "Remark")
        private String Remark;

        /*
         * 见证系统流水号    string(16)
         */
        @JsonProperty(value = "FrontSeqNo")
        private String FrontSeqNo;
    }
}
