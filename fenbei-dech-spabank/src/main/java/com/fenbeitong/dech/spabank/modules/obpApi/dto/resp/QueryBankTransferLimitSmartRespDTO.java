package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:50:52
 * @Version 1.0
 **/
@Data
public class QueryBankTransferLimitSmartRespDTO extends SpaBankObpApiBaseRespDTO{
    // bankCode String  银行代码  queryType=0才会返回
    private String bankCode;
    // bankId String  银行联行号  queryType=0才会返回
    private String bankId;
    // bankName String  银行名称
    private String bankName;
    // canPay String  是否支持代扣
    private String canPay;
    // cardBin String  卡bin  queryType=0才会返回
    private String cardBin;
    // cardType String  卡类型  queryType=0才会返回 1-贷记卡 2-借记卡 3-预付费卡 4-准贷记卡
    private String cardType;
    // dayLimit String  单日限额，单位（元）
    private String dayLimit;
    // oneLimit String  单笔限额，单位（元）
    private String oneLimit;
    // bibOpenBankCode String  bib银行代码
    private String bibOpenBankCode;
}
