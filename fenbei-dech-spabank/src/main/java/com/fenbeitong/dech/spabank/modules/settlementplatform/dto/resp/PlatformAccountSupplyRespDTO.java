package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName PlatformAccountSupplyRespDTO
 * @Description: KFEJZB6147	平台补账-见证收单	PlatformAccountSupply
 * <AUTHOR>
 * @Date 2021/10/22
 **/
@Data
public class PlatformAccountSupplyRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 见证系统流水号	Y 16
     * 即电商见证宝系统生成的流水号，可关联具体一笔请求
     */
    @JsonProperty(value = "FrontSeqNo")
    private String frontSeqNo;

    /*
     * 见证子账户的账号	Y 32
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /**
     * 金额单位：分。 必填:Y
     */
    @JsonProperty(value = "Amt")
    private String Amt;

    /*
     * 备注	N 120
     */
    @JsonProperty(value = "Remark")
    private String remark;

    /*
     * 保留域  N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

}
