package com.fenbeitong.dech.spabank.modules.b2bic.Sign;

import com.fenbeitong.dech.spabank.modules.b2bic.Exception.CodeAndMsgException;
import com.fenbeitong.dech.spabank.modules.b2bic.Exception.ErrorInfo;
import com.fenbeitong.dech.spabank.modules.b2bic.Sign.sign.ISign;
import com.fenbeitong.dech.spabank.modules.b2bic.Sign.sign.ISignFactory;
import com.fenbeitong.dech.spabank.modules.b2bic.Sign.sign.signcfca.CfcaSign;
import com.fenbeitong.dech.spabank.modules.b2bic.Sign.sign.usbkey.IUKeyType;
import com.fenbeitong.dech.spabank.modules.b2bic.Sign.sign.usbkey.SM2Soft;
import com.fenbeitong.dech.spabank.modules.b2bic.Util.StringTool;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.*;

public class SignFactory implements ISignFactory {
    private static final Log log = LogFactory.getLog(SignFactory.class);

    private CfcaSign cfcaSign = null;

    private static Object locker = new Object();

    private static Set<String> methods = new HashSet<String>();

    static {
        methods.add("sign");
        methods.add("hashAndSign");
    }

    public ISign createSignTool(String pfxPath, String pfxPwd ) {
        Config config = Config.getInstance();
        String type = config.getSignMode();
        ISign tool = createSignTool(type, null,pfxPath,pfxPwd);
        return tool;
    }

    public ISign createSignTool(String type, Map param, String pfxPath, String pfxPwd) {
        Config config = Config.getInstance();
        if (type.equalsIgnoreCase(IUKeyType.SOFT)) {
            if (param == null || param.size() == 0) {
                // 从配置文件中读取参数
                synchronized (locker) {
                    if (cfcaSign == null) {
                        cfcaSign = new CfcaSign();
                        cfcaSign.setCaCertPath(config.getCaCertPath());
                        cfcaSign.setCrlPath(config.getCrlPath());
                        cfcaSign.setHashAlg(config.getHashAlg());
                        cfcaSign.setPfxPath(pfxPath);
                        cfcaSign.setPfxPwd(pfxPwd);
                        cfcaSign.setCheckCert(config.isCheckBankSign());
                        cfcaSign.setVerifyCertDNs(config.getBankCertDN());
                        try {
                            cfcaSign.init();
                        } catch (Exception e) {
                            FinhubLogger.error("Create signature class exception：" + StringTool.getErrorStack(e));
                            CodeAndMsgException exp = new CodeAndMsgException();
                            exp.setErrorCode(ErrorInfo.get("Sign_CODE"));
                            exp.setErrorMsg(ErrorInfo.get("Sign_INIT"));
                            throw exp;
                        }
                    }
                    return cfcaSign;
                }
            } else {
                // Web端访问
                CfcaSign signTool = new CfcaSign();
                signTool.setCaCertPath((String) param.get("caCertPath"));
                signTool.setCrlPath((String) param.get("crlPath"));
                String hashAlg = (String) param.get("hashAlg");
                if (hashAlg == null) {
                    hashAlg = Config.getInstance().getHashAlg();
                }
                signTool.setHashAlg(hashAlg);
                signTool.setPfxPath((String) param.get("pfxPath"));
                signTool.setPfxPwd((String) param.get("pfxPwd"));
                try {
                    signTool.init();
                } catch (Exception e) {
                    FinhubLogger.error("Create signature class exception：" + StringTool.getErrorStack(e));
                    CodeAndMsgException exp = new CodeAndMsgException();
                    exp.setErrorCode(ErrorInfo.get("Sign_CODE"));
                    exp.setErrorMsg(ErrorInfo.get("Sign_INIT"));
                }
                return signTool;
            }
        } else if (type.equalsIgnoreCase(IUKeyType.SM2_FILE))  {
            SM2Soft tool = new SM2Soft();
            tool.setHashAlg(config.getHashAlg());
            String subject = null;
            if (param == null || param.size() == 0) {
                //从配置文件中读取参数
                subject = config.getCertDn();
            } else {
                //web端访问-把页面的配置参数及时更改到sm的配置文件
                subject = (String) param.get("certDn");
                if(subject == null ){
                    FinhubLogger.error("Create signature class exception: SM2 signature test needs to be saved before verification:" + type);
                }

            }
            tool.setSubject(subject);
            return tool;
        }else{
            FinhubLogger.error( "Create signature class exception: Unsupported signature type:" + type);
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List findCerts(String type) throws Exception {
        List ret = null;
        if (type.equalsIgnoreCase(IUKeyType.SOFT)) {
            synchronized (locker) {
                if (cfcaSign == null) {
                    Config config = Config.getInstance();
                    cfcaSign = new CfcaSign();
                    cfcaSign.setCaCertPath(config.getCaCertPath());
                    cfcaSign.setCrlPath(config.getCrlPath());
                    cfcaSign.setHashAlg(config.getHashAlg());
                    cfcaSign.setPfxPath(config.getPfxPath());
                    cfcaSign.setPfxPwd(config.getPfxPwd());
                    cfcaSign.setCheckCert(config.isCheckBankSign());
                    cfcaSign.setVerifyCertDNs(config.getBankCertDN());
                    try {
                        cfcaSign.init();
                    } catch (Exception e) {
                        FinhubLogger.error("创建签名类异常：" + StringTool.getErrorStack(e));
                        CodeAndMsgException exp = new CodeAndMsgException();
                        exp.setErrorCode(ErrorInfo.get("Sign_CODE"));
                        exp.setErrorMsg(ErrorInfo.get("Sign_INIT"));
                        throw exp;
                    }
                }
                ret = new ArrayList();
                ret.add(cfcaSign.getSubjectDN());
            }
        } else {
            throw new IllegalArgumentException("Signature method not supported:" + type);
        }
        return ret;
    }

	@Override
	public ISign createSignTool(String signMode, Map param) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ISign createSignTool() throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

}

