package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

@Data
public class ObtainFileTokenRespDTO extends SpaBankObpApiBaseRespDTO {

    // wefileToken	认证	string	非必须
    private String wefileToken;

    // project	请求系统渠道ID	string	非必须
    private String project;

    // wefileUrl	文件上传地址	string	非必须
    private String wefileUrl;

    // uuid	随机字符串	string	非必须
    private String uuid;

    // expiredTime	过期时间	string	非必须
    private String expiredTime;
}
