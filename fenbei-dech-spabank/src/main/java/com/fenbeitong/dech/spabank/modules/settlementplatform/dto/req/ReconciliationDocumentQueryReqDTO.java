package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName ReconciliationDocumentQueryReqDTO
 * @Description: KFEJZB6103	查询对账文件信息	ReconciliationDocumentQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class ReconciliationDocumentQueryReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 文件类型	Y 2
     * 充值文件-CZ
     * 提现文件-TX
     * 交易文件-JY
     * 余额文件-YE
     * 鉴权文件-JQ
     * POS文件-POS
     * 资金汇总账户明细文件-JG
     * 平台归集账户明细文件-GJ
     */
    @JsonProperty(value = "FileType")
    private String fileType;

    /*
     * 文件日期	Y 10
     * 文件日期不能超过查询交易当天日期
     */
    @JsonProperty(value = "FileDate")
    private String fileDate;

    /*
     * 保留域	N 120
     * 保留域
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
