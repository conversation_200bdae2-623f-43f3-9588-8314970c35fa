package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:51:54
 * @Version 1.0
 **/
@Data
public class QueryBankTransferLimitSmartReqDTO implements Serializable {
    // businessNo String 32 业务请求流水号 Y
    private String businessNo;
    // agreeNo String 32 协议号 Y
    private String agreeNo;
    // queryType string 2 查询类型 Y 0：按照卡号查询，1按照银行名称查询默认填写0
    private String queryType = "0";
    // bankNames string 20 银行名称集合 N queryType=1必输， 示例：平安银行,招商银行，cardNos、bankNames二选一
    private String bankNames;
    // cardNos string 20 卡号集合 N queryType=0必输，示例：622,623。cardNos、bankNames二选一
    private String cardNos;
    // isCardSign string 2 是否需要卡号解签 Y 填写默认值0：不需要解签
    private String isCardSign = "0";
}
