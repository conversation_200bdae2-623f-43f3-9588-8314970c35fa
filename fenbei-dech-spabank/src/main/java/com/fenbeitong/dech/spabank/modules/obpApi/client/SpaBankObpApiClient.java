package com.fenbeitong.dech.spabank.modules.obpApi.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.dech.core.common.GlobalExceptionEnum;
import com.fenbeitong.dech.core.utils.FinDechException;
import com.fenbeitong.dech.spabank.modules.obpApi.constant.SpaObpApiMethodNameConstant;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.SpaBankObpApiBaseRespDTO;
import com.fenbeitong.dech.spabank.modules.obpApi.dto.resp.SpaBankObpApiRespDTO;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp.SpaBankObpApiErrorRespDTO;
import com.fenbeitong.dech.spabank.modules.settlementplatform.enums.SettlementPlatformRespEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.pingan.openbank.api.sdk.OpenBankApiClient;
import com.pingan.openbank.api.sdk.common.http.HttpResult;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName SpaBankObpApiClient
 * @Description: 商户联营交互请求类
 * <AUTHOR>
 * @Date 2022/07/28
 **/
@Component
public class SpaBankObpApiClient {


    public <T> T request(String key, String request, Class<T> responseT, String interfaceName) throws FinDechException {
        FinhubLogger.info("平安商户联营卡接口请求参数,appId={},request={},interfaceName={}", key, request, interfaceName);
        String response = null;
        long end = 0L;
        long start = 0L;
        JSONObject head = JSON.parseObject(request);
        try {
            start = System.currentTimeMillis();
            HttpResult result = OpenBankApiClient.invoke(key, interfaceName, head, null);
            end = System.currentTimeMillis();
            response = result.getData();
            FinhubLogger.info("平安商户联营卡接口返回，url={},耗时{}ms,request={},response={}",interfaceName, end-start, request, response);
            if (result.getCode() != 200){
                FinhubLogger.warn("平安银行-平安商户联营卡通讯异常,url={},耗时{}ms,request={},response={}", interfaceName, end-start, head.toJSONString(), response);
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_EXCEPTION);
            }
            if (ObjUtils.isBlank(response)){
                FinhubLogger.warn("平安银行-平安商户联营卡接口返回为NULL,url={},耗时{}ms,request={},response={}", interfaceName, end-start, head.toJSONString(), response);
                throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_RESPONSE_NULL);
            }
            T t = responseT.newInstance();
            SpaBankObpApiErrorRespDTO memberBindQueryRespDTO = JsonUtils.toObj(response, SpaBankObpApiErrorRespDTO.class);
            if (SettlementPlatformRespEnum.isFail(memberBindQueryRespDTO.getCode())) {
                SpaBankObpApiBaseRespDTO baseRespDTO = new SpaBankObpApiBaseRespDTO();
                baseRespDTO.setResponseCode(memberBindQueryRespDTO.getErrors().getErrorCode());
                baseRespDTO.setResponseMsg(memberBindQueryRespDTO.getErrors().getErrorMessage());
                baseRespDTO.setBizCode(memberBindQueryRespDTO.getErrors().getErrorCode());
                baseRespDTO.setBizMsg(memberBindQueryRespDTO.getErrors().getErrorMessage());
                return (T) JsonUtils.toObj(JsonUtils.toJson(baseRespDTO), t.getClass());
            }
            SpaBankObpApiRespDTO<T> dataRespDto = JsonUtils.toObj(response, SpaBankObpApiRespDTO.class);
            return (T) JsonUtils.toObj(JsonUtils.toJson(dataRespDto.getData()), t.getClass());
        } catch (Exception e) {
            end = System.currentTimeMillis();
            FinhubLogger.error("【平安商户联营卡接口请求异常】【耗时{}ms】,exception=",end-start,e);
            throw new FinDechException(GlobalExceptionEnum.BANK_SPA_OBPAPI_EXCEPTION);
        }
    }

    /**
     * 判断响应的结果时候包含responseCode字段
     *
     * @param interfaceName
     * @return
     */
    private Boolean hasResponseCode(String interfaceName) {
        // 特殊接口：底层有结算通逻辑，整体出参具有特殊性
        List<String> noResponseCodeList = Arrays.asList(SpaObpApiMethodNameConstant.QRY_REFUND_STATE, SpaObpApiMethodNameConstant.APPLY_ACCOUNT_REFUND);
        for (String noResponseCode : noResponseCodeList) {
            if (interfaceName.contains(noResponseCode)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }
}
