package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName SingleTransactionStatusQueryRespDTO
 * @Description: KFEJZB6110	查询银行单笔交易状态	SingleTransactionStatusQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class SingleTransactionStatusQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 记账标志	Y 1
     * 记账标志（1：登记挂账 2：支付 3：提现 4：清分5:下单预支付 6：确认并付款 7：退款 8：支付到平台 N:其他）
     * BookingFlagEnum
     */
    @JsonProperty(value = "BookingFlag")
    private String bookingFlag;

    /*
     * 交易状态	Y 1
     * （0：成功，1：失败，2：待确认, 5：待处理，6：处理中）
     * TranStatusEnum
     */
    @JsonProperty(value = "TranStatus")
    private String tranStatus;

    /*
     * 交易金额	Y 15
     * 包含手续费，即交易金额=实际到账金额+手续费
     */
    @JsonProperty(value = "TranAmt")
    private String tranAmt;

    /*
     * 交易日期	Y 8
     */
    @JsonProperty(value = "TranDate")
    private String tranDate;

    /*
     * 交易时间	Y 6
     */
    @JsonProperty(value = "TranTime")
    private String tranTime;

    /*
     * 转入子账户账号	Y 32
     */
    @JsonProperty(value = "InSubAcctNo")
    private String inSubAcctNo;

    /*
     * 转出子账户账号	Y 32
     */
    @JsonProperty(value = "OutSubAcctNo")
    private String outSubAcctNo;

    /*
     * 失败信息	Y 120
     * 当提现失败时，返回交易失败原因
     */
    @JsonProperty(value = "FailMsg")
    private String failMsg;

    /*
     * 原交易前置流水号	Y 32
     */
    @JsonProperty(value = "FunctionFlag")
    private String OldTranFrontSeqNo;
}
