package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

import java.util.List;

@Data
public class QueryAccountStatusRespDTO extends SpaBankObpApiBaseRespDTO {
    // 二类户卡号	非必须	二类户卡号
    private String secondAccountNo;
    // 二类户协议号	非必须	二类户协议号
    private String secondCardAgreementNo;
    // 二类户状态	非必须	二类户状态
    private String secondCardStatus;
    // 三类户卡号	非必须	三类户卡号
    private String thirdAccountNo;
    // 三类户协议号	非必须	三类户协议号
    private String thirdCardAgreementNo;
    // 三类户状态	非必须	三类户状态
    private String thirdCardStatus;
    // 0-未签约,1-已签约,2-已解除签约
    private String bibStatus;
    // 协议日限额	非必须	协议日限额
    private String bibDailyLimit;
    // 协议年限额	非必须	协议年限额
    private String bibYearLimit;
    // 协议当日可用限额	非必须	协议当日可用限额
    private String bibDailyAvailable;
    // 协议当年可用限额	非必须	协议当年可用限额
    private String bibYearAvailable;
    // 绑卡列表
    private List<QueryAccountBindCardDTO> bindCardList;

}
