package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @description: 申请修改手机号码，用于有需要进行短信动态码验证的平台，申请修改会员手机号码
 * @author: yanqiu.hu
 * @create: 2022-11-16 17:51:53
 * @Version 1.0
 **/
@Data
public class ApplyForChangeOfCellPhoneNumReqDTO extends SpaSettlementPlatBaseReqDTO {

    // 交易网会员代码 string(32) Y
    @JsonProperty(value = "TranNetMemberCode")
    private String tranNetMemberCode;

    /**
     * 见证子账户的账号 string(32) Y
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /**
     * 修改方式 string(2 ) Y "修改方式说明
     *
     * 修改方式说明：
     *     1- 短信验证码
     *     这种方式适合原手机还能接收到短信的。银行直接给原手机发短信，然后会员再回填收到的短信码，验证正确完成修改。
     *     2- 银联鉴权
     *     这种方式适合原手机已不能收到短信。银行会根据客户卡号，及提取后台已维护的证件信息、姓名，去验证新手机是否该银行卡的预留手机号码，确认客户身份后，给新号码发送动态码，回填成功完成修改。（这种方式只支持个人卡的修改）
     * @return
     */
    @JsonProperty(value = "ModifyType")
    private String modifyType;

    // 新手机号码 string(12) Y
    @JsonProperty(value = "NewMobile")
    private String newMobile;

    // 银行卡号 string(32) N 当修改方式为2时必输。
    @JsonProperty(value = "BankCardNo")
    private String bankCardNo;

    // 保留域 string(120) N
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;

}
