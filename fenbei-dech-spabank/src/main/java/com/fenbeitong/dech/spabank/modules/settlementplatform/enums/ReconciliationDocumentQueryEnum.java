package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @ClassName ReconciliationDocumentQueryEnum
 * @Description: 对账文件查询文件类型枚举
 * <AUTHOR>
 * @Date 2021/10/28
 **/
public enum ReconciliationDocumentQueryEnum {
    /*
     * 文件类型	Y 2
     * 充值文件-CZ
     * 提现文件-TX
     * 交易文件-JY
     * 余额文件-YE
     * 鉴权文件-JQ
     * POS文件-POS
     * 资金汇总账户明细文件-JG
     * 平台归集账户明细文件-GJ
     */
    CZ("CZ","充值"),
    TX("TX","提现"),
    JY("JY","交易"),
    YE("YE","余额"),
    JQ("JQ","鉴权"),
    POS("POS","POS"),
    PUB("PUB","对公付款"),
    ;

    private String fileType;
    private String desc;

    ReconciliationDocumentQueryEnum(String fileType, String desc) {
        this.fileType = fileType;
        this.desc = desc;
    }

    public String getFileType() {
        return fileType;
    }

    public String getDesc() {
        return desc;
    }

    public static List<ReconciliationDocumentQueryEnum> needReconciliationList() {
        List<ReconciliationDocumentQueryEnum> enums = Lists.newArrayList(CZ, TX, JY, YE, PUB);
        return enums;
    }

    public static ReconciliationDocumentQueryEnum getFileType(String fileType){
        for (ReconciliationDocumentQueryEnum value : ReconciliationDocumentQueryEnum.values()) {
            if(value.getFileType().equals(fileType)){
                return value;
            }
        }
        return null;
    }

    public static boolean isCz(String fileType){
        return CZ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isTx(String fileType){
        return TX.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isJy(String fileType){
        return JY.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isYe(String fileType){
        return YE.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isJQ(String fileType){
        return JQ.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isPOS(String fileType){
        return POS.getFileType().equals(fileType) ? true : false;
    }

    public static boolean isPub(String fileType){
        return PUB.getFileType().equals(fileType) ? true : false;
    }
}
