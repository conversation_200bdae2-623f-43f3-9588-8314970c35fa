package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaQueryTradeDetailsRespDTO
 * @Description: 3.9查询账户当日历史交易明细[4013]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement
@Data
public class SpaQueryTradeDetails implements Serializable {
    
    /**
     * 		主机记账日期	Char(8)
     */
    private String AcctDate;
    
    /**
     * 		交易时间	Char(6)
     */
    private String TxTime;
    
    /**
     * 		主机流水号	Char(32)		银行记账流水号
     */
    private String HostTrace;
    
    /**
     * 		业务流水号	Char(32)		银行业务流水号
     */
    private String BussSeqNo;
    
    /**
     * 		明细序号	Num(19)		明细序号，原来和核心水号一起区分交易唯一性
     */
    private String DetailSerialNo;
    
    /**
     * 		付款方网点号	Char(9)
     */
    private String OutNode;
    
    /**
     * 		付款方联行号	Char(16)
     */
    private String OutBankNo;
    
    /**
     * 		付款行名称	Char(120)
     */
    private String OutBankName;
    
    /**
     * 		付款方账号	Char(32)
     */
    private String OutAcctNo;
    
    /**
     * 		付款方户名	Char(120)
     */
    private String OutAcctName;
    
    /**
     * 		结算币种	Char(3)
     */
    private String CcyCode;
    
    /**
     * 		交易金额	Char (15)
     */
    private String TranAmount;
    
    /**
     * 		收款方网点号	Char(9)
     */
    private String InNode;
    
    /**
     * 		收款方联行号	Char(16)
     */
    private String InBankNo;
    
    /**
     * 		收款方行名	Char(120)
     */
    private String InBankName;
    
    /**
     * 		收款方账号	Char(32)
     */
    private String InAcctNo;
    
    /**
     * 		收款方户名	Char(120)
     */
    private String InAcctName;
    
    /**
     * 		借贷标志	Char(1)
     */
    private String DcFlag;
    
    /**
     * 		摘要，未翻译的摘要，如TRS	Char(120)
     */
    private String AbstractStr;
    
    /**
     * 		凭证号	Char(20)		空
     */
    private String VoucherNo;
    
    /**
     * 		手续费	Char (15)
     */
    private String TranFee;
    
    /**
     * 		邮电费	Char (15)
     */
    private String PostFee;
    
    /**
     * 		账户余额	Char (15)		重要提示：只提供历史明细的账户余额，不提供当日明细的账户余额，当日明细返回值为0。
     */
    private String AcctBalance;
    
    /**
     * 		用途，附言	定长报文无；
     *     Char (300)		只有在xml报文中有返回，定长报文无返回；客户端上送的用途。
     */
    private String Purpose;

    /**
     * 		中文摘要，AbstractStr的中文翻译	定长报文无；
     *     Char (100)		只有在xml报文中有返回，定长报文无返回
     */
    private String AbstractStr_Desc;

    /**
     * 		代理人户名	定长报文无；
     *     Char (100)	非必输	用于代理行支付功能
     */
    private String ProxyPayName;

    /**
     * 		代理人账号	定长报文无；
     *     Char (100)	非必输	用于代理行支付功能
     */
    private String ProxyPayAcc;

    /**
     * 		代理人银行名称	定长报文无；
     *     Char (100)	非必输	用于代理行支付功能
     */
    private String ProxyPayBankName;
    
    /**
     * 		主机日期	Char (8)		交易自然日期
     */
    private String HostDate;
    
    /**
     * 		备注1	Char (300)
     */
    private String Remark1;
    
    /**
     * 		备注2	Char (300)
     */
    private String Remark2;

}
