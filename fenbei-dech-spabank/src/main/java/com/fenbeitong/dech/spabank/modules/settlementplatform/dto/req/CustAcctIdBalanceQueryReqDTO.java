package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp.SpaSettlementPlatBaseRespDTO;
import lombok.Data;

/**
 * @ClassName CustAcctIdBalanceQueryReqDTO
 * @Description: 查询银行子账户余额
 * <AUTHOR>
 * @Date 2021/11/5
 **/
@Data
public class CustAcctIdBalanceQueryReqDTO extends SpaSettlementPlatBaseRespDTO {

    // 见证子账户的账号	string(32)	N	若QueryFlag为2时，子账号必输。
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;
    // 查询标志	string(1)	Y	"2：普通会员子账号 3：功能子账号"
    @JsonProperty(value = "QueryFlag")
    private String queryFlag;
    // 页码	string(6)	Y	起始值为1，每次最多返回20条记录，第二页返回的记录数为第21至40条记录，第三页为41至60条记录，顺序均按照建立时间的先后
    @JsonProperty(value = "PageNum")
    private String pageNum;
}
