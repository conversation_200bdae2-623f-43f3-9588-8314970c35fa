package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ToTransferSendOtpBizKey implements Serializable {
    // {"fromAccountNo":"转出卡号","toAccountNo":"收款方卡号","amount":"转账金额","fromAccountName":"转出户名","accountName":"收款方户名"}
    private String	fromAccountNo;
    private String	fromAccountName;
    private String	toAccountNo;
    private String	accountName;
    private String	amount;

}
