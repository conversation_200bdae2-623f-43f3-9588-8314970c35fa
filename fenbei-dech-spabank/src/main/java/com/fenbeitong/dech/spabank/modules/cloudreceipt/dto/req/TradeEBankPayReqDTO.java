package com.fenbeitong.dech.spabank.modules.cloudreceipt.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName TradeEBankPayReqDTO
 * @Description: 3.7.2 企业网银(B2B)支付(必接)
 * <AUTHOR>
 * @Date 2021/10/14
 **/
@Data
public class TradeEBankPayReqDTO extends SpaCloudReceiptBaseReqDTO {

    /*
     * 商户编号	Y 32
     * 商户在云收款系统的编号
     */
    @JsonProperty(value = "TraderNo")
    private String traderNo;

    /*
     * 商户订单号	Y 100
     * 商户系统生成的订单号
     */
    @JsonProperty(value = "TraderOrderNo")
    private String traderOrderNo;

    /*
     * 订单发送时间	Y 20
     * 商户系统订单生成时间 格式：yyyyMMddHHmmss
     */
    @JsonProperty(value = "OrderSendTime")
    private String orderSendTime;

    /*
     * 支付方式编号	Y 50
     * 固定传B2B
     */
    @JsonProperty(value = "PayModeNo")
    private String payModeNo = "B2B";

    /*
     * 交易金额	Y 20
     * 整数，单位为分
     */
    @JsonProperty(value = "TranAmt")
    private String tranAmt;

    /*
     * 订单名称	N 60
     * 订单名称
     */
    @JsonProperty(value = "OrderName")
    private String orderName;

    /*
     * 银行卡类型	N 2
     * 0：无卡类型
     * 1：借记卡 2：贷记卡
     * 固定传0
     */
    @JsonProperty(value = "SubType")
    private String subType = "0";

    /*
     * 银行编码	N 20
     * 银行代码(请查看6.2章节 企业网银(B2B)支持的银行)
     */
    @JsonProperty(value = "BankCode")
    private String bankCode;

    /*
     * 订单备注	N 300
     * 订单备注，选填
     * 见证宝商户必须上送，具体内容组成请查看6.10章节 见证宝订单备注字段规则
     */
    @JsonProperty(value = "OrderRemark")
    private String orderRemark;

    /*
     * 前端跳转url	Y 500
     * 前端跳转url
     */
    @JsonProperty(value = "FrontSkipUrl")
    private String frontSkipUrl;

    /*
     * 回调通知url	Y 500
     * 回调通知url
     */
    @JsonProperty(value = "CallBackNoticeUrl")
    private String callBackNoticeUrl;

    /*
     * 交易清算类型	N 20
     * 商户发交易时如果需要快速到账功能，必须上送T0/D0标识
     * TranSettleTypeEnum
     */
    @JsonProperty(value = "TranSettleType")
    private String tranSettleType;
}
