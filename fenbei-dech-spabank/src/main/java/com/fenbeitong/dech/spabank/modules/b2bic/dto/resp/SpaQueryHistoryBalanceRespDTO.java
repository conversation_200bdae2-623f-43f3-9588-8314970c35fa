package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @ClassName SpaQueryReceiptRespDTO
 * @Description: 3.2历史单笔PDF回单查询接口[ELC002]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryHistoryBalanceRespDTO extends SpaB2BICBaseRespDTO{

    /**
     * 			账号	C(20)	必输
     */
    private String Account;

    /**
     * 			币种	C(3)	必输
     */
    private String CcyCode;

    /**
     * 			历史日期	C(8)	必输
     */
    private String RptDate;

    /**
     * 			钞汇标志	C(1)	非必输
     */
    private String CcyType;

    /**
     * 			账单余额	NUM(15,2)	必输	账单余额，如包括智能存款、阶梯财富产品等计算出来的余额。和4013历史明细最后一笔交易后余额等价。
     */
    private String HisBalance;

    /**
     * 			账面余额	NUM(15,2)	非必输	账面余额，等于历史回单的余额
     */
    private String HisBookBalance;

}
