package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 09:52:00
 * @Version 1.0
 **/
@Data
public class TransferInReqDTO implements Serializable {
    // businessNo String 是 32 请求流水号 必输。需要和OTP发送接口中的businessNo的值一样 OB8870a9462e00202101074490097633
    private String businessNo;
    // agreeNo String 是 40 代扣协议号 必输，协议号，填：E000000518 443296199106099000
    private String agreeNo;
    // mobileNo String 否 18 扣款账号手机号 用户基本信息与协议号二选一 ***********
    private String mobileNo;
    // fromAccNo String 是 50 扣款账号 用户基本信息与协议号二选一，绑定卡卡号 ****************
    private String fromAccNo;
    // agreementNo String 是 - 账户协议号 账户开户/开户结果查询/账户信息查询/授权/授权查询返回的协议号 -
    private String agreementNo;
    // toAccType String 是 6 收款账号类型 必输，收款账号类型 ,收款方账号类型：1：平安I类户，2：平安II类户，3：平安III类户，这里为入账到II类户，请传“2” 2
    private String toAccType;
    // transAmt String 是 20 交易金额 必输，交易金额。最低2元。 2
    private String transAmt;
    // transChannelType String 否 10 终端类型 必输，终端类型01- PC 02- APP安卓 03- APP苹果 04- H5 2
    private String transChannelType;
    // clientIP String 否 - 客户端IP 非必输 ***************
    private String clientIP;
    // device String 否 - 设备指纹 非必输（设备唯一标识） N-A
    private String device;
    // gps String 否 - GPS 非必输 N-A
    private String gps;
    // transRemark String 否 200 备注 非必输，备注 xxx
    private String transRemark;
    // transScene String 是 20 代扣场景 必输，代扣场景，固定值，100030。 100030
    private String transScene = "100030";
    // scene String 是 10 场景号 必输，TI001：代表代扣 TI001
    private String scene = "TI001";
    // otpOrderNo String 是 32 OTP订单号 发OTP接口返回的OTP订单号 2.10E+21
    private String otpOrderNo;
    // otpValue String 是 10 短信验证码 短信验证码 112432
    private String otpValue;
}
