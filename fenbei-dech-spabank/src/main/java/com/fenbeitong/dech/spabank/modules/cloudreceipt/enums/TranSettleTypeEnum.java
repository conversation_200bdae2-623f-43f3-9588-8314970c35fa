package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

/**
 * @ClassName TranSettleTypeEnum
 * @Description: 交易清算类型T0/D0
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum TranSettleTypeEnum {

    /**
     * 商户发交易时如果需要快速到账功能，必须上送T0/D0标识
     */
    T0("T0","T0到账"),
    D0("D0","D0到账")
    ;

    TranSettleTypeEnum(String tranSettleType, String desc){
        this.tranSettleType = tranSettleType;
        this.desc = desc;
    }

    private String tranSettleType;
    private String desc;

    public String getTranSettleType() {
        return tranSettleType;
    }

    public String getDesc() {
        return desc;
    }
}
