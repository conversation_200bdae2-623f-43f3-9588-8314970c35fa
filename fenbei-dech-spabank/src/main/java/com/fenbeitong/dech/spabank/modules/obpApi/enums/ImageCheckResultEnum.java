package com.fenbeitong.dech.spabank.modules.obpApi.enums;

import com.fenbeitong.dech.spabank.modules.obpApi.constant.SpaObpApiCommonConstant;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 影响审核结果枚举
 */
public enum ImageCheckResultEnum {
    // 影像审核结果码值
    // 0-审核中
    CHECK_PROCESSING("0", "审核中", SpaObpApiCommonConstant.IMAGE_CHECK_PROCESSING),
    // 1-审核成功
    CHECK_SUCCESS("1", "审核成功", SpaObpApiCommonConstant.IMAGE_CHECK_SUCCEEDED),
    // 2-审核失败，
    CHECK_FAILED("2", "审核失败", SpaObpApiCommonConstant.IMAGE_CHECK_FAILED),
    // 3-人工审核中，
    CHECK_ARTIFICIAL("3", "人工审核中", SpaObpApiCommonConstant.IMAGE_CHECK_PROCESSING),
    // 4-待确认转人工；
    CHECK_TO_BE_CONFIRMED("4", "待确认转人工", SpaObpApiCommonConstant.IMAGE_CHECK_PROCESSING),
    // * 其他值：影像审核失败,
    ;

    // 审核结果
    private String checkResult;
    // 审核结果描述
    private String checkResultDesc;
    // 审核状态
    private String checkStatus;

    ImageCheckResultEnum(String checkResult, String checkResultDesc, String checkStatus) {
        this.checkResult = checkResult;
        this.checkResultDesc = checkResultDesc;
        this.checkStatus = checkStatus;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public String getCheckResultDesc() {
        return checkResultDesc;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public static Boolean isFail(String status) {
        ImageCheckResultEnum resultEnum = null;
        if (StringUtils.isNotBlank(status)) {
            for (ImageCheckResultEnum value : ImageCheckResultEnum.values()) {
                if (value.getCheckResult().equals(status)) {
                    resultEnum = value;
                }
            }
            return ObjUtils.isNull(resultEnum) || resultEnum.getCheckStatus().equals(SpaObpApiCommonConstant.IMAGE_CHECK_FAILED);
        }
        return false;
    }

    public static Boolean isProcessing(String status) {
        ImageCheckResultEnum resultEnum = null;
        if (StringUtils.isNotBlank(status)) {
            for (ImageCheckResultEnum value : ImageCheckResultEnum.values()) {
                if (value.getCheckResult().equals(status)) {
                    resultEnum = value;
                }
            }
            return resultEnum.getCheckStatus().equals(SpaObpApiCommonConstant.IMAGE_CHECK_PROCESSING);
        }
        return false;
    }

    public static Boolean isSuccess(String status) {
        ImageCheckResultEnum resultEnum = null;
        if (StringUtils.isNotBlank(status)) {
            for (ImageCheckResultEnum value : ImageCheckResultEnum.values()) {
                if (value.getCheckResult().equals(status)) {
                    resultEnum = value;
                }
            }
            return resultEnum.getCheckStatus().equals(SpaObpApiCommonConstant.IMAGE_CHECK_SUCCEEDED);
        }
        return false;
    }

    public static ImageCheckResultEnum match(String status) {
        ImageCheckResultEnum resultEnum = null;
        if (StringUtils.isNotBlank(status)) {
            for (ImageCheckResultEnum value : ImageCheckResultEnum.values()) {
                if (value.getCheckResult().equals(status)) {
                    resultEnum = value;
                }
            }
            return resultEnum;
        }
        return null;
    }
}
