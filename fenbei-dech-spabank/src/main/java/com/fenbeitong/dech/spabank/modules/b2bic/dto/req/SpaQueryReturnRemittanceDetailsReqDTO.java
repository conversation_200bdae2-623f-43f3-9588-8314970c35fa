package com.fenbeitong.dech.spabank.modules.b2bic.dto.req;

import com.fenbeitong.dech.spabank.modules.b2bic.dto.resp.SpaB2BICBaseRespDTO;
import com.fenbeitong.dech.spabank.modules.b2bic.dto.resp.SpaQueryReturnRemittanceDetails;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SpaQueryReturnRemittanceDetailsRespDTO
 * @Description: 3.11支付退票查询[4019]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaQueryReturnRemittanceDetailsReqDTO implements Serializable {

    /**
     * 	企业付款帐号	C(20)	Y
     */
    private String AccNo;

    /**
     * 	查询开始时间	C(8)	Y	yyyyMMdd 查询时间范围为30天
     */
    private String StartDate;

    /**
     * 	查询结束时间	C(8)	Y	yyyyMMdd查询时间范围为30天，包含次天
     */
    private String EndDate;

    /**
     * 	当前页码	C(10)	Y	从1开始递增
     */
    private String PageNo;

    /**
     * 	每页记录条数	C(5)	N	默认为30；最大300
     * 对同一个账户的分页查询此值保持一致
     */
    private String PageCts;

}
