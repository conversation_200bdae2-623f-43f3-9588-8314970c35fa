package com.fenbeitong.dech.spabank.modules.cz.service.impl;

import com.fenbeitong.dech.spabank.modules.cz.client.SpaCZApiClient;
import com.fenbeitong.dech.spabank.modules.cz.dto.*;
import com.fenbeitong.dech.spabank.modules.cz.enums.SpaCzTransCodeEnum;
import com.fenbeitong.dech.spabank.modules.cz.service.SpaCzAccountService;
import com.finhub.framework.core.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by FBT on 2023/8/15.
 */
@Service
public class SpaCzAccountServiceImpl implements SpaCzAccountService {

    @Autowired
    SpaCZApiClient spaCZApiClient;

    @Override
    public SpaCzBalanceQueryRespDto queryAccountBalanceInfo(SpaCzBalanceQueryReqDto spaCzBalanceQueryReqDto) {
        SpaCzBalanceQueryRespDto request = spaCZApiClient.request(JsonUtils.toJson(spaCzBalanceQueryReqDto), SpaCzBalanceQueryRespDto.class, SpaCzTransCodeEnum.STAR_BALANCE001.getTxnCode());
        return request;
    }

    @Override
    public SpaCzSignQueryRespDto querySignResult(SpaCzSignQueryReqDto spaCzSignQueryReqDto) {
        SpaCzSignQueryRespDto request = spaCZApiClient.request(JsonUtils.toJson(spaCzSignQueryReqDto), SpaCzSignQueryRespDto.class, SpaCzTransCodeEnum.STAR_SIGNSTATUS001.getTxnCode());
        return request;
    }
}
