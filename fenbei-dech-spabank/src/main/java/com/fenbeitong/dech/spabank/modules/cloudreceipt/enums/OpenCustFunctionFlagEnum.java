package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

/**
 * @ClassName OpenCustFunctionFlagEnum
 * @Description: 开户功能标识
 * <AUTHOR>
 * @Date 2021/10/25
 **/
public enum OpenCustFunctionFlagEnum {

    // 功能标识:1:开户 3:销户 4: 为存量见证子帐号申请智能收款子账号
    OPEN_ACCOUNT("1","开户"),
    CLOSED_ACCOUNT("3","销户"),
    APPLY_ACCOUNT("4","为存量见证子帐号申请智能收款子账号")
    ;
    private String flag;
    private String desc;

    OpenCustFunctionFlagEnum(String flag, String desc) {
        this.flag = flag;
        this.desc = desc;
    }

    public String getFlag() {
        return flag;
    }

    public String getDesc() {
        return desc;
    }
}
