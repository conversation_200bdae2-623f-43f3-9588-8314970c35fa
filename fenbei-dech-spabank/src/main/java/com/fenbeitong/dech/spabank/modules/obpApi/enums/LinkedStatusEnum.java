package com.fenbeitong.dech.spabank.modules.obpApi.enums;

import com.fenbeitong.dech.spabank.modules.obpApi.constant.SpaObpApiCommonConstant;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;

public enum LinkedStatusEnum {

    BUILDING("0", "未建立", SpaObpApiCommonConstant.IMAGE_CHECK_PROCESSING),
    BUILD_SUCCESS("1", "已建立", SpaObpApiCommonConstant.IMAGE_CHECK_SUCCEEDED),
    BUILD_FAILED("2", "建立失败", SpaObpApiCommonConstant.IMAGE_CHECK_FAILED),
    ;
    private String linkedStatus;

    private String linkedStatusDesc;

    private String openStatus;

    LinkedStatusEnum(String linkedStatus, String linkedStatusDesc, String openStatus) {
        this.linkedStatus = linkedStatus;
        this.linkedStatusDesc = linkedStatusDesc;
        this.openStatus = openStatus;
    }

    public String getLinkedStatus() {
        return linkedStatus;
    }

    public String getLinkedStatusDesc() {
        return linkedStatusDesc;
    }

    public String getOpenStatus() {
        return openStatus;
    }

    public static Boolean isProcessing(String status) {
        LinkedStatusEnum resultEnum = null;
        if (StringUtils.isNotBlank(status)) {
            for (LinkedStatusEnum value : LinkedStatusEnum.values()) {
                if (value.getLinkedStatus().equals(status)) {
                    resultEnum = value;
                }
            }
            return ObjUtils.isNull(resultEnum) || resultEnum.getOpenStatus().equals(SpaObpApiCommonConstant.IMAGE_CHECK_PROCESSING);
        }
        return false;
    }

    public static Boolean isFailed(String status) {
        return BUILD_FAILED.getLinkedStatus().equals(status);
    }
}
