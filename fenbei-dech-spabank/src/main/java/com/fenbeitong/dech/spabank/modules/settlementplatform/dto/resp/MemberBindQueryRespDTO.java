package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName MemberBindQueryRespDTO
 * @Description: KFEJZB6098	会员绑定信息查询	MemberBindQuery
 * <AUTHOR>
 * @Date 2022/08/01
 **/
@Data
public class MemberBindQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 	本次交易返回查询结果记录数	string(8)
     */
    @JsonProperty(value = "ResultNum")
    private String resultNum;

    /*
     * 	起始记录号	string(8)
     */
    @JsonProperty(value = "StartRecordNo")
    private String startRecordNo;

    /*
     * 	结束标志	string(1)		0：否  1：是
     */
    @JsonProperty(value = "EndFlag")
    private String endFlag;

    /*
     * 	符合业务查询条件的记录总数	string(4)		重复次数（一次最多返回20条记录）
     */
    @JsonProperty(value = "TotalNum")
    private String totalNum;

    @JsonProperty(value = "TranItemArray")
    private List<MemberBindQueryArrayDTO> tranItemArray;

}
