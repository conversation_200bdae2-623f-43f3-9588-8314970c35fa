package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 11:08:02
 * @Version 1.0
 **/
@Data
public class QueryTransactionDetailDTO implements Serializable {
    // 账户余额	账户余额	56735.2
    private	String	balance;
    // 交易币种	交易币种	RMB
    private	String	currType;
    // 本行卡	本行卡	6.22299E+15
    private	String	cardNo;
    // 借贷标志	C(贷）代表'收' ， D(借)代表‘支'　	C
    private	String	dcFlag;
    // 摘要	摘要	支出
    private	String	remark;
    // 摘要码标识	摘要码标识	12
    private	String	remarkFlag;
    // 交易金额	交易金额	100
    private	String	tranAmt;
    // 交易时间	交易时间	2022/10/26 15:54
    private	String	tranTime;
    // 交易流水号	交易流水号	3de76212fa6f6bc0a2b72fd7675e7413202210261554416160400100
    private	String	tranSysNo;
    @Deprecated
    private	String	bussinessNo;
    // 不是用tranSysNo去关联oriBussinessNo
    //新增的businessNo关联oriBussinessNo
    private	String	businessNo;
    // 原业务流水号	原业务流水号	4.06E+21
    private	String	oriBussinessNo;
    // 对手方客户姓名	对手方客户姓名	张三
    private	String	otherAcctName;
    // 对手方银行名称	对手方银行名称	中国银行
    private	String	otherBankName;
    // 对手卡类型	对手卡类型	借记卡
    private	String	otherCardType;
    // 对手方银行账号	对手方银行账号	6.21283E+18
    private	String	otherAcctNo;
    // 商户1名称	商户1名称	财付通
    private	String	merchant1Name;
    // 商户1ID	商户1ID	1E+15
    private	String	merchant1Id;
    // 商户2名称	商户2名称	濂溪区杨氏卤味十里店
    private	String	merchant2Name;
    // 商户2ID	商户2ID	*********
    private	String	merchant2Id;
    // 商户3名称	商户3名称	信阳卤肉分店
    private	String	merchant3Name;
    // 交易抬头	交易抬头	微信支付-濂溪区杨氏卤味十里店
    private	String	transTitle;
    // 交易大类id	交易大类id	61
    private	String	tradeClassId;
    // 交易大类	交易大类	支出
    private	String	tradeClass;
    // 交易分类名称id	交易分类名称id	88
    private	String	transCategoryId;
    // 交易分类名称	交易分类名称	其他支出
    private	String	transCategory;
}
