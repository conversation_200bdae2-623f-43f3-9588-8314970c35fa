package com.fenbeitong.dech.spabank.modules.cloudreceipt.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName CreateBillStatusEnum
 * @Description: 对账文件生成状态
 * <AUTHOR>
 * @Date 2021/10/14
 **/
public enum CreateBillStatusEnum {
    /**
     * 对账单状态(0初始状态,1对账成功,2,对账中,4对账失败
     */
    BILL_INIT("0","初始状态"),
    BILL_SUCCESS("1","对账成功"),
    BILL_PROCESSING("2","对账中"),
    BILL_FAIL("4","对账失败")
    ;

    CreateBillStatusEnum(String billStatus, String desc){
        this.billStatus = billStatus;
        this.desc = desc;
    }

    private String billStatus;
    private String desc;

    public String getBillStatus() {
        return billStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Boolean isSuccess(String status) {
        return StringUtils.isNotBlank(status) && BILL_SUCCESS.getBillStatus().equals(status) ? true : false;
    }
}
