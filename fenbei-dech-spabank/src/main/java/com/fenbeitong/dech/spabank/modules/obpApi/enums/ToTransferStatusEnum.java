package com.fenbeitong.dech.spabank.modules.obpApi.enums;

import com.fenbeitong.dech.common.until.enums.TradeStatusEnum;
import org.apache.commons.lang3.StringUtils;

public enum ToTransferStatusEnum {
    FAILED("0", "失败", TradeStatusEnum.FAILED),
    SUCCEEDED("1", "成功", TradeStatusEnum.SUCCEEDED),
    PROCESSING("2", "处理中", TradeStatusEnum.PROCESSING),
    ;

    private String transferStatus;

    private String transferStatusDesc;

    private TradeStatusEnum tradeStatusEnum;

    ToTransferStatusEnum(String transferStatus, String transferStatusDesc, TradeStatusEnum tradeStatusEnum) {
        this.transferStatus = transferStatus;
        this.transferStatusDesc = transferStatusDesc;
        this.tradeStatusEnum = tradeStatusEnum;
    }

    public String getTransferStatus() {
        return transferStatus;
    }

    public String getTransferStatusDesc() {
        return transferStatusDesc;
    }

    public TradeStatusEnum getTradeStatusEnum() {
        return tradeStatusEnum;
    }

    public static Boolean isSucceeded(String status){
        if(StringUtils.isNotBlank(status) && SUCCEEDED.getTransferStatus().equals(status)){
            return true;
        }
        return false;
    }

    public static Boolean isFailed(String status){
        if(StringUtils.isNotBlank(status) && FAILED.getTransferStatus().equals(status)){
            return true;
        }
        return false;
    }

    public static ToTransferStatusEnum match(String status) {
        ToTransferStatusEnum resultEnum = null;
        if (StringUtils.isNotBlank(status)) {
            for (ToTransferStatusEnum value : ToTransferStatusEnum.values()) {
                if (value.getTransferStatus().equals(status)) {
                    resultEnum = value;
                }
            }
            return resultEnum;
        }
        return null;
    }
}
