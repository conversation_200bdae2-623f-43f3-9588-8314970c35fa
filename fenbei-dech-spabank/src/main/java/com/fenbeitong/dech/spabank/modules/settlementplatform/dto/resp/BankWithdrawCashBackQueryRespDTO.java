package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName BankWithdrawCashBackQueryRespDTO
 * @Description: KFEJZB6048	查询银行提现退单信息	BankWithdrawCashBackQuery
 * <AUTHOR>
 * @Date 2021/10/15
 **/
@Data
public class BankWithdrawCashBackQueryRespDTO extends SpaSettlementPlatBaseRespDTO {

    /*
     * 本次交易返回查询结果记录数	Y 8
     */
    @JsonProperty(value = "ResultNum")
    private String resultNum;

    /*
     * 交易信息数组
     */
    @JsonProperty(value = "TranItemArray")
    private List<BankWithdrawCashBackDTO> tranItemArray;

}
