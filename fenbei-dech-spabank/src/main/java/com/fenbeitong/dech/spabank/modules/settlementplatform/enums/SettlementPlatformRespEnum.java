package com.fenbeitong.dech.spabank.modules.settlementplatform.enums;

import com.fenbeitong.dech.common.until.enums.TradeStatusEnum;
import com.luastar.swift.base.utils.ObjUtils;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CloudReceiptRespEnum
 * @Description: 云收款接口状态枚举
 * <AUTHOR>
 * @Date 2021/10/14
 */
@Getter
public enum SettlementPlatformRespEnum {

    E50000("E50000","业务处理失败（通讯成功）",TradeStatusEnum.FAILED),
    //    转入和转出子账号
    //    转出子账号不存在
    //    转入子账号不存在
    //    自营平台功能受限
    // 见证子账户的账号SubAcctNo上传错误
    ERR031("ERR031","见证子账户不存在或已失效",TradeStatusEnum.FAILED),
    // 借方子账户已被冻结,禁止出金
    // 子账户处于冻结状态
    E50058("E50058","子账户已被冻结,禁止出金",TradeStatusEnum.FAILED),
    E10002("E10002","金额格式错",TradeStatusEnum.FAILED), // 金额格式为数字带两位小数点，例如100.01
    ERR042("ERR042","金额须大于0元",TradeStatusEnum.FAILED), // 提现金额不允许小于等于0,手续费金额不允许小于0
    //    上送的子账户类型不正确目前仅支持三种类型
    //    1-会员子账户
    //    3-手续费子账户
    //    13-营销子账户
    // 提现账号TakeCashAcctNo命中信息异常黑名单
    E40018("E40018","不支持受理该类子账户提现的请求",TradeStatusEnum.FAILED),
    E40019("E40019","平台没有手续费手动提现权限",TradeStatusEnum.FAILED), // 在手续费子账户提现场景下，平台未配置允许手续费手动提现的参数
    // 上送的资金汇总账号FundSummaryAcctNo错误
    // 上送的子账户SubAcctNo没有绑定提现账户TakeCashAcctNo
    ERR033("ERR033","提现账户未绑定",TradeStatusEnum.FAILED),
    ERR221("ERR221","该子帐号禁止提现",TradeStatusEnum.FAILED), // 子账号SubAcctNo在禁止提现管控名单
    ERR222("ERR222","提现白名单验证失败",TradeStatusEnum.FAILED), // 子账号SubAcctNo不在允许提现的白名单
    E40042("E40042","提现金额超出单笔限定额度",TradeStatusEnum.FAILED), // 市场开启额度校验
    E50063("E50063","海外卡账户未配置实际提现账户",TradeStatusEnum.FAILED), // 海外卡账户未配置实际提现账户
    ERR923("ERR923","系统异常请联系后端",TradeStatusEnum.PROCESSING), // 系统级异常，一般是数据库错误因起，常见原因是字段过长或带特殊字符等原因导致数据库操作错误
    ERR936("ERR936","交易要素不全",TradeStatusEnum.FAILED), //必输字段未填，请自行对照接口文档检查报文
    ERR021("ERR021","错误的功能码",TradeStatusEnum.FAILED), //功能标识(FunctionFlag)上传错误不在枚举值内
    E10005("E10005","市场代码与资金汇总账号不一致或市场已注销",TradeStatusEnum.FAILED), //资金汇总账号(FundSummaryAcctNo)送错
    E85033("E85033","此支付流水已受理，请稍后查询状态",TradeStatusEnum.PROCESSING), //流水号(OrderNo)重复
    E10015("E10015","自营平台功能受限",TradeStatusEnum.FAILED), //自营平台不允许调功能分支4(FunctionFlag)
    E40011("E40011","6034接口不支持同名子账户支付",TradeStatusEnum.FAILED), //未配置调用功能分支4(FunctionFlag)的接口权限
    E40012("E40012","转出方和转入方都须为商户子账户",TradeStatusEnum.FAILED), //在调用功能分支4时(FunctionFlag)，转出方(OutSubAcctNo)和转入方(InSubAcctNo)有一个不是商户子账户
    E40013("E40013","转出方不存在同名子账户",TradeStatusEnum.FAILED), //在调用功能分支4时(FunctionFlag)，转出方子账户(OutSubAcctNo)未查到对应的同名子账户
    E40014("E40014","转出方同名子账户已失效",TradeStatusEnum.FAILED), //在调用功能分支4时(FunctionFlag)，转出方(OutSubAcctNo)对应的同名子账户失效
    E40015("E40015","可用余额不足",TradeStatusEnum.FAILED), //上送金额(TranAmt)大于可用金额
    E50050("E50050","市场未配置为资方类型,不可使用资方交易分支",TradeStatusEnum.FAILED), //在调用功能分支X时(FunctionFlag)，该平台未开通资方交易权限
    E40008("E40008","商户不支持使用6034接口转出到营销子账户",TradeStatusEnum.FAILED), //再调用功能分支为6或9时(FunctionFlag)，营销子账户为转入方时转出子账户必须是普通子账户(00)
    E10019("E10019","该接口不支持同名户互转",TradeStatusEnum.FAILED), //未配置该接口进行同名户互转的权限(SH→00)
    E50059("E50059","贷方子账户已被冻结,禁止入金",TradeStatusEnum.FAILED), //转入方子账户(InSubAcctNo)已被冻结，不允许进行转入
    ERR105("ERR105","订单号已存在",TradeStatusEnum.FAILED), //再调用功能分支1时(FunctionFlag)，订单号已存在
    ERR115("ERR115","订单号不存在",TradeStatusEnum.FAILED), //再调用功能分支2时(FunctionFlag)，订单号不存在
    F09001("F09001","系统异常请联系后端",TradeStatusEnum.PROCESSING), //系统性报错请直接联系银行，多数情况为因字段过长、主键冲突等原因导致的数据库操作失败。
    E11001("E11001","原鉴权申请是否已经失效或原申请已绑定成功",TradeStatusEnum.FAILED),
    E11003("E11003","原绑定提现账号的鉴权申请已经失败，请重新发起",TradeStatusEnum.FAILED),
    E11002("E11002","申请的短信指令号已经失效",TradeStatusEnum.FAILED),
    ERR258("ERR258","验证次数超过5次，请稍后重试",TradeStatusEnum.FAILED),
    ERR145("ERR145","限定时间内只能发起一次小额转账鉴权绑定申请",TradeStatusEnum.FAILED),
    ERR147("ERR147","无有效的待验证小额转账鉴权记录",TradeStatusEnum.FAILED),
    E50001("E50001","交易要素不全请核对请求要素",TradeStatusEnum.FAILED),
    ERR027("ERR027","所输监管账号未开户或已销户",TradeStatusEnum.FAILED),
    ERR126("ERR126","金额不符",TradeStatusEnum.FAILED),
    ERR269("ERR269","订单信息与上送信息不一致",TradeStatusEnum.FAILED),
    ERR270("ERR270","原交易信息不存在",TradeStatusEnum.FAILED),
    ERR158("ERR158","原交易日期（保留域）字段格式错误，正确应为yyyymmdd",TradeStatusEnum.FAILED),
    E00001("E00001","查无匹配的交易记录",TradeStatusEnum.FAILED),
    ERR293("ERR293","未检索到原交易，正在尝试查询历史数据，请稍后再试",TradeStatusEnum.FAILED),
    ERR272("ERR272","原转出方信息不符",TradeStatusEnum.FAILED),
    ;


    SettlementPlatformRespEnum(String status, String desc, TradeStatusEnum tradeStatusEnum){
        this.status = status;
        this.desc = desc;
        this.tradeStatusEnum = tradeStatusEnum;
    }

    private String status;
    private String desc;
    private TradeStatusEnum tradeStatusEnum;

    public static boolean isFail(String status) {
        if(ObjUtils.isBlank(status)){
            return false;
        }
        
        List<String> failedList = Arrays.stream(SettlementPlatformRespEnum.values()).filter(s -> s.tradeStatusEnum.equals(TradeStatusEnum.FAILED)).map(SettlementPlatformRespEnum::getStatus).collect(Collectors.toList());
        return failedList.contains(status);
    }
    
    public static boolean isF09001(String status){
        if(ObjUtils.isBlank(status)){
            return false;
        }
        
        return status.equals(F09001.status);
    }

    /**
     * 短信验证码失效
     * @param status
     * @return
     */
    public static boolean isE11XX(String status){
        if(ObjUtils.isBlank(status)){
            return false;
        }
        
        return status.equalsIgnoreCase(E11003.status)||status.equalsIgnoreCase(E11001.status)||status.equalsIgnoreCase(E11002.status)||status.equalsIgnoreCase(ERR147.status);
    }

    /**
     * 验证次数超过5次，请稍后重试
     * @param status
     * @return
     */
    public static boolean isERR258(String status){
        if(ObjUtils.isBlank(status)){
            return false;
        }
        
        return status.equalsIgnoreCase(ERR258.status)||status.equalsIgnoreCase(ERR145.status);
    }
}