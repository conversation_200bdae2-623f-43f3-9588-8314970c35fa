package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class EJZBCustInfomationQueryRespDTO extends SpaSettlementPlatBaseRespDTO{
    /*
     * 子台账账号	Y 32
     */
    @JsonProperty(value = "CustAcctId")
    private String custAcctId;

    /*
     * ThirdCustId	交易网会员代码	string(32)	Y	　	　
     */
    @JsonProperty(value = "ThirdCustId")
    private String thirdCustId;

    /*
     *CustName	会员名称	string(120)	Y		李四
     */
    @JsonProperty(value = "CustName")
    private String custName;

    /*
     * IdType	会员证件类型	string(4)	Y		1
     */
    @JsonProperty(value = "IdType")
    private String idType;

    /*
     * IdCode	会员证件号码	string(20)	Y	只展示后4位	**************1234
     */
    @JsonProperty(value = "IdCode")
    private String idCode;

    /*
     * ClientLvl	会员属性	string(4)	Y	客户属性：00-普通;SH-商户	SH
     */
    @JsonProperty(value = "ClientLvl")
    private String clientLvl;

    /*
     * NickName	用户昵称	string(120)	N
     */
    @JsonProperty(value = "NickName")
    private String nickName;

    /*
     * MobilePhone	手机号码	string(12)	N	掩码中间4位	138*****123
     */
    @JsonProperty(value = "MobilePhone")
    private String mobilePhone;

    /*
     * BusinessFlag	个体工商户标志	string(1)	N	1-是 0-否	1
     */
    @JsonProperty(value = "BusinessFlag")
    private String businessFlag;

    /*
     * ComepanyName	公司名称	string(120)	N
     */
    @JsonProperty(value = "ComepanyName")
    private String comepanyName;

    /*
     * CreditidCode	公司证件号码	string(32)	N	只展示后4位	**************3H5X
     */
    @JsonProperty(value = "CreditidCode")
    private String creditidCode;

    /*
     * StoreId	店铺编号	string(32)	N
     */
    @JsonProperty(value = "StoreId")
    private String storeId;

    /*
     * StoreName	店铺名称	string(120)	N
     */
    @JsonProperty(value = "StoreName")
    private String storeName;

    /*
     * LegalFlag	法人标志	string(1)	N	1-是 0-否	1
     */
    @JsonProperty(value = "LegalFlag")
    private String legalFlag;

    /*
     * LegalName	法人名称	string(120)	N
     */
    @JsonProperty(value = "LegalName")
    private String legalName;

    /*
     * CPFlag	实名认证标识	STRING(1)	N
     */
    @JsonProperty(value = "CPFlag")
    private String cPFlag;

    /*
     * OrangePay	是否有登记行为记录信息 S 是 F 否	STRING(32)	N	S 是 F 否	S
     */
    @JsonProperty(value = "OrangePay")
    private String orangePay;

    /*
     * LecertiType	法人证件类型	string(4)	N
     */
    @JsonProperty(value = "LecertiType")
    private String lecertiType;


    /*
     * LecertiCode	法人证件号码	string(32)	N	掩码中间部分	4123**********1234
     */
    @JsonProperty(value = "LecertiCode")
    private String lecertiCode;

    /*
     * Reserve	保留域	string(100)	N	　	　
     */
    @JsonProperty(value = "Reserve")
    private String reserve;

}
