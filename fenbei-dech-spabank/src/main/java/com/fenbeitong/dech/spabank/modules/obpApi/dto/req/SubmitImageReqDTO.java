package com.fenbeitong.dech.spabank.modules.obpApi.dto.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class SubmitImageReqDTO implements Serializable {
    // 	orderNo	开户订单号	string	必须
    private String	orderNo;
    // 	frontSide	人像面身份证图片	string	必须
    private String	frontSide;
    // 	backSide	国徽面身份证图片	string	必须
    private String	backSide;
    // 	idType	证件类型	string	必须
    private String	idType;
    // 	idNo	证件号	string	必须
    private String	idNo;
    // 	trueName	姓名	string	必须
    private String	trueName;
}
