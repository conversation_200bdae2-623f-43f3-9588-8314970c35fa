package com.fenbeitong.dech.spabank.modules.settlementplatform.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName MntMbrBindRelateAcctBankCodeReqDTO
 * @Description: KFEJZB6138	维护会员绑定提现账户联行号	MntMbrBindRelateAcctBankCode
 * <AUTHOR>
 * @Date 2022/08/01
 **/
@Data
public class MntMbrBindRelateAcctBankCodeReqDTO extends SpaSettlementPlatBaseReqDTO {

    /*
     * 子账户账号	Y 32
     * 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”(右侧)进行分隔。
     */
    @JsonProperty(value = "SubAcctNo")
    private String subAcctNo;

    /*
     * 会员绑定账号	Y 32
     */
    @JsonProperty(value = "MemberBindAcctNo")
    private String memberBindAcctNo;

    /*
     * 开户行名称	Y 32
     * 若大小额行号不填则送超级网银号对应的银行名称，若填大小额行号则送大小额行号对应的银行名称
     */
    @JsonProperty(value = "AcctOpenBranchName")
    private String acctOpenBranchName;

    /*
     * 大小额行号	Y 32
     * CnapsBranchId和EiconBankBranchId两者二选一必填。
     */
    @JsonProperty(value = "CnapsBranchId")
    private String cnapsBranchId;

    /*
     * 超级网银行号	Y 32
     */
    @JsonProperty(value = "EiconBankBranchId")
    private String eiconBankBranchId;

    /*
     * 保留域	N 120
     */
    @JsonProperty(value = "ReservedMsg")
    private String reservedMsg;
}
