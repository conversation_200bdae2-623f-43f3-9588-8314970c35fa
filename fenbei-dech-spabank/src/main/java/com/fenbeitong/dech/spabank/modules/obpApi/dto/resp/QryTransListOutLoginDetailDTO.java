package com.fenbeitong.dech.spabank.modules.obpApi.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-10-11 11:08:02
 * @Version 1.0
 **/
@Data
public class QryTransListOutLoginDetailDTO implements Serializable {
    // balance String 否 - 账户余额 - 0
    private String balance;
    // fromAcctNo String 否 - 转出账号(掩码) - 6226********696
    private String fromAcctNo;
    // fromBankName String 否 - 转出行名 - 光大银行
    private String fromBankName;
    // fromClientName String 否 - 转出户名 - 张洛
    private String fromClientName;
    // ipFlag String 否 - 交易类型 1表示借（当前账号为转出方），0表示贷（当前账号为转入方） 0
    private String ipFlag;
    // remark String 否 - 摘要 示例值：转入（代扣）、跨行转账（绑定卡转账到二类户卡）、网银转账（二类户卡转账到绑定卡）、冲销（二类户卡转账到绑定卡交易失败的数据）、产品认购（购买）、产品回款（赎回）、移动收款、来账退票（接收行和收款账户填写错误的） 转入
    private String remark;
    // toAcctNo String 否 - 转入账号（掩码） - 6222********8001
    private String toAcctNo;
    // toBankName String 否 - 转入行名 - 平安银行
    private String toBankName;
    // toClientName String 否 - 转入户名 - 张洛
    private String toClientName;
    // tranAmt String 否 - 付款金额 - 20
    private String tranAmt;
    // tranCode String 否 - 交易码 示例值：G1001。冲销：G1010 G1010
    private String tranCode;
    // tranDate String 否 - 交易日期 格式2017-01-19 2017/1/19
    private String tranDate;
    // tranFlag String 否 - 转账标志 01--网银转账, 默认00 0
    private String tranFlag;
    // tranSysNo String 否 - 流水号 - -
    private String tranSysNo;
    // tranTime String 否 - 交易时间 格式：2017-01-19 10:35:06 2017/1/19 10:35
    private String tranTime;
    // userRemark String 否 - 留言 "示例值： 1、购买基金产品[PAZ999]现金宝 2、现金宝基金T+0实时赎回" 现金宝基金T+0实时赎回
    private String userRemark;
    // valueDate String 否 - 起息日期 - 2020/8/30
    private String valueDate;
    // isWriteOff Boolean 否 - 是否冲销交易 是否冲销交易 FALSE
    private String isWriteOff;
}
