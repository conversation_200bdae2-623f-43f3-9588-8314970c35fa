package com.fenbeitong.dech.spabank.modules.b2bic.dto.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * @ClassName SpaFundTransferRespDTO
 * <AUTHOR>
 * @Date 2022/1/10
 **/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Result")
@Data
public class SpaFundTransferRespDTO extends SpaB2BICBaseRespDTO {

    /**
     * 	转账凭证号	C(20)	必输
     * 	同上送
     */
    private String ThirdVoucher;

    /**
     * 	银行流水号	C(32)	必输
     * 	银行业务流水号；可以用于对账
     */
    private String FrontLogNo;

    /**
     * 	客户自定义凭证号	C(20)	非必输
     * 	用于客户转账登记和内部识别，通过转账结果查询可以返回。银行不检查唯一性
     */
    private String CstInnerFlowNo;

    /**
     * 	货币类型	C(3)	必输
     */
    private String CcyCode;

    /**
     * 	付款人账户名称	C(60)	必输
     */
    private String OutAcctName;

    /**
     * 	付款人账户	C(20)	必输
     */
    private String OutAcctNo;

    /**
     * 	收款人开户行名称	C(60)	必输
     */
    private String InAcctBankName;

    /**
     * 	收款人账户	C(32)	必输
     */
    private String InAcctNo;

    /**
     * 	收款人账户户名	C(60)	必输
     */
    private String InAcctName;

    /**
     * 	交易金额	C(13)	必输
     */
    private String TranAmount;

    /**
     * 	行内跨行标志	C(1)	必输
     * 	1：行内转账，0：跨行转账
     */
    private String UnionFlag;

    /**
     * 	手续费	C(13)	必输
     * 	转账手续费预算，实际手续费用以实际扣取的为准。
     */
    private String Fee1;

    /**
     * 	邮电费	C(13)	非必输
     */
    private String Fee2;

    /**
     * 	银行返回流水号	C(32)	非必输
     * 	银行记账流水号；
     *  转账成功后，银行返回的流水号。
     */
    private String hostFlowNo;

    /**
     * 	记账日期	C(8)	非必输
     * 	银行交易成功后的记账日期，仅对行内实时转账交易有效。
     */
    private String hostTxDate;

    /**
     * 	交易状态标志	C(2)	非必输
     * 	20：交易成功
     *  30：失败；
     *  其他为银行受理成功处理中，请使用“交易进度查询4005”接口获取最终状态
     */
    private String stt;
}
