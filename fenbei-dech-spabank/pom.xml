<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fenbei-dech</artifactId>
        <groupId>com.fenbeitong</groupId>
        <version>5.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <version>${dech.version}</version>
    <artifactId>fenbei-dech-spabank</artifactId>

    <!--    跳过deploy到远程仓库-->
    <properties>
        <skip-maven-deploy>true</skip-maven-deploy>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-dech-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-dech-dto</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPACryptoPkg</artifactId>
            <version>${spa.3rd.SPACryptoPkg.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPASignPkg</artifactId>
            <version>${spa.3rd.SPASignPkg.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPADcRSAPkg</artifactId>
            <version>${spa.3rd.SPADcRSAPkg.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${zb.3rd.bcprov.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPAPKIBASE</artifactId>
            <version>${spa.3rd.SPAPKIBASE.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPACFCACertKitJS</artifactId>
            <version>${spa.3rd.SPACFCACertKitJS.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPAnetsign18</artifactId>
            <version>${spa.3rd.SPAnetsign18.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPAnetsignderutil</artifactId>
            <version>${spa.3rd.SPAnetsignderutil.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPAApacheConfiguration</artifactId>
            <version>${spa.3rd.SPAApacheConfiguration.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.3rd</groupId>
            <artifactId>SPAApacheHttpclient</artifactId>
            <version>${spa.3rd.SPAApacheHttpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>com.pingan.openbank</groupId>
            <artifactId>api-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pingan.openbank</groupId>
            <artifactId>obp-client</artifactId>
        </dependency>
    </dependencies>
</project>
