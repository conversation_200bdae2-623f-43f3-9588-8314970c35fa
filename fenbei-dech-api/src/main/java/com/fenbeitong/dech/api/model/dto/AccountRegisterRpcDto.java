package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName AccountRegisterRpcDto
 * @Description: 虚拟卡账户注册RPC请求DTO
 * <AUTHOR>
 * @Date 2021/3/8
 **/
@Data
public class AccountRegisterRpcDto implements Serializable {

    /*
    订单号
     */
    private String orderNo;

    /*
    身份证信息
     */
    private IdCardInfo idCardInfo;

    /*
    职业：
    0 国家机关、党群组织、企业、事业单位负责人
    1 与业技术人员 3 办事人员和有关人员
    4 商业、服务业人员 5 农、林、牧、渔、水利业生产人员
    6 生产、运输设备操作人员及有关人员
    X 军人 Y 不便分类的其他从业人员 Z 未知
     */
    private String occupation;

    /*
    地址
     */
    private String address;

    /*
    人脸上传Url
     */
    private String faceImageUrl;



    @Data
    public class IdCardInfo implements Serializable{

        /*
        身份证正面url
         */
        private String idCardFrontUrl;

        /*
        身份证反面url
         */
        private String idCardReverseUrl;

        /*
        身份证号
         */
        private String idCardNumber;

        /*
        身份证名称
         */
        private String idCardName;

        /*
        身份证有效期（若证件有效期为长期，请填写：长期。2、填写起止日期幵用逗号隔开，格式：1990-01-01，长期）
         */
        private String idCardEffectiveTime;
    }

    public IdCardInfo createIdCardInfo(){
        IdCardInfo idCardInfo = new IdCardInfo();
        return idCardInfo;
    }
}
