package com.fenbeitong.dech.api.model.dto.airwallex.withdraw;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * <AUTHOR>
 * @date 2023年8月8日
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class AddressDTO implements Serializable {

    /**
     * 收款人所在城市
     */
    @NotBlank
    private String  city;

    /**
     * 收款人的国家代码（2个字母的ISO 3166-2国家代码）@see CountryCodeEnum
     */
    @NotBlank
    private String countryCode;

    /**
     * 收款人所在州
     * 1-50个字符长
     */
    @NotBlank
    private String state;

    /**
     * 收款人的街道地址
     * 1-200个字符长
     * 不应为邮政信箱地址，请输入有效地址
     */
    @NotBlank
    private String streetAddress;

    /**
     * 邮政编码
     */
    @Default
    private String postcode = "10086";
}
