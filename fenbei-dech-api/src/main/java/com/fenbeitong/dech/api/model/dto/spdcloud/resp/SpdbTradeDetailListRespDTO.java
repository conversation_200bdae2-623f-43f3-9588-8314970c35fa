package com.fenbeitong.dech.api.model.dto.spdcloud.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class SpdbTradeDetailListRespDTO implements Serializable {
    private String transTime ;

    /**
     *对方行名
     */
    private String oppositeBankName ;

    /**
     *对方帐号
     */
    private String subAccount ;

    /**
     *柜员流水号
     */
    private String tellerJnlNo ;

    /**
     *对方行号
     */
    private String oppositeBankNo ;

    /**
     *对方户名
     */
    private String subAcctName ;

    /**
     *备注
     */
    private String remark ;

    /**
     *交易码
     */
    private String businessCode ;

    /**
     *系统日期
     */
    private String systemDate ;

    /**
     *营业机构号
     */
    private String orgNo ;

    /**
     *借贷标记   0-借/收
     1-贷/付
     */
    private String debitFlag ;

    /**
     *交易金额
     */
    private String transAmount ;

    /**
     *帐户余额
     */
    private String acctBalance ;

    /**
     *摘要代码
     */
    private String summaryCode ;

    /**
     *备用字段40位
     */
    private String remark1 ;

    /**
     *凭证号
     */
    private String voucherNo ;

    /**
     *客户帐号
     */
    private String custAcctNo ;

    /**
     *客户帐号类型
     */
    private String masterAcctNoType ;

    /**
     *交易柜员
     */
    private String transGy ;

    /**
     *授权柜员
     */
    private String authGy ;

    /**
     *备用域10位
     */
    private String reserveDomain ;

    /**
     *传票组内序号
     */
    private String summonsNumber ;

    /**
     *冲补标志
     */
    private String fillUpMark ;

    /**
     *交易日期（自然日期）
     */
    private String transDate ;

    /**
     *唯一标识
     */
    private String uniqueIdentification ;

    /**
     *原支付交易发起渠道
     */
    private String oldChannel ;

    /**
     *原支付交易码
     */
    private String oldTransCode ;

    /**
     *原支付交易账务日期
     */
    private String oldTransDate ;

    /**
     *原支付交易发起时间
     */
    private String oldStartDate ;

    /**
     *原支付交易受理编号
     */
    private String oldEntrustSeqNo ;

    /**
     *原支付交易电子凭证号
     */
    private String oldElecChequeNo ;

    /**
     *是否标识
     */
    private String isFlag ;

    /**
     *客户附言
     */
    private String customerNote ;

    /**
     *退汇原因
     */
    private String backReason ;

    private String note1 ;

    private String note2 ;

    private String note3 ;

}
