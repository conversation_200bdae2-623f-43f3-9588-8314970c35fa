package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName SpaBankToPubAcctReqDto
 * @Description: 付款3：平安易内部户到企业对公付
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
public class SpaBankToPubAcctReqDto extends SpaBankTradeReqBaseDto {

    /**
     * 付款3必填
     * 付款3：付款方账户名（平安易内部账户名）
     */
    @NotNull
    private String payAccountName;

    /**
     * 付款3必填
     * 付款3：对公付款企业会员子账户号
     */
    @NotNull
    private String paySubAcctNo;

    /**
     * 付款3必填
     * 付款3：对公付款企业会员子账户名称
     */
    @NotNull
    private String paySubAcctName;

    /**
     * 付款3：用途备注
     */
    private String remark;

    /**
     * 付款3必填
     * 付款3：收款账户名（企业对公户）
     */
    @NotNull
    private String receiveAccountName;

    /**
     * 付款3必填
     * 付款3：开户银行联行号
     */
    @NotNull
    private String bankBrnNo;

    /**
     * 付款3必填
     * 付款3：开户行名称
     */
    @NotNull
    private String bankBrnName;

    /**
     * 收款方是否本行 true 是
     */
    @NotNull
    private Boolean trgBankIsOwnAcct;
}
