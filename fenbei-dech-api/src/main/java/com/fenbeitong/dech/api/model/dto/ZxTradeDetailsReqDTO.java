package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/4/8
 */
@Data
public class ZxTradeDetailsReqDTO implements Serializable {
    /**
     * 主体账号
     */
    private String accountNo;

    /**
    资金分簿编号
     */
    private String subAccNo;

    /**
    查询类型 1：查询待调账交易明细 空：查询全部交易明细
     */
    private String queryType = "";

    /**
    起始日期
     */
    private String startDate;

    /**
    终止日期
     */
    private String endDate;

    /**
    交易类型
    11	普通转账
    12	资金初始化
    13	利息分配
    14	手续费分配
    15	强制转账
    16	调账
    21	公共利息收费账户转账
    22	公共调账账户外部转账
    23	普通外部转账
     */
    private String tranType = "";

    /**
    起始记录号
     */
    private String startRecord = "";

    /**
     请求记录条数
     */
    private String pageNumber = "";

    /**
    上一笔柜员交易号
     */
    private String lastHostFlw = "";

    /**
    上一笔交易序号
     */
    private String lastHostSeq = "";

    /**
    上一笔交易日期
     */
    private String lastHostDate = "";

    /**
    查询模式
     */
    private String qryMode = "";
}
