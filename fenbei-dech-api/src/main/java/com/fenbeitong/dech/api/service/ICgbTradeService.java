package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.BankRefundFundReqDTO;
import com.fenbeitong.dech.api.model.dto.BankRefundFundRespDTO;
import com.fenbeitong.dech.api.model.dto.BankTradeBaseReqDto;
import com.fenbeitong.dech.api.model.dto.BankTradeRespDto;

public interface ICgbTradeService {

    /**
     * 专户转给其他账薄
     *
     * @param reqDto
     * @return
     */
    BankTradeRespDto specialBankTransfer(BankTradeBaseReqDto reqDto);

    /**
     * 不明资金退款（原路退回）
     *
     * @param reqDTO
     * @return
     */
    BankRefundFundRespDTO refundFund(BankRefundFundReqDTO reqDTO);
}
