package com.fenbeitong.dech.api.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName SpaQuerySingleOrderReqDTO
 * @Description: 查询充值订单
 * <AUTHOR>
 * @Date 2021/10/25
 **/
@Data
public class SpaQuerySingleOrderReqDTO implements Serializable {

    /*
     * 商户订单号	Y 100
     * 商户系统生成的订单号
     */
    @NotBlank
    private String traderOrderNo;

    /*
     * 订单发送时间	Y 20
     * 商户系统订单生成时间 格式：yyyyMMddHHmmss
     */
    @NotBlank
    private Date orderSendTime;
}
