package com.fenbeitong.dech.api.model.dto.airwallex.authpay;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class AuthPayRequestDTO implements Serializable {

	/**
	 * 请求流水号 长度 <= 50字符
	 */
	@NotNull
	private String requestId;
	
	/**
	 * 订单id
	 */
	@NotNull
	private String orderId;
	
	/**
	 * 用于定位账户
	 */
	@NotNull
	private String companyId;
	
	/**
	 * 支付金额，单位为分
	 */
	@NotNull
	private BigDecimal amount;
	
	/**
	 * 选填，如果为空那么默认为人民币
	 */
	private String currency;
	
	/**
	 * 附言、备注
	 */
	private String reference;
}
