package com.fenbeitong.dech.api.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ZsOrderQueryReqDto implements Serializable {
    private static final long serialVersionUID = 520897288987804442L;

//    @ApiModelProperty(value = "机构代码")
//    private String issCode;

    @ApiModelProperty(value = "二维码")
    private String qrCode;

//    @ApiModelProperty(value = "版本")
//    private String version;

//    @ApiModelProperty(value = "交易类型")
//    private String reqType;
}
