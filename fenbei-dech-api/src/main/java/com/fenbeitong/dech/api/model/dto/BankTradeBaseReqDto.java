package com.fenbeitong.dech.api.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/2/8
 * @Description
 */
@Data
public class BankTradeBaseReqDto implements Serializable {

    /**
     * 账户流水id
     */
    @NotBlank
    private String accountFlowId;

    /**
     * 银行名称
     */
    @NotBlank
    private String bankName;

    /**
     * 操作金额 分
     */
    @NotNull
    private BigDecimal operationAmount;


    private String operationUserId;

    /**
     * 订单流水号
     */
    @NotBlank
    private String txnId;

    /**
     * 消费方账户
     */
    @NotBlank
    private String payAccountNo;

    /**
     * 消费方账户名
     */
    @NotBlank
    private String payAccountName;

    /**
     * 收款方账户
     */
    @NotBlank
    private String receiveAccountNo;

    /**
     * 收款方账户名
     */
    @NotBlank
    private String receiveAccountName;

    /**
     * 公司id
     */
    @NotBlank
    private String companyId;

    /**
     * 是否是同步交易 true 是同步
     */
    @NotNull
    private Boolean syncTrade;

    /**
     * 收款方是否本行 true 是
     */
    private Boolean trgBankIsOwnAcct;

    /**
     * 账户类型
     */
    private Integer accountSubType;

    /**
     * 账户模式
     */
    private Integer accountModel;

    /**
     * 标识  1. 企业  2. 个人
     */
    @NotNull
    private Integer customerType;

    /**
     * 上账流水号
     */
    private String accountSerialNumber;


}
