package com.fenbeitong.dech.api.service;


import com.fenbeitong.dech.api.model.dto.*;

/**
 * @ClassName ISpaBankTradeService
 * @Description: 平安银行交易
 * <AUTHOR>
 * @Date 2022/1/4
 **/
public interface ISpaBankTradeService {

    /*
     * @MethodName: toTransitionAccount
     * @Description: 付款1：企业会员子账户到平台过渡户
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankTradeRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/4
    **/
    SpaBankTradeRespBaseDto toTransitionAccount(SpaBankToTransAcctReqDto reqDto);

    /*
     * @MethodName: toInternalAccount
     * @Description: 付款2：平台过渡户到平安易内部户
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankTradeRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/4
    **/
    SpaBankTradeRespBaseDto toInternalAccount(SpaBankToInterAcctReqDto reqDto);

    /*
     * @MethodName: toPublicAccount
     * @Description: 付款3：平安易内部户到企业对公付
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankTradeRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/4
    **/
    SpaBankTradeRespBaseDto toPublicAccount(SpaBankToPubAcctReqDto reqDto);

    /*
     * @MethodName: refundToTransitionAccount
     * @Description: 退款1：平安易内部户到平台过渡户
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankTradeRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/4
     **/
    SpaBankTradeRespBaseDto refundToTransitionAccount(SpaBankRefundToTransAcctReqDto reqDto);

    /*
     * @MethodName: refundToMemberAccount
     * @Description: 退款3：平台过渡户到企业会员子账户
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankTradeRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/4
     **/
    SpaBankTradeRespBaseDto refundToMemberAccount(SpaBankRefundToMemberAcctReqDto reqDto);


    /*
     * @MethodName: toCompanyAccount
     * @Description: 平安报销打款：个人见证账户到企业见证账户
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankTradeRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/4
     **/
    SpaBankTradeRespBaseDto toCompanyAccount(SpaBankToTransAcctReqDto reqDto);

    /*
     * @MethodName: toCompanyAccount
     * @Description: 平安虚拟卡额度退回：个人见证账户到企业见证账户
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankTradeRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/4
     **/
    SpaBankTradeRespBaseDto personAccountToCompanyAccount(SpaBankToTransAcctReqDto reqDto);

}
