package com.fenbeitong.dech.api.model.dto.lianlian.account.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LianLianAccountApplyRpcReqDTO implements Serializable {
    private String txnSeqno;

    private String notifyUrl;

    private String channelOnlineType;

    private LianLianSubjectInfo subjectInfo;

    private LianLianBusinessInfo businessInfo;

    private List<LianLianBusinessQualification> businessQualifications;

    private LianLianSalesInfos salesInfos;

    private LianLianRelatedPersonnel relatedPersonnel;

    private List<LianLianProductInfo> productInfos;

    private LianLianSettleInfo settleInfo;

    private LianLianProtocolInfo protocolInfo;


}
