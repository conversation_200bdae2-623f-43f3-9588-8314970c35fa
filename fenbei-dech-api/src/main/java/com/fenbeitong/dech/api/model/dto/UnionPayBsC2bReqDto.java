package com.fenbeitong.dech.api.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class UnionPayBsC2bReqDto implements Serializable {
    /**
     * 银行卡号
     */
    @JsonProperty(value = "accNo")
    private String accNo;

    /**
     * 名称
     */
    @JsonProperty(value = "name")
    private String name;

    /**
     *
     */
    @JsonProperty(value = "cardAttr")
    private String cardAttr;


    /**
     * 免密金额
     */
    @JsonProperty(value = "pinFree")
    private String pinFree;

    /**
     * 最大交易金额
     */
    @JsonProperty(value = "maxAmont")
    private String maxAmont;

    /**
     * 附加处理服务器地址
     */
    @JsonProperty(value = "addnOpUrl")
    private String addnOpUrl;

    /**
     * 附加处理条件
     */
    @JsonProperty(value = "addnCond")
    private String addnCond;

    /**
     *优惠信息
     */
    @JsonProperty(value = "couponInfo")
    private String couponInfo;


    /**
     *交易通知URL
     */
    @JsonProperty(value = "backUrl")
    private String backUrl;

    /**
     *EMV 二维码标识
     */
    @JsonProperty(value = "emvCodeIn")
    private String emvCodeIn;

    /**
     *35借记卡   51 贷记卡
     */
    @JsonProperty(value = "qrType")
    private String qrType;

    /**
     *风控信息
     */
    @JsonProperty(value = "riskInfo")
    private String riskInfo;

    /**
     *付款方信息.字段域
     */
    @JsonProperty(value = "payerInfo")
    private String payerInfo;

    /**
     *
     */
    @JsonProperty(value = "reqReserved")
    private String reqReserved;

    /**
     *二维码有效时间
     */
    @JsonProperty(value = "qrValidTime")
    private String qrValidTime;

    /**
     *加密证书 ID
     */
    @JsonProperty(value = "encryptCertId")
    private String encryptCertId;

    /**
     * 版本号
     */
    @JsonProperty(value = "version")
    private String version;

    /**
     * 机构号
     */
    @JsonProperty(value = "issCode")
    private String issCode;

    /**
     * 交易类型
     */
    @JsonProperty(value = "reqType")
    private String reqType;

    /**
     *付款方申码订单号
     */
    @JsonProperty(value = "qrOrderNo")
    private String qrOrderNo;

    /**
     * 付款时建
     */
    @JsonProperty(value = "qrOrderTime")
    private String qrOrderTime;

    /**
     * 手机号
     */
    @JsonProperty(value = "mobile")
    private String mobile;

    /**
     * 金额币种 156 人民币
     */
    @JsonProperty(value = "currency")
    private String currency;

    /**
     * 应答码
     */
    @JsonProperty(value = "respCode")
    private String respCode;

    /**
     * 响应信息
     */
    @JsonProperty(value = "respMsg")
    private String respMsg;

}
