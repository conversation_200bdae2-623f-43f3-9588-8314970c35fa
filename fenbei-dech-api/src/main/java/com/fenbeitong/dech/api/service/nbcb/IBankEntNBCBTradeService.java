package com.fenbeitong.dech.api.service.nbcb;

import com.fenbeitong.dech.api.model.dto.nbcb.*;

public interface IBankEntNBCBTradeService {

    NBCBBatchTransferRespDTO batchTransfer(NBCBBatchTransferReqDTO nbcbBatchTransferReq);

    NBCBQueryBatchTransferResultRespDTO queryBatchTransferResult(NBCBQueryBatchTransferResultReqDTO nbcbQueryBatchTransferResult);

    NBCBQueryRefundRespDTO queryRefund(NBCBQueryRefundReqDTO nbcbQueryRefundReqDTO);
}
