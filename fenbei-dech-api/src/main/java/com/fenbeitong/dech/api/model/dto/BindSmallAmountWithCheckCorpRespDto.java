package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-09-16 10:59:55
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BindSmallAmountWithCheckCorpRespDto implements Serializable {
    /**
     * 是否成功： true：成功  false:失败
     */
    private Boolean isSuccess;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 请求流水号
     */
    private String seqNo;
}
