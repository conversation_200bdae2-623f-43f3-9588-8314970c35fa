package com.fenbeitong.dech.api.model.dto.lianlian.account.req;

import lombok.Data;

import java.io.Serializable;
@Data
public class LianLianAccountActiveApplyRpcReqDTO implements Serializable {
    private String mchId;
    /**
     * 银行账户号 加款充值的银行账户号
     */
    private String bankAccountNo;
    /**
     out_order_no
     string
     商户请求唯一编号
     必需
     >= 1 字符
     <= 32 字符
     */
    private String outOrderNo;
}
