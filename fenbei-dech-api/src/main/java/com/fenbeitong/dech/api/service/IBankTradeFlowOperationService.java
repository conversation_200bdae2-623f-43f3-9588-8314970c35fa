package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.BankFlowRespDto;

/**
 * <AUTHOR>
 * @Date 2021/6/25
 * @Description
 */
public interface IBankTradeFlowOperationService {


    /**
     * 更新交易流水
     *
     * @param bassTxnId
     * @return
     */
    BankFlowRespDto updateTradeFlowStatus(String bassTxnId, String txnStatus);

    /**
     * 更新出金流水
     *
     * @param bassTxnId
     * @return
     */
    BankFlowRespDto updateCashOutFlowStatus(String bassTxnId, String txnStatus);

}
