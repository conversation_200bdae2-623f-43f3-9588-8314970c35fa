package com.fenbeitong.dech.api.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ZbQueryCashInListRespDTO
 * @Description: 母户入金、转账入金列表查询
 * <AUTHOR>
 * @Date 2022/2/24
 **/
@Data
public class ZbQueryCashInListRespDTO implements Serializable {

    // 系统订单号
    @JsonProperty(value = "SysOrdNo")
    private String sysOrdNo;

    //  电子账簿 ID
    @JsonProperty(value = "FctId")
    private String fctId;

    // 关联账户账号 string(32) Y 母户入金时为母户账号，转账入金时为 电子账簿账簿号。
    @JsonProperty(value = "RltdAcctNo")
    private String rltdAcctNo;

    //  金额
    @JsonProperty(value = "Amt")
    private String amt;

    // 转出方账号
    @JsonProperty(value = "SwtchOutAcctNo")
    private String swtchOutAcctNo;

    // 转出方银行账户名称
    @JsonProperty(value = "PayBnkActNm")
    private String payBnkActNm;

    // 转出方银行编号
    @JsonProperty(value = "PayBnkCd")
    private String payBnkCd;

    // 转出方银行联行号
    @JsonProperty(value = "PayBnkBrnNo")
    private String payBnkBrnNo;

    // 行名
    @JsonProperty(value = "BnkNm")
    private String bnkNm;

    // 附言信息
    @JsonProperty(value = "CmntsInf")
    private String cmntsInf;

    // 时间
    @JsonProperty(value = "Time")
    private String time;

    // 交易状态
    @JsonProperty(value = "TxnSt")
    private String txnSt;

    // 交易类型
    @JsonProperty(value = "TxnTp")
    private String txnTp;

    // 银行流水号
    @JsonProperty(value = "BnkTrnSeqNo")
    private String bnkTrnSeqNo;

    // 原订单号
    @JsonProperty(value = "OrgOrdNo")
    private String orgOrdNo;

    // 原始平台订单号
    @JsonProperty(value = "OrgPltOrdNo")
    private String orgPltOrdNo;
}
