package com.fenbeitong.dech.api.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ZbQueryCashInListRespDTO
 * @Description: 母户入金、转账入金列表查询
 * <AUTHOR>
 * @Date 2022/2/24
 **/
@Data
public class ZbQueryCashInListReqDTO implements Serializable {

    // 交易类型 10: 母户入金;11: 转账入金； 12：原路退汇
    @JsonProperty(value = "TxnTp")
    private String txnTp;

    // 电子账簿 ID 交易类型为转账入金时，可选
    @JsonProperty(value = "FctId")
    private String fctId;

    // 开始时间
    @JsonProperty(value = "StaTm")
    private String staTm;

    // 结束时间
    @JsonProperty(value = "EndTm")
    private String endTm;

    // 请求页码 string(10) N 默认值 1。
    @JsonProperty(value = "RqsPgNo")
    private String rqsPgNo;

    // 每页条数
    @JsonProperty(value = "PgSz")
    private String pgSz;
}
