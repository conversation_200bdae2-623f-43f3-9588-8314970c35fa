package com.fenbeitong.dech.api.model.dto.airwallex;

import lombok.Data;

import java.io.Serializable;
@Data
public class FxMarketFxRpcReqDTO implements Serializable {

    private String channel;


    /**
     * 客户购买的货币（3位ISO-4217代码）
     */
    private String buyCurrency;

    /**
     * 客户销售的货币（3位ISO-4217代码）。这是您需要在结算截止时间前发送给我们的货币
     */
    private String sellCurrency;

    /**
     * 客户以buy_currency购买的金额（如果指定了sell_Amount，则必须为空）
     */
    private String buyAmount;

    /**
     * 结算转换的日期。conversation_date默认为当前日期，如果资金可用，除非指定。
     */
    private String conversionDate;

    /**
     * 客户以sell_currency出售的金额（如果指定了buy_Amount，则必须为空）
     */
    private	Long sellAmount;
}
