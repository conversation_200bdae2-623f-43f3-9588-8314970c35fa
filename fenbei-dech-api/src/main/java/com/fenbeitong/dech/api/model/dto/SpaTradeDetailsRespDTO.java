package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/4/8
 */
@Data
public class SpaTradeDetailsRespDTO implements Serializable {
    /*
     *      入账类型    string(1)
     *      02：会员充值/退票入账
     *      03：资金挂账"
     */
    private String InAcctType;

    /*
     * 交易网会员代码    string(32)
     */
    private String TranNetMemberCode;

    /*
     * 见证子帐户的帐号    string(32)
     */
    private String SubAcctNo;

    /*
     * 入金金额    string(15)
     */
    private String TranAmt;

    /*
     * 入金账号    string(32)
     */
    private String InAcctNo;

    /*
     * 入金账户名称    string(120)
     */
    private String InAcctName;

    /*
     * 币种    string(3)
     */
    private String Ccy;

    /*
     * 会计日期    string(8);;即银行主机记账日期
     */
    private String AccountingDate;

    /*
     * 银行名称    string(120);;付款账户银行名称
     */
    private String BankName;

    /*
     * 转账备注    string(120)
     */
    private String Remark;

    /*
     * 见证系统流水号    string(16)
     */
    private String FrontSeqNo;
}
