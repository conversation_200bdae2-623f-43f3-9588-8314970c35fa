package com.fenbeitong.dech.api.model.dto;

import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;

import java.io.Serializable;

/*
 发送短信验证码
**/
@Data
public class SendMsgRespDto implements Serializable {

    /**
     * 发送状态 true 成功
     */
    private Boolean sendStatus;

    /**
     * 发送验证码id
     */
    private String smsId;

    public static SendMsgRespDto onlySendStatusBuild(Boolean sendStatus){
        SendMsgRespDto respDto = new SendMsgRespDto();
        if(ObjUtils.isNull(sendStatus)){
            return null;
        }
        respDto.setSendStatus(sendStatus);
        return respDto;
    }

}
