package com.fenbeitong.dech.api.model.dto.airwallex.authpay;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class QueryAccountBalanceRespDTO implements Serializable {

	/**
	 * 可用余额，单位为分
	 */
	private BigDecimal availableAmount;
	
	/**
	 * 币种
	 */
	private String currency;
	
	private String companyId;
}
