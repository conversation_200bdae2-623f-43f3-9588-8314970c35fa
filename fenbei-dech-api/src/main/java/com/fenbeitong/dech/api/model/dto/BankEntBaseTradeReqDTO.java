package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BankEntBaseTradeReqDTO implements Serializable {

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 分贝通流水号
     */
    @NotNull
    private String tradeFlowId;

    /**
     * 账户ID（同 account_info accountId）
     */
    @NotNull
    private String accountInfoId;

    /**
     * 平台编码 用友-UFIDA,招商-CMB
     */
    @NotNull
    private String platformCode;

    /**
     * 银行编码
     */
    @NotNull
    private String bankCode;

    /**
     * 付款方账号
     */
    @NotNull
    private String payAccountNo;

    /**
     * 付款方账号名称
     */
    @NotNull
    private String payAccountName;

    /**
     * 收款方账号
     */
    @NotNull
    private String receiveAccountNo;

    /**
     * 收款方账号名称
     */
    @NotNull
    private String receiveAccountName;

    /**
     * 操作金额 分
     */
    @NotNull
    private BigDecimal operationAmount;

    /**
     * 是否是同步交易 true 是同步
     */
    @NotNull
    private Boolean syncTrade;

    /**
     * 企业ID
     */
    @NotNull
    private String companyId;

}
