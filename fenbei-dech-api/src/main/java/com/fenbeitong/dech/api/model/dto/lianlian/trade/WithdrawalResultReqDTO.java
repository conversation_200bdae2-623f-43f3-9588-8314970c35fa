package com.fenbeitong.dech.api.model.dto.lianlian.trade;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class WithdrawalResultReqDTO implements Serializable {

	/**
     * 公司ID
     */
    private String companyId;
    
    /**
     * 商户id
     */
    private String mchId;
    
	/**
	 * 商户提现单号
	 */
	private String outOrderNo;
	
	/**
	 * 系统提现单号
	 */
	private String orderNo;
	
	/**
	 * 订单总金额 单位为RMB-元。 大于0的数字，精确到小数点后两位
	 */
	private BigDecimal orderAmount;
	
	/**
	 * 提现结果:SUCCESS、FAIL
	 */
	private String orderStatus;
	
	/**
	 * 提现失败原因 失败或退汇时描述原因
	 */
	private String failReason;
	
	/**
	 * 系统账务日期，交易成功时返回
	 */
	private String accountDate;
}
