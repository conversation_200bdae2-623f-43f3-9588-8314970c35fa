package com.fenbeitong.dech.api.service.spabank;

import com.fenbeitong.dech.api.model.dto.spabank.req.SpaCzReceiptDownLoadRpcReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.req.SpaQueryBatchTransferReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.req.SpaReceiptReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaCzReceiptDownLoadRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaQueryBatchTransferRespDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaReceiptRespDto;

/**
 * 平安银行查询查询接口
 */
public interface IBankEntSpaBankSearchService {
    /**
     * 回单下载申请
     */
    SpaReceiptRespDto getReceiptRequest(SpaReceiptReqDto spaReceiptReqDto);

    /**
     * 账户交易明细查询
     * @param spaQueryBatchTransferReqDto
     * @return
     */
    SpaQueryBatchTransferRespDto queryTradeDetails(SpaQueryBatchTransferReqDto spaQueryBatchTransferReqDto);

    /**
     * 回单下载
     * @param spaCzReceiptDownLoadReqDto
     * @return
     */
    SpaCzReceiptDownLoadRpcRespDTO getReceiptUrl(SpaCzReceiptDownLoadRpcReqDto spaCzReceiptDownLoadReqDto);

}
