package com.fenbeitong.dech.api.model.dto.spdcloud.req;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Data
public class SpdbQueryBatchPayRpcReqDTO implements Serializable {

    /**
     * 统一社会信用代码 Y
     */
    private String unfSocCrdtNo;

    /**
     * 客户名称 N
     */
    private String masterName;

    /**
     * 账号 Y
     * 需要查询的企业账户。
     必须是已经签约的账户
     */
    private String acctNo;

    /**
     * 查询的笔数 Y
     * 最大为100
     */
    private String queryNumber;

    /**
     * 查询的起始笔数 Y
     */
    private String beginNumber;

    /**
     * 包号 Y
     * 对应EYW2交易返回的包号,输入则精确查询该包批量处理情况
     */
    private String packageNo;

    /**
     * 交易申请渠道 N
     * 为空即查所有，腾讯ERP模式下可送DGWY
     银企直联-YQZL
     公司网银-DGWY
     公司手机-GSSJ
     小微网银-WXXY
     小微手机-XWSJ
     企业财资-QYCZ
     存管系统- CGXT
     */
    private String channel;

    /**
     * 备用字段5 N
     */
    private String queryDateType;

    /**
     * 收款人账号 N
     * 为空即查所有
     */
    private String payeeAcctNo;

    /**
     * 收款行行号 N
     * 为空即查所有
     */
    private String payeeBankNo;

    /**
     * 本行他行标志 N
     * 0：本行
     1：他行
     */
    private String sysFlag;

    /**
     * 网银制单状态 N
     * 00：待处理
     01：处理中：在网银页面上点击确认后到提交之前或授权前
     02：已处理：网银发核心交易成功
     03：已拒绝：客户取消交易
     04：校验失败：交易内容的格式或逻辑校验未通过或发后台失败
     05：已失效：客户提交交易后未到网银页面处理，过了该笔交易的处理时效
     */
    private String interHandleStatus;

    /**
     * 受理状态 N
     * A：业务登记
     0：待补录
     1：待记账
     2：待处理
     3：待授权
     4：受理完成
     8：受理失败
     9：撤销
     为空即查所有
     */
    private String transStatus;

    private String SAASId;

    private String SAASName;



}
