package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022年10月11日 19:10
 * @description
 */
@Data
public class SpaBankCardTradeReqDto implements Serializable {

    //	开户业务流水号
    private String  spaOpenBusinessNo;

    //	协议号 二类户协议号
    private String  agreementNo;

    // 业务流水号
    private String	businessNo;

    // 	客户姓名
    private String	realName;

    // 	证件号码
    private String	certNo;

    // 	证件类型
    private String	certType;

    // 	phoneNo	手机号	string	必须
    private String	phoneNo;

    // 	bankCardNo	二类户卡号	string	必须
    private String	bankCardNo;

    // 	bankCardName	二类户卡户名	string	必须
    private String	bankCardName;

    // 	targetBankCardNo	绑定一类户卡号	string	必须
    private String	targetBankCardNo;

    // 	targetBankCardName	绑定一类户卡户名	string	必须
    private String	targetBankCardName;

    // 合规需要 10 终端类型 Y 必输，终端类型01- PC 02- APP安卓 03- APP苹果 04- H5
    private String channelType;

    // 合规需要 ip地址
    private String ipAddr;

    // otpOrderNo	otp流水号	string	必须
    private String otpOrderNo;

    // otpValue	验证码
    private String otpValue;

    //	50	用户备注	N
    private String userRemark;
}
