package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class BankEntPrivateTradeReqDTO implements Serializable {

    private String custNo;

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 平台编码 用友-UFIDA,招商-CMB
     */
    @NotNull
    private String platformCode;

    /**
     * 付款总笔数
     */
    private Integer totalMum;

    /**
     * 付款总金额
     */
    @NotNull
    private BigDecimal totalAmount;

    /**
     * 是否需要风控
     */
    private Boolean needRisk;

    /**
     * 批次号
     */
    private String batNo;

    /**
     * 支付明细
     */
    @NotNull
    private List<BankEntBatPayDetailDTO> tradeDetail;

}
