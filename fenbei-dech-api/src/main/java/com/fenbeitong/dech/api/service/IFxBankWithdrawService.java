package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.ResponseDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.withdraw.FxWithdrawRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.withdraw.FxWithdrawResponseDTO;

public interface IFxBankWithdrawService {

    /**
     * 校验提现请求参数是否符合要求
     * @param withdrawReq
     * @return
     */
    ResponseDTO<Boolean> validateWithdraw(FxWithdrawRequestDTO withdrawRequest);
    /**
     * 外币提现请求
     * @param withdrawReq
     * @return
     */
    ResponseDTO<FxWithdrawResponseDTO> withdraw(FxWithdrawRequestDTO withdrawRequest);
    
    /**
     * 根据请求流水号查询提现信息
     * @param requestId、channel
     * @return
     */
    ResponseDTO<FxWithdrawResponseDTO> queryWithdraw(FxWithdrawRequestDTO request);
    
    /**
     * 根据付款id查询付款记录
     * @param paymentId、channel
     * @return
     */
    ResponseDTO<FxWithdrawResponseDTO> queryWithdrawByPaymentId(FxWithdrawRequestDTO request);
    
    /**
     * 取消提现
     * @param paymentId、channel
     * @return
     */
    ResponseDTO<Boolean> cancelWithdraw(FxWithdrawRequestDTO request);
}
