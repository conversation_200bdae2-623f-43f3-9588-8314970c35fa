package com.fenbeitong.dech.api.model.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/23
 * @Description 打款金额验证
 */
@Data
@Builder
public class PaymentAmountCheckRespDto implements Serializable {

    private String bankName;

    /**
     * 绑定账户
     */
    private String bindAccount;

    /**
     * 分贝通订单号
     */
    private String ordNo;

    /**
     * 是否需要重新打款验证
     */
    private boolean tryPayment;

    /**
     * 打款验证是否成功 true 成功
     */
    private Boolean validSuccess;

    /**
     * 验证状态
     */
    private String validStatus;

    /**
     * 验证失败原因
     */
    private String failReason;


    /**
     * TODO
     */
    private String frontSeqNo;


    public static PaymentAmountCheckRespDto ofSuccess(String bankNo, String bankName, String ordNo,String frontSeqNo) {
        PaymentAmountCheckRespDto respDto = builder().validSuccess(Boolean.TRUE).bindAccount(bankNo).bankName(bankName).ordNo(ordNo).frontSeqNo(frontSeqNo).tryPayment(false).build();
        return respDto;
    }

    public static PaymentAmountCheckRespDto ofFail(String bankNo, String bankName, String ordNo, String failReason) {
        PaymentAmountCheckRespDto respDto = builder().validSuccess(Boolean.FALSE).bindAccount(bankNo).bankName(bankName).ordNo(ordNo).failReason(failReason).tryPayment(false).build();
        return respDto;
    }

    public static PaymentAmountCheckRespDto ofFail(String bankName, String ordNo, String failReason) {
        PaymentAmountCheckRespDto respDto = builder().validSuccess(Boolean.FALSE).bindAccount("").bankName(bankName).ordNo(ordNo).failReason(failReason).tryPayment(false).build();
        return respDto;
    }

    public static PaymentAmountCheckRespDto ofFailTry(String bankNo, String bankName, String ordNo, String failReason) {
        PaymentAmountCheckRespDto respDto = builder().validSuccess(Boolean.FALSE).bindAccount(bankNo).bankName(bankName).ordNo(ordNo).failReason(failReason).tryPayment(true).build();
        return respDto;
    }


}
