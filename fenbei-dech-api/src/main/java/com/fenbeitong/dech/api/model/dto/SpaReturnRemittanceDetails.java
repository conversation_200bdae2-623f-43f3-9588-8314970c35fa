package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SpaReturnRemittanceDetails
 * @Description: 3.9查询账户当日历史交易明细[4013]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@Data
public class SpaReturnRemittanceDetails implements Serializable {


    /**
     * 			批次号	C(20)	N	若属于批内则返回批次号
     */
    private String thirdVoucher;

    /**
     * 			原始交易对应转账凭证号	C(20)	Y	客户上送单笔凭证号
     */
    private String sThirdVoucher;

    /**
     * 			客户自定义凭证号	C(20)	N	用户输入则返回
     */
    private String cstInnerFlowNo;

    /**
     * 			交易日期时间	C(14)	Y	交易发送时间
     */
    private String transDate;

    /**
     * 			本方帐号	C(20)	Y
     */
    private String payAccNo;

    /**
     * 			对方帐号	C(30)	Y
     */
    private String oppAccNo;

    /**
     * 			收款方户名	C(30)	Y
     */
    private String oppAccName;

    /**
     * 			收款方开户行	C(30)	Y
     */
    private String oppBankName;

    /**
     * 		 交易金额	C(15)	Y
     */
    private String amount;

    /**
     * 			币种	C(3)	Y	交易币种
     */
    private String ccyCode;

    /**
     * 			退票日期	C(8)	Y
     */
    private String rejectDate;

    /**
     * 			退票描述	C(30)	Y
     */
    private String rejectRemark;

    /**
     * 			银行流水号	C(32)	Y	银行交易流水号
     */
    private String frontLogNo;

    /**
     * 			原始交易时间	C(15)	Y	原始交易时间
     */
    private String submitTime;

    /**
     * 			原始交易备注	C(30)	N	原始交易备注
     */
    private String remark;

}
