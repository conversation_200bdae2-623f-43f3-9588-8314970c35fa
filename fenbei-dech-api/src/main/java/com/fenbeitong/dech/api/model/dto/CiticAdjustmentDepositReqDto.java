package com.fenbeitong.dech.api.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/2/8
 * @Description
 */
@Data
public class CiticAdjustmentDepositReqDto implements Serializable {

    /**
     * 操作金额 分
     */
    private BigDecimal operationAmount;

    /**
     * 消费方账户
     */
    private String payAccountNo;

    /**
     * 消费方账户名
     */
    private String payAccountName;

    /**
     * 收款方账户
     */
    private String receiveAccountNo;

    /**
     * 收款方账户名
     */
    private String receiveAccountName;

    /**
     * 被调账日期char(8)
     */
    private String hostDate;

    /**
     * 被调账柜员交易号varchar(14)
     */
    private String hostFlw;

    /**
     * 被调账交易序号varchar(13)
     */
    private String hostSeq;

}
