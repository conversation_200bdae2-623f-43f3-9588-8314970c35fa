package com.fenbeitong.dech.api.model.dto;

import com.fenbeitong.dech.api.model.dto.vo.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/2/23
 * @Description 重新打款
 */
@Data
public class PaymentCheckTryReqDto implements Serializable {
    /**
     * 绑定账户
     */
    @NotBlank
    private String bindAccount;

    private String ordNo;

    private String companyId;

    /**
     * 银行编码
     */
    @NotBlank
    private String bankName;

    /**
     * 账户名称：同企业名称
     */
    private String accountName;


    /**
     * 短信验证码或者短信序列号
     */
    private String smsNo;


    private CompanyBasicInfoStructVo basicInfoStructVo;
    private BusinessLicenseStructVo businessLicenseStructVo;
    private LegalPersonInformationStructVo legalPersonInformationStructVo;
    private ContactInformationStructVo contactInformationStructVo;
    private CompanyAgreeLicenseVo companyAgreeLicenseVo;


}
