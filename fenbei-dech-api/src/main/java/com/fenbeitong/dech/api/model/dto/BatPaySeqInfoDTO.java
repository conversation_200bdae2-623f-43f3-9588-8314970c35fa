package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class BatPaySeqInfoDTO implements Serializable {

    /**
     * 批次号
     */
    @NotBlank
    private String batNo;

    /**
     * 分贝通流水号
     */
    @NotBlank
    private String tradeFlowId;

    /**
     * 请求银行订单号
     */
    @NotNull
    private String syncBankTransNo;

    /**
     * 银行编码
     */
    private String bankCode;
}
