package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

/**
 * <AUTHOR>
 * @Date 2021/2/8
 * @Description
 */
public interface IBankTradeService {


    /**
     * 银行转账
     * @param reqDto
     * @return
     */
    BankTradeRespDto bankTransfer(BankTransferReqDto reqDto);

    /**
     * 银行消费
     * @param reqDto
     * @return
     */
    BankTradeRespDto bankConsume(BankTradeReqDto reqDto);


    /**
     * 银行退款
     * @param reqDto
     * @return
     */
    BankTradeRespDto bankRefund(BankTradeReqDto reqDto);


    /**
     * 企业账户充值
     * @return
     */
    String companyAccountCharge();


    /**
     * 提现
     * @param reqDto
     * @return
     */
    BankCashOutRespDto cashOut(BankCashOutReqDto reqDto);

    /**
     * 打报销款
     * @param reqDto
     * @return
     */
    BankTradeRespDto reimbursementPayment(BankCashOutReqDto reqDto);

}
