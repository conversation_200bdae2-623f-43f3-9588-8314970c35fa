package com.fenbeitong.dech.api.model.dto.airwallex.cardholder;

import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/18
 */
@Data
public class AirCreateCardholderRpcRspDTO implements Serializable {
    private static final long serialVersionUID = 4442631177811211629L;

    /**
     * 状态
     */
    private String status;

    /**
     * 电话
     */
    private String mobile_number;

    /**
     * 个人持卡人的详细信息。
     */
    private BaseAirwallexRpcDTO.Individual individual;

    private String email;

    /**
     * 持卡人的唯一标识符
     */
    private String cardholder_id;

    /**
     * 持卡人地址
     */
    private BaseAirwallexRpcDTO.Address address;

    /**
     * 持卡人邮政地址
     */
    private BaseAirwallexRpcDTO.Address postal_address;
}
