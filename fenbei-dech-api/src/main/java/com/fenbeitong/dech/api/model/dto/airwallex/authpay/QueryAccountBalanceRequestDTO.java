package com.fenbeitong.dech.api.model.dto.airwallex.authpay;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class QueryAccountBalanceRequestDTO implements Serializable {

	/**
	 * 必填项
	 */
	@NotNull
	private String companyId;
	
	private String accountId;
	
	private String accountNo;
	
	/**
	 * @see com.fenbeitong.finhub.common.constant.CurrencyEnum 不传则默认为 CNY
	 */
	private String currency;
}
