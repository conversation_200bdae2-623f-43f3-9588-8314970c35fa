package com.fenbeitong.dech.api.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ZsPayResDto extends UnionPayBaseRespDto implements Serializable {
    private static final long serialVersionUID = 2273992742351162615L;

    @ApiModelProperty(value = "付款凭证号")
    private String voucherNum;

    @ApiModelProperty(value = "银行侧订单编号")
    private String orderNo;

}
