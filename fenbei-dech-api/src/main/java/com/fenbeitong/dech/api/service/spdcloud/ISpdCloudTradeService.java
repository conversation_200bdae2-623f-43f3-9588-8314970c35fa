package com.fenbeitong.dech.api.service.spdcloud;


import com.fenbeitong.dech.api.model.dto.spdcloud.req.SpdbBatchPayRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.spdcloud.req.SpdbSinglePayRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.spdcloud.resp.SpdbBatchPayRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.spdcloud.resp.SpdbSinglePayRpcRespDTO;

public interface ISpdCloudTradeService {

    /**
     * 单笔交易
     * @param spdbSinglePayReqDTO
     */
    SpdbSinglePayRpcRespDTO singlePay(SpdbSinglePayRpcReqDTO spdbSinglePayReqDTO);


    SpdbBatchPayRpcRespDTO batchPay(SpdbBatchPayRpcReqDTO spdbBatchPayReqDTO);

}
