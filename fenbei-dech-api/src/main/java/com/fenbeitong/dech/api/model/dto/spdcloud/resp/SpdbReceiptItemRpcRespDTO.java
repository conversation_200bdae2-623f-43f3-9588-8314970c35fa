package com.fenbeitong.dech.api.model.dto.spdcloud.resp;


import lombok.Data;

import java.io.Serializable;

@Data

public class SpdbReceiptItemRpcRespDTO implements Serializable{

    /**
     * 账单下载渠道为0，“报文头客户号_表单编号.pdf”即为这条流水对应的回单文件名，可从我行第三方文件传输平台获取该回单文件；账单下载渠道为1，可从文件传输平台下载文件
     */
    private String acceptNo ;

    /**
     * 返回结果
     */
    private String result ;

    /**
     *返回结果说明
     */
    private String resultMsg ;

    /**
     *对方账号
     */
    private String oppositeAcctNo ;

    /**
     *对方行号
     */
    private String oppositeBankNo ;

    /**
     *借贷标记
     */
    private String debitFlag ;

    /**
     *交易码
     */
    private String businessCode ;

    /**
     *柜员流水
     */
    private String backhostGyno ;

    /**
     *传票组内序号
     */
    private String subpoenaSeqNo ;

    /**
     *交易日期
     */
    private String transDate ;

    /**
     *交易金额
     */
    private String transAmount ;

    /**
     *
     */
    private String reserve1 ;

    /**
     *
     */
    private String reserve2 ;

    /**
     *
     */
    private String reserve3 ;

}
