package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

/**
 * 挂帐
 *
 * <AUTHOR>
 */
public interface IBankSuspendAccountService {

    /**
     * 挂帐认款
     *
     * @param reqDto
     * @return
     */
    BankTradeRespDto suspendTransfer(SuspendAccountTradeReqDto reqDto);

    /**
     * 挂帐撤销
     *
     * @param reqDto
     * @return
     */
    BankTradeRespDto suspendRevoke(SuspendAccountRevokeReqDto reqDto);

    /**
     * 原路退回
     * @param reqDto
     * @return
     */
    BankTradeRespDto suspendRefundFund(SuspendAccountRefundFundReqDto reqDto);

    /**
     * 企业账户入金 目前就浦发
     * @param reqDto
     * @return
     */
    BankTradeRespDto companyAcctCashIn(SuspendAccountTradeReqDto reqDto);
}
