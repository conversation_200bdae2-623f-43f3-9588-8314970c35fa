package com.fenbeitong.dech.api.service.nbcb;

import com.fenbeitong.dech.api.model.dto.nbcb.*;

import java.util.List;

/**
 * 宁波银行账户
 */
public interface IBankEntNBCBAccountService {
    NBCBQueryAccountRespDTO queryAccount(NBCBQueryAccountReqDTO nbcbQueryAccountReqDTO);

    NBCBQueryAccDetailRespDTO queryAccDetail(NBCBQueryAccDetailReqDTO nbcbQueryAccDetailReqDTO);

    NBCBQueryReceiptRespDTO queryReceipt(NBCBQueryReceiptReqDTO nbcbQueryReceiptReqDTO);

    NBCBQueryBillDownloadIdRespDTO queryBillDownloadId(NBCBQueryBillDownloadIdReqDTO nbcbQueryBillDownloadIdReqDTO);

    /**
     * 银企联账户实时余额查询
     * @param companyId
     * @param bankAcctNos
     * @return
     */
    List<NbcbAcctBalanceRespDTO> queryAccountBalanceInfo(String companyId,String custId,List<String> bankAcctNos);

    /**
     * 配置公私钥
     */
    boolean initConfig();
}
