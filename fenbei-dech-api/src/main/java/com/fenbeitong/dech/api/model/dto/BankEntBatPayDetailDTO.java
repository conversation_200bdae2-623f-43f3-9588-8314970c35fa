package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BankEntBatPayDetailDTO implements Serializable {

    /**
     * 分贝通流水号
     */
    @NotNull
    private String tradeFlowId;

    /**
     * 同招商银企联交互批次明细序号（8位数字）
     */
    private String syncBankTransNo;

    /**
     * 账户ID（同account_info accountId）
     */
    @NotNull
    private String accountInfoId;

    /**
     * 银行编码
     */
    @NotNull
    private String bankCode;

    /**
     * 付款方账号
     */
    @NotNull
    private String payAccountNo;

    /**
     * 付款方账号名称
     */
    @NotNull
    private String payAccountName;

    /**
     * 收款方账号
     */
    @NotNull
    private String receiveAccountNo;

    /**
     * 收款方账号名称
     */
    @NotNull
    private String receiveAccountName;

    /**
     * 跨行标识  01:本地本行；02:异地本行；03:本地他行；04:异地他行；05:国外他行；06:国外本行;99:自动补充
     */
    @NotNull
    private String toBankType;

    /**
     * 操作金额 分
     */
    @NotNull
    private BigDecimal operationAmount;

    /**
     * 是否是同步交易 true 是同步
     */
    @NotNull
    private Boolean syncTrade;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 收方开户行联行号
     */
    private String toBankNo;

    /**
     * 收方开户行名
     */
    private String toBrchName;

    /**
     * 收方内部编号（员工编号）
     */
    private String receiveInnerNo;

    /**
     * 收方证件类型
     */
    private String receiveIdType;

    /**
     * 收方证件号码
     */
    private String receiveIdCode;

    /**
     * 用途
     */
    private String useDesc;

    /**
     * 附言
     */
    private String remark;
}
