package com.fenbeitong.dech.api.model.dto.airwallex;

import lombok.Data;

import java.io.Serializable;

@Data
public class FxConversionsListAllRpcReqDTO implements Serializable {
    private String channel;

    /**
     * 客户购买的货币（以3个字母的ISO-4217格式）。
     */
    private String buyCurrency;

    /**
     * 用于指定要返回的结果的日期范围的起始值。日期范围特定于转换创建（转换的created_at日期）。指定开始创建日期（以ISO-8601格式）。此开始日期包含范围。
     */
    private String fromCreatedAt;

    /**
     * 页数，从0开始。
     */
    private String pageNum;

    /**
     * 每页的结果数。默认值为100。
     */
    private String pageSize;

    /**
     * 客户出售的货币（3个字母的ISO-4217格式）。
     */
    private String requestId;

    /**
     * 客户出售的货币（3个字母的ISO-4217格式）。
     */
    private String sellCurrency;

    /**
     * 转换的状态。可以是AWAITING_FUNDS,CANCELLED,PENDING_SETTLEMENT,SETTLED,
     * ROLLOVER_REQUESTED,PENDING_ROLLOVER中的一个。
     */
    private	String	status;

    /**
     * 用于指定要返回的结果的日期范围的结束值。日期范围是针对转换的创建（一个转换的credate_at日期）。
     * 指定一个结束的credate_at日期（ISO-8601格式）。这个结束日期包括在这个范围内。
     */
    private	String	toCreatedAt;
}
