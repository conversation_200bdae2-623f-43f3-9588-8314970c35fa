package com.fenbeitong.dech.api.model.dto.airwallex.cardholder;

import com.fenbeitong.dech.api.model.dto.base.BaseModel;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by FBT on 2023/3/29.
 */
@Data
public class GetAllCardHoldersRpcReqDTO extends BaseModel {


    /**
     * 筛选依据的持卡人状态-待定、就绪、禁用之一 PENDING, READY, DISABLED
     */
    private String cardholder_status;

    /**
     * 页码，从0开始。
     */
    private Integer page_num = 0;

    /**
     * 每页的结果数
     */
    private Integer page_size = 500;

}
