package com.fenbeitong.dech.api.model.dto.lianlian.card.resp;

import com.fenbeitong.dech.api.model.dto.lianlian.LianLianBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LianLianPhysicalCardOrderQueryRpcRespDTO extends LianLianBaseDTO {

    private String orderStatus;

    private String failCode;

    private String failReason;
    private LianLianPhysicalCardDeliveryLogisticsAddress deliveryLogistics;

    private LianLianPhysicalCardApplyShippingAddress shippingAddress;
}
