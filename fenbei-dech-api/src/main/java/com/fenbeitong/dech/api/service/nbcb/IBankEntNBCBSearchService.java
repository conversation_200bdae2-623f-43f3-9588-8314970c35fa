package com.fenbeitong.dech.api.service.nbcb;

import com.fenbeitong.dech.api.model.dto.nbcb.NBCBGetBtBankAccAllowListReqDTO;
import com.fenbeitong.dech.api.model.dto.nbcb.NBCBGetBtBankAccAllowListRespDTO;
import com.fenbeitong.dech.api.model.dto.nbcb.NBCBGetGeneralDownloadUrlReqDTO;
import com.fenbeitong.dech.api.model.dto.nbcb.NBCBGetGeneralDownloadUrlRespDTO;

/**
 * 宁波银行查询查询接口
 */
public interface IBankEntNBCBSearchService {
    /**
     * 4.1 获取下载地址通用接口
     */
    NBCBGetGeneralDownloadUrlRespDTO getGeneralDownloadUrl(NBCBGetGeneralDownloadUrlReqDTO NBCBGetGeneralDownloadUrlReqDTO);

    /**
     * 4.4 账户白名单查询接口
     * @param nbcbGetBtBankAccAllowListReqDTO 白名单参数
     * @return
     */
    NBCBGetBtBankAccAllowListRespDTO getBtBankAccAllowListRespDTO(NBCBGetBtBankAccAllowListReqDTO nbcbGetBtBankAccAllowListReqDTO);
}
