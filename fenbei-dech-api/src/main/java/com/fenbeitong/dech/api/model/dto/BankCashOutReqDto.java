package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/2/26
 * @Description 提现
 */
@Data
public class BankCashOutReqDto implements Serializable {

    /**
     * 流水id
     */
    private String accountFlowId;

    /**
     * 企业id
     */
    @NotBlank
    private String companyId;
    /**
     * 银行名称
     */
    @NotBlank
    private String bankName;

    /**
     * 操作金额 分
     */
    @NotBlank
    private BigDecimal operationAmount;


    private String operationUserId;

    /**
     * 订单流水号
     */
    @NotBlank
    private String txnId;

    /**
     * 消费方账户(银行虚户id)
     */
    @NotBlank
    private String accountNo;

    /**
     * 消费方银行卡号
     */
    private String bankAccountNo;

    /**
     * 提现的目标账户
     */
    @NotBlank
    private String targetAccountNo;

    /**
     * 银行编码(提现至非绑定账户时必填)
     */
    private String targetAcctBankCode;

    /**
     * 开户银行联行号(提现至非绑定账户时必填)
     */
    private String bankBrnNo;

    /**
     * 开户银行总行号(提现至非绑定账户时必填)
     */
    private String bankMainNo;

    /**
     *  开户银行所在城市 （浦发精确到省市，例如 安徽省 合肥市）
     */
    private String bankAddress;

    /**
     * 是否提现至绑定账户 true 是
     */
    private Boolean trgBankIsBindAcct;

    /**
     * 开户名称(提现至非绑定账户时必填)
     */
    private String targetAccountName;

    /**
     * 账户类型
     */
    @NotBlank
    private Integer accountSubType;

    /**
     * 收款方是否本行 true 是
     */
    private Boolean trgBankIsOwnAcct;

    /**
     * 付款交易备注
     */
    private String remark;

    /**
     * 标识  1. 企业  2. 个人
     */
    @NotNull
    private Integer customerType;

    /**
     * 联系手机号(广发用)
     */
    private String contactMobile;

    /**
     * 短信验证码（广发、连连）
     */
    private String verCode;
    
    /**
     * 原冻结受理编号(廊坊)
     */
    private Long transId;
    
    /**
     * 开户银行名称(廊坊)，例如：中国建设银行
     */
    private String bankNameShow;
    /**
     * 上账流水号(廊坊)
     */
    private Long accountSerialNumber;
    
    /**
     * 银行交易流水号 类似连连二次交互时使用
     */
    private String bankTransNo;
    
    /**
     * 商户id
     */
    private String mchId;
}
