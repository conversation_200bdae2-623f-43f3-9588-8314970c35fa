package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.lf.LfTradeQueryReqDTO;
import com.fenbeitong.dech.api.model.dto.lf.LfTradeQueryRespDTO;
import com.fenbeitong.dech.api.model.dto.lf.LfTradeReceiptQueryReqDTO;
import com.fenbeitong.dech.api.model.dto.lf.LfTradeReceiptRespDTO;

/**
 * @description:廊坊银行查询
 * @author: yanqiu.hu
 * @create: 2022-06-22 15:23:56
 * @Version 1.0
 **/
public interface ILfBankSearchService {

    /**
     * 交易状态查询(例如：冻结、解冻交易等)
     *
     * @param lfQueryTradeReqDTO
     * @return
     */
    LfTradeQueryRespDTO queryTrade(LfTradeQueryReqDTO lfQueryTradeReqDTO);


    /**
     * 电子回单获取
     *
     * @param reqDTO
     * @return
     */
    LfTradeReceiptRespDTO queryTxnReceipt(LfTradeReceiptQueryReqDTO reqDTO);

}
