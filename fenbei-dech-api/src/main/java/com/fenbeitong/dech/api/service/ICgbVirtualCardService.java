package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.cgb.*;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
public interface ICgbVirtualCardService {

    /**
     * 上传客户影像资料
     * @param cgbUploadCustImageDataReqDTO 影像资料
     * @return UploadCustImageDataResponseDTO
     */
    CgbUploadCustImageDataRespDTO uploadCustImageData(CgbUploadCustImageDataReqDTO cgbUploadCustImageDataReqDTO);

    /**
     * 发送短信验证码
     * @param cgbSendMsgCodeReqDTO
     * @return SendMsgCodeRespDTO
     */
    CgbSendMsgCodeRespDTO sendMsgCode(CgbSendMsgCodeReqDTO cgbSendMsgCodeReqDTO);

    /**
     * 开立银行账户
     * @param cgbCreateAccountTypeOPReqDTO 开立银行账户
     * @return CgbCreateAccountTypeOPRespDTO
     */
    CgbCreateAccountTypeOPRespDTO createAccountTypeOP(CgbCreateAccountTypeOPReqDTO cgbCreateAccountTypeOPReqDTO);

    /**
     * 回查二三类户开户结果。
     * @param cgbQueryUpgradeAccountTypeHWReqDTO 回查二三类户结果
     * @return QueryUpgradeAccountTypeHWRespDTO
     */
    CgbQueryUpgradeAccountTypeHWRespDTO queryUpgradeAccountTypeHW(CgbQueryUpgradeAccountTypeHWReqDTO cgbQueryUpgradeAccountTypeHWReqDTO);

    /**
     * 查询账户余额
     * @param queryAccountBalanceReqDTO 查询账户余额
     * @return QueryAccountBalanceRespDTO
     */
    CgbQueryAccountBalanceRespDTO queryAccountBalance(CgbQueryAccountBalanceReqDTO queryAccountBalanceReqDTO);

    /**
     * 设置非绑定入金
     * @param setAcountEntrySignHWReqDTO 非绑定入金
     * @return SetAcountEntrySignHWRespDTO
     */
    CgbSetAcountEntrySignHWRespDTO setAcountEntrySignHW(CgbSetAccountEntrySignHWReqDTO setAcountEntrySignHWReqDTO);

    /**
     * 圈存
     * @param trapReqDTO 圈存请求DTO
     * @return TrapRespDTO
     */
    CgbTrapRespDTO trap(CgbTrapReqDTO trapReqDTO);

    /**
     * 解圈存
     * 通过本接口完成资金解圈存（回收资金）。资金链路：II类户→III类户→分贝通对公户。
     * 如解圈存资金超2000元，需要拆分多笔小于2000元交易完成解圈存。
     */
    CgbSloveTrapRespDTO sloveTrap(CgbSloveTrapReqDTO sloveTrapReqDTO);

    /**
     * 将二三类户与企业账簿做绑定。
     * @param bindAccountReqDTO 绑定账户请求
     */
    CgbBindAccountRespDTO bindAccount(CgbBindAccountReqDTO bindAccountReqDTO);

    /**
     * 查询省市区
     */
    CgbQueryProvinceCityRespDTO queryProvinceCity(CgbQueryProvinceCityReqDTO cgbQueryProvinceCityReqDTO);

    /**
     * 2.2 查询职业
     * @param cgbQueryOccupationReqDTO
     * @return
     */
    CgbQueryOccupationRespDTO queryOccupation(CgbQueryOccupationReqDTO cgbQueryOccupationReqDTO);

    /**
     * 查询圈存编号
     */
    CgbHoldNoQueryRespDTO holdNoQuery(CgbHoldNoQueryReqDTO holdNoQueryReqDTO);
    /**
     * 查询圈存金额
     * @param depositAmountQueryReqDTO
     * @return
     */
    CgbDepositAmountQueryRespDTO depositAmountQuery(CgbDepositAmountQueryReqDTO depositAmountQueryReqDTO);

    /**
     * 回查圈存/解圈存结果
     */
    CgbTrapQueryRespDTO trapQuery(CgbTrapQueryReqDTO cgbTrapQueryReqDTO);
    /**
     * 销户预检查
     */
    CgbEAccLogoutCheckRespDTO eAccLogoutCheck(CgbEAccLogoutCheckReqDTO eAccLogoutCheckReqDTO);

    /**
     * 销户
     * @return
     */
    CgbAccountLifeCycleRespDTO accountLifeCycle(CgbAccountLifeCycleReqDTO cgbAccountLifeCycleReqDTO);

    /**
     * 二类户圈存
     */
    CgbAccountTrapMoneyRespDTO accountTrapMoney(CgbAccountTrapMoneyReqDTO cgbAccountTrapMoneyReqDTO);

    /**
     * 增加账户绑定卡
     * @param cgbBindingCardTypeHwReqDTO 绑定DTO
     * @return  CgbBindingCardTypeHwRespDTO
     */
    CgbBindingCardTypeHwRespDTO bindingCardTypeHW(CgbBindingCardTypeHwReqDTO cgbBindingCardTypeHwReqDTO);

    /**
     * 解除账户绑定卡
     * @param cgbUnlockBoundCardTypeHWReqDTO 解绑DTO
     * @return CgbUnlockBoundCardTypeHWRespDTO
     */
    CgbUnlockBoundCardTypeHWRespDTO unlockBoundCardTypeHW(CgbUnlockBoundCardTypeHWReqDTO cgbUnlockBoundCardTypeHWReqDTO);

    /**
     * 当二类户圈存接口【accountTrapMoney】请求超时，商户可通过本接口根据原交易请求流水号回查原交易结果。
     * @param queryTrapReqDTO
     * @return CgbQueryTrapRespDTO
     */
    CgbQueryTrapRespDTO queryTrap(CgbQueryTrapReqDTO queryTrapReqDTO);

    /**
     * 接口描述：本接口作用为从绑定银行卡充值到二类户可用余额。
     * @param cgbRechargeReqDTO 充值DTO
     * @return CgbRechargeRespDTO
     */
    CgbRechargeRespDTO recharge(CgbRechargeReqDTO cgbRechargeReqDTO);

    /**
     * 接口描述：本接口作用为从二类户可用余额提现到绑定银行卡。
     * @param cgbWithdrawReqDTO 提现DTO
     * @return CgbWithdrawRespDTO
     */
    CgbWithdrawRespDTO withdraw(CgbWithdrawReqDTO cgbWithdrawReqDTO);

    /**
     * 回查充值提现交易结果 queryTradeResult
     * @param cgbQueryTradeResultReqDTO 查询交易结果DTO
     * @return CgbQueryTradeResultRespDTO
     */
    CgbQueryTradeResultRespDTO queryTradeResult(CgbQueryTradeResultReqDTO cgbQueryTradeResultReqDTO);

    /**
     * 接口描述：客户勾选同意“个人信息授权书”后，商户需调用本接口完成授权协议签署。
     * @param cgbCustSignAgrtAuth
     * @return
     */
    CgbCustSignAgrtAuthRespDTO custSignAgrtAuth(CgbCustSignAgrtAuthReqDTO cgbCustSignAgrtAuth);


    /**
     * 签署协议授权查询
     * @param custSignAgrtAuthQueryReqDTO
     * @return CustSignAgrtAuthQueryRespDTO
     */
    CgbCustSignAgrtAuthQueryRespDTO custSignAgrtAuthQuery(CgbCustSignAgrtAuthQueryReqDTO custSignAgrtAuthQueryReqDTO);


    /**
     * 签署协议授权撤销
     * @param custSignAgrtAuthCancelReqDTO
     * @return
     */
    CgbCustSignAgrtAuthCancelRespDTO custSignAgrtAuthCancel(CgbCustSignAgrtAuthCancelReqDTO custSignAgrtAuthCancelReqDTO);


    /**
     * 开户协议回传
     * @param openAcctAgrtPostbackReqDTO
     * @return
     */
    CgbOpenAcctAgrtPostbackRespDTO openAcctAgrtPostback(CgbOpenAcctAgrtPostbackReqDTO openAcctAgrtPostbackReqDTO);

    /**
     * 查询客户签约信息
     * @param queryCustSignInfoReqDTO
     * @return
     */
    CgbQueryCustSignInfoRespDTO queryCustSignInfo(CgbQueryCustSignInfoReqDTO queryCustSignInfoReqDTO);
}
