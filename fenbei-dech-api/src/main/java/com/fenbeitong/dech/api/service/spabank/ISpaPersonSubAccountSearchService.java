package com.fenbeitong.dech.api.service.spabank;

import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaPersonSubAccountFlowRespDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaPersonSubAccountRespDto;

import java.util.List;

/**
 * @description: 平安个人见证账户查询接口
 * @author: yanqiu.hu
 * @create: 2022-08-12 10:15:23
 * @Version 1.0
 **/
public interface ISpaPersonSubAccountSearchService {


    /**
     * 根据子账户ID查询详情
     *
     * @param subAccountId
     * @return
     */
    SpaPersonSubAccountRespDto queryBySubAccountId(String subAccountId);

    /**
     * 根据子账户no查询详情
     *
     * @param subAccountNo
     * @return
     */
    List<SpaPersonSubAccountRespDto> queryBySubAccountNo(String subAccountNo);

    /**
     * 根据bindCardNo查询详情
     *
     * @param bindCardNo
     * @return
     */
    List<SpaPersonSubAccountRespDto> queryByBindCardNo(String bindCardNo);

    /**
     * 根据身份证号查询见证子账户
     *
     * @param certNo
     * @return
     */
    List<SpaPersonSubAccountRespDto> queryByCertNo(String certNo);

    /**
     * 根据员工手机号查询见证子账户
     *
     * @param employeePhoneNo
     * @return
     */
    List<SpaPersonSubAccountRespDto> queryByEmployeePhoneNo(String employeePhoneNo);

    /**
     * 根据手机号查询见证子账户
     *
     * @param phoneNo
     * @return
     */
    List<SpaPersonSubAccountRespDto> queryByPhoneNo(String phoneNo);

    /**
     * 通过流水Id查询详情
     *
     * @param subAccountFlowId
     * @return
     */
    SpaPersonSubAccountFlowRespDto queryBySubAccountFlowId(String subAccountFlowId);

    /**
     * 通过bizNo和操作类型查询详情
     *
     * @param bizNo
     * @param operationType
     * @return
     */
    List<SpaPersonSubAccountFlowRespDto> queryByBizNoAndOperationType(String bizNo, int operationType);

    List<SpaPersonSubAccountFlowRespDto> queryByReBizNo(String txnId);
}
