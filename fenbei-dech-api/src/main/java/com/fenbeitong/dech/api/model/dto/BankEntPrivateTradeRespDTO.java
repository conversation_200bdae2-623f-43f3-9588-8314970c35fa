package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BankEntPrivateTradeRespDTO implements Serializable {

    /**
     * 批次受理结果
     */
    private String batStatus;

    /**
     * 失败原因
     */
    private String batFailMsg;

    /**
     * 批次号
     */
    private String batNo;

    /**
     * 支付成功的订单集
     */
    private List<BankEntBaseTradeRespDTO> successList;

    /**
     * 支付失败的订单集
     */
    private List<BankEntBaseTradeRespDTO> failList;

    /**
     * 支付进行中的订单集
     */
    private List<BankEntBaseTradeRespDTO> processingList;

}
