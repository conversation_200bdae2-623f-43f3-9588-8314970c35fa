package com.fenbeitong.dech.api.service.icbc;

import com.fenbeitong.dech.api.model.dto.icbc.IcbcReceiptFileRespDTO;
import com.fenbeitong.dech.api.model.dto.icbc.IcbcReceiptOssRespDTO;

import java.util.List;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2023-04-10 16:48:30
 * @Version 1.0
 **/
public interface IBankentIcbcSearchService {

    /**
     * 查询指定账户下的电子回单所有压缩包文件
     *
     * @param accountNo 账户
     * @param time 时间  yyyyMMdd
     * @return
     */
    List<IcbcReceiptFileRespDTO> queryAllZipFileNameByAccountNo(String accountNo, String time);

    /**
     * 查询指定压缩包电子回单
     * @param accountNo
     * @param filName
     * @return
     */
    List<IcbcReceiptOssRespDTO> queryReceiptByZipFileName(String accountNo, String filName);

    /**
     * 查询单条记录电子回单
     * @param accountNo
     * @param time
     * @param costBillNo
     * @return
     */
    IcbcReceiptOssRespDTO queryReceiptByCostBillNo(String accountNo, String time, String costBillNo);
}
