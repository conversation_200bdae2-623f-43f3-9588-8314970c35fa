package com.fenbeitong.dech.api.service.airwallex;


import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.AirCreateCardholderRpcRspDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/4/21.
 */

public interface IAirWallexCardHolderService {


    /**
     * 查询所有持卡人
     * @param reqDTO
     * @return
     */
    GetAllCardHoldersRpcRespDTO getAllCardHolder(GetAllCardHoldersRpcReqDTO reqDTO);

    /**
     * 获取持卡人信息
     * @param id
     * @return
     */
    CardHolderRpcRespDTO getCardHolderDetails(String id);

    /**
     * 创建持卡人
     *
     * @param reqDTO
     * @return
     */
    AirCreateCardholderRpcRspDTO createCardholder(AirCreateCardHolderRpcReqDTO reqDTO);


    /**
     * 更新持卡人
     * @param reqDTO
     * @return
     */
    AirCreateCardholderRpcRspDTO updateCardholder(AirUpdateCardHolderRpcReqDTO reqDTO);

}
