package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/4/8
 */
@Data
public class SpaTradeDetailsReqDTO implements Serializable {

    /*
     * 功能标志	string(1)	Y	1:当日，0：历史
     */
    private String functionFlag;

    /*
     * 	开始日期	string(8)	N	若是历史查询，则必输，当日查询时，不起作用；开始日期不能超过当前日期
     */
    private String startDate;

    /*
     * 	终止日期	string(8)	N	若是历史查询，则必输，当日查询时，不起作用；终止日期不能超过当前日期
     */
    private String endDate;

    /*
     * 	页码	string(6)	Y	起始值为1，每次最多返回20条记录，第二页返回的记录数为第21至40条记录，第三页为41至60条记录，顺序均按照建立时间的先后
     */
    private String pageNum;
}
