package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName SpaBankTradeRespDataDto
 * @Description: 平安交易公共参数
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpaBankTradeRespDataDto implements Serializable {

    /**
     * 银行交易流水号
     */
    private String bankTransNo;

    /**
     * 上账流水号
     */
    private String bassTxnId;

}
