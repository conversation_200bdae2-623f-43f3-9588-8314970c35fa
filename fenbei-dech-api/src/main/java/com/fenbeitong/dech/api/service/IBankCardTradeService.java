package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

/**
 * <AUTHOR>
 * @date 2022年10月11日 15:39
 * @description 虚拟卡交易公共入口
 */
public interface IBankCardTradeService {

    /**
     * 额度发放
     * @param bankCardQuotaReqDTO
     * @return
     */
    BankCardTradeRespBaseDTO quotaDistribution(BankCardQuotaReqDTO bankCardQuotaReqDTO);

    /**
     * 额度退回
     * @param bankCardQuotaReqDTO
     * @return
     */
    BankCardTradeRespBaseDTO quotaRecovery(BankCardQuotaReqDTO bankCardQuotaReqDTO);

    /**
     * 错花还款
     * @param bankCardWrongPayReqDTO
     * @return
     */
    BankCardTradeRespBaseDTO wrongPayRepayment(BankCardWrongPayReqDTO bankCardWrongPayReqDTO);

    /**
     * 错花还款退回
     * @param bankCardWrongPayReqDTO
     * @return
     */
    BankCardTradeRespBaseDTO wrongPayRepaymentReturn(BankCardWrongPayReqDTO bankCardWrongPayReqDTO);

    /**
     * 查询交易状态
     * @param bankCardQueryTradeReqDTO
     * @return
     */
    BankCardQueryTradeRespDTO queryTradeStatus(BankCardQueryTradeReqDTO bankCardQueryTradeReqDTO);


    /**
     * 平安结息或外部错误转入自动提现
     */
    BankCardCashOutRespDTO cashOut(BankCardCashOutReqDTO bankCardCashOutReqDTO);
}
