package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName SpaBankReturnRemittanceRespBaseDto
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SpaBankReturnRemittanceRespBaseDto implements Serializable {

    private String code;

    private String message;

    private SpaBankReturnRemittanceRespDto data;

    public static SpaBankReturnRemittanceRespBaseDto fail(String code, String message){
        SpaBankReturnRemittanceRespBaseDto respDto = SpaBankReturnRemittanceRespBaseDto.builder().code(code)
                .message(message)
                .build();
        return respDto;
    }

    public static SpaBankReturnRemittanceRespBaseDto success(SpaBankReturnRemittanceRespDto respDataDto){
        SpaBankReturnRemittanceRespBaseDto respDto = SpaBankReturnRemittanceRespBaseDto.builder().code("000000")
                .message("success")
                .data(respDataDto)
                .build();
        return respDto;
    }

}
