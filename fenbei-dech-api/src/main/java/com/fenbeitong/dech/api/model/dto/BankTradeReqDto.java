package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/2/8
 * @Description
 */
@Data
public class BankTradeReqDto extends BankTradeBaseReqDto {

    /**
     * 原单号 （退款时上传）
     */
    private String orgSysOrdNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 平安-冻结订单号取tb_fund_freezen_flow的sync_bank_trans_no字段
     */
    private String freezeOrderNo;

    /**
     * 廊坊-消费(分贝券支付时必填)-消费传入发放券企业账号
     */
    private Long marketUserId;
    /**
     * 廊坊-消费商品名称
     */
    private String goods;
    /**
     * 廊坊-消费资金用途，如XXX差旅费、XXX采购；XXX为购买人手机号
     */
    private String funds;


}
