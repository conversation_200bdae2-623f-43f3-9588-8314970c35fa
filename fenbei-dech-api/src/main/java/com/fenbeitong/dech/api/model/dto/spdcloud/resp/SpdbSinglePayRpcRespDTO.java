package com.fenbeitong.dech.api.model.dto.spdcloud.resp;


import lombok.Data;

import java.io.Serializable;

@Data
public class SpdbSinglePayRpcRespDTO implements Serializable {
    /**
     * 交易状态
     * 交易当前状态
     *0-请至网银处理
     *1-成功
     *2-失败
     *（当异步判断标志为0时返回0）
     */
    private String onlineBankStatus;

    /**
     * 受理编号
     * 交易的受理编号，同步模式下有值。
     *异步模式下为空
     */
    private String reserve1;
    /**
     * 错误信息
     * 记录网银录入失败的原因，返回状态为2时有值
     */
    private String errMessage;
    /**
     * 电子凭证号
     */
    private String elecChequeNo;

}
