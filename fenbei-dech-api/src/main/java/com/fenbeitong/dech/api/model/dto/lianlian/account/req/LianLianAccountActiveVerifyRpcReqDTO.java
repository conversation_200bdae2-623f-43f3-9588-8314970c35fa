package com.fenbeitong.dech.api.model.dto.lianlian.account.req;

import lombok.Data;

import java.io.Serializable;
@Data
public class LianLianAccountActiveVerifyRpcReqDTO implements Serializable {
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 银行账户号 加款充值的银行账户号
     */
    private String bankAccountNo;
    /**
     商户系统唯一交易流水号。与申请的交易流水号一致。
     */
    private String outOrderNo;
    /**
     * 授权令牌。有效期10分钟。需要在账户激活验证接口上送改字段
     */
    private String token;
    /**
     * 短信验证码
     */
    private String verifyCode;
}
