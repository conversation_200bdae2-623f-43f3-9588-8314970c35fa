package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-09-16 10:02:11
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindQueryRespDto implements Serializable {

    /**
     * 是否成功： true：成功  false:失败
     */
    private Boolean isSuccess;
    /**
     * 失败原因
     */
    private String failReason;

    /*
     * 本次交易返回查询结果记录数	string(8)
     */
    private String resultNum;

    /*
     * 	起始记录号	string(8)
     */
    private String startRecordNo;

    /*
     * 	结束标志	string(1)		0：否  1：是
     */
    private String endFlag;

    /*
     * 	符合业务查询条件的记录总数	string(4)		重复次数（一次最多返回20条记录）
     */
    private String totalNum;

    private List<BindQueryItemDto> tranItemList;

}
