package com.fenbeitong.dech.api.service.airwallex;

import java.util.List;

import com.fenbeitong.dech.api.model.dto.airwallex.ConfirmationLetterDTO;

/**  
 * @Description: 
 * <AUTHOR>
 * @date 2023年6月29日 
*/
public interface AirwallexConfirmationLetterService {

    /**
     * 下载确认函，并上传oss
     * @param request
     * @return
     */
    List<ConfirmationLetterDTO> download(List<ConfirmationLetterDTO> request);
    
    /**
     * 下载确认函，并上传oss
     * @param request
     * @return
     */
    ConfirmationLetterDTO download(ConfirmationLetterDTO request);
}
