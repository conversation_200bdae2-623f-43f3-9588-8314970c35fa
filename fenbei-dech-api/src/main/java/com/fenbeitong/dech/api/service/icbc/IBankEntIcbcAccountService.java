package com.fenbeitong.dech.api.service.icbc;

import com.fenbeitong.dech.api.model.dto.icbc.IcbcAcctBalanceRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.icbc.IcbcAcctBalanceRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.icbc.IcbcAcctTradeDetailDTO;
import com.fenbeitong.dech.api.model.dto.icbc.IcbcAcctTradeDetailRpcQueryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-03-30 下午2:35
 */

public interface IBankEntIcbcAccountService {

    /**
     * 账户交易明细查询
     * @param icbcAcctTradeDetailRpcQueryDto
     * @return
     */
     IcbcAcctTradeDetailDTO queryTradeDetail(IcbcAcctTradeDetailRpcQueryDto icbcAcctTradeDetailRpcQueryDto);

    /**
     * 账户余额查询
     * @param acctBalanceRpcReqDTOList
     * @return
     */
     IcbcAcctBalanceRpcRespDTO queryAcctBalance(List<IcbcAcctBalanceRpcReqDTO> acctBalanceRpcReqDTOList);
}
