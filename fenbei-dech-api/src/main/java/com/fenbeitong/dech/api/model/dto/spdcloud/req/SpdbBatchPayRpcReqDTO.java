package com.fenbeitong.dech.api.model.dto.spdcloud.req;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Data
public class SpdbBatchPayRpcReqDTO implements Serializable {

    /**
     * 统一社会信用代码 Y
     * 通过统一社会信用代码来区分客户
     */
    private String unfSocCrdtNo ;

    /**
     * 客户名称 N
     */
    private String masterName ;

    /**
     * 总笔数 Y
     * 单包交易最多400笔
     */
    private String totalNumber ;

    /**
     * 总金额 Y
     * 该批交易的总金额
     */
    private String totalAmount ;

    /**
     * 包号 Y
     *包号必输，后续查询交易情况（EYW5）通过包号来查
     */
    private String packageNo ;

    /**
     */
    private String SAASId ;

    /**
     */
    private String SAASName ;

    /**
     * 同步异步标志 N
     * 0-同步；1-异步
     不送则默认异步
     */
    private String synFlag ;

    private List<SpdbBatchPayItemDTO> batchPayDetails;

    public class SpdbBatchPayItemDTO implements Serializable {

        /**
         * 电子凭证号 Y
         * 是客户ERP系统产生的流水号，建议使用ERP系统的唯一识别号
         * 加上日期前缀，按天唯一
         */
        private String elecChequeNo;

        /**
         * 付款账号 Y
         */
        private String acctNo;

        /**
         * 付款人账户名称 Y
         */
        private String acctName;

        /**
         * 预约日期 N
         * 不输入时表示实时，输入日期为当天时也表示实时
         */
        private String bespeakDate;

        /**
         * 收款人账号 Y
         */
        private String payeeAcctNo;

        /**
         * 收款人名称 Y
         * 必输项，如果payeeAcctNo收款人账号是本行账号，系统将校验收
         * 款人名称的正确性
         */
        private String payeeName;

        /**
         * 收款人账户类型 N
         * 0-对公账号
         * 1-卡
         * 2-活期一本通
         * 3-定期一本通
         * 4-定期存折
         * 5-存单
         * 6-国债
         * 7-外系统账号
         * 8-活期存折
         * 9-内部帐/表外帐（银行内部账户）
         * S-对私内部账号
         * Z-客户号
         * 常用的是上面红字的类型
         * <p>
         * 当收款人为本行个人账户时必须填写此项
         */
        private String payeeType;

        /**
         * 收款行名称 N
         * 当SysFlag=1即跨行转帐时必须输入
         */
        private String payeeBankName;

        /**
         * 支付金额 Y
         */
        private String amount;

        /**
         * 本行/他行标志 Y
         * 0：表示本行
         * 1：表示他行
         */
        private String sysFlag;

        /**
         * 附言 Y
         * 如果跨行转账，附言请不要超过60字节（汉字30个）
         * 不允许存在单引号，双引号，冒号，百分号，竖线
         */
        private String note;

        /**
         * 收款行速选标志 N
         * 1-速选
         * 当本行/他行标志为“1”（他行）时才能生效。
         * 如果希望跨行汇款自动处理，请务必填写此项。
         */
        private String payeeBankSelectFlag;

        /**
         * 收款行行号 N
         * 当sysFlag本行/他行标志为“1”（他行）且速选标志也为1时，收款行行号必须输入。
         * 如果希望跨行汇款自动处理，请务必填写该项，我行公司手机银行提供行号查询服务
         * 当本行/他行标志SysFlag为“1”（他行），收款行行号不能以907开头
         */
        private String payeeBankNo;
        private String payeeAddress;

        /**
         * 支付用途 N
         * 当收款人账户类型为个人时必须输入
         * 1-工资、奖金收入
         * 2-稿费、演出费等劳务费用
         * 3-债券、期货、信托等投资的本金和收益
         * 4-个人债权或产权转让收益
         * 5-个人贷款转存
         * 6-证券交易结算资金和期货交易保证金
         * 7-集成、赠予款项
         * 8-保险理赔、保费退换等款项
         * 9-纳税退还
         * A-农、副、矿产品销售收入
         * B-其他合法款项（转款）
         * C-其他合法款项（货款）
         * D-其他合法款项（采购款）
         * E-其他合法款项（工程款）
         * F-其他合法款项（拨款）
         * G-其他合法款项（借款）
         * H-其他合法款项（还款）
         * I-其他合法款项（补偿款）
         * J-其他合法款项（运费）
         * K-其他合法款项（服务费）
         * L-其他合法款项（广告费）
         * M-其他合法款项（差旅费）
         * N-其他合法款项（物业费）
         * O-其他合法款项（检测费）
         * P-其他合法款项（佣金）
         * Q-其他合法款项（租金）
         * R-其他合法款项（违约金）
         * S-其他合法款项（保证金）
         * T-其他合法款项（退休金）
         * U-其他合法款项（维修基金）
         * V-其他合法款项（维修费）
         * W-其他合法款项（公共事业费）X-其他合法款项（预存话费）
         * Y-其他合法款项（费用报销）
         * Z-其他合法款项（工资）
         * a-其他合法款项（利息）
         * 当收款人为个人时必须输入汇款用途
         */
        private String payPurpose;

        private String detailedContent;
    }

}
