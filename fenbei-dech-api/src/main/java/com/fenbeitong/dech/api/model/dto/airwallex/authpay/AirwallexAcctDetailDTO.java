package com.fenbeitong.dech.api.model.dto.airwallex.authpay;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/07/03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class AirwallexAcctDetailDTO implements Serializable {

	private String awId;
	
	private String nickname;
	
	private String companyId;
	
	private String primaryContactEmail;
	
	private String businessName;
	
}
