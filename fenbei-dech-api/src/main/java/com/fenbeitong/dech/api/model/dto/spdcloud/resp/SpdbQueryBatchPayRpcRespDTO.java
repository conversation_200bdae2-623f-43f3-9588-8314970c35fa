package com.fenbeitong.dech.api.model.dto.spdcloud.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class SpdbQueryBatchPayRpcRespDTO implements Serializable {


    /**
     * 00：待处理
     01：处理中：在网银页面上点击确认后到提交之前或授权前
     02：已处理：网银发核心交易成功
     03：已拒绝：客户取消交易
     04：校验失败：交易内容的格式或逻辑校验未通过或发后台失败
     05：已失效：客户提交交易后未到网银页面处理，过了该笔交易的处理时效
     */
    private String interHandleStatus ;

    private String errMessage ;

    /**
     * 交易总笔数
     */
    private String totalCount ;
    /**
     * 此次查询笔数
     */
    private String count ;


    private List<SpdbBatchPayDetailResDTO> batchPayDetails ;


    public class SpdbBatchPayDetailResDTO implements Serializable{


        /**
         * 电子凭证号
         */
        private String elecChequeNo ;

        /**
         * 交易日期
         */
        private Date transDate ;
        /**
         * 预约日期
         */
        private Date bespeakDate ;
        /**
         * 申请日期
         */
        private Date promiseDate ;
        /**
         * 账号
         */
        private String acctNo ;
        /**
         * 付款人账户名称
         */
        private String acctName ;
        /**
         * 收款人账号
         */
        private String payeeAcctNo ;
        /**
         * 收款人名称
         */
        private String payeeName ;
        /**
         * 收款人账户类型
         */
        private String payeeType ;
        /**
         * 收款行名称
         */
        private String payeeBankName ;
        /**
         * 支付金额
         */
        private String amount ;
        /**
         * 本行它行标志
         */
        private String sysFlag ;
        /**
         * 当明细的交易状态为8-失败拒绝时，在“附言”字段中拼接摘要代码和退票理由。
         当明细的交易状态为9-撤销，则附言字段返回为附言+ "<错误原因:" + " " + 错误信息 + ">"。
         */
        private String note ;
        /**
         * 交易当前状态
         A-业务登记
         0-待补录
         1-待记帐
         2-待复核
         3-待授权
         4-完成
         8-拒绝
         9-撤销
         */
        private String transStatus ;
        /**
         * 交易流水号
         */
        private String seqNo ;
        /**
         * 后台错误信息
         */
        private String reason ;
        /**
         * 后台错误码
         */
        private String abstractCode ;
    }

}
