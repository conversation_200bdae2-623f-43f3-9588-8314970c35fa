package com.fenbeitong.dech.api.model.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class PayResultResDto implements Serializable {
    private static final long serialVersionUID = 3840120900778144855L;

    @ApiModelProperty(value = "支付结果")
    private Boolean payResult;

    @ApiModelProperty(value = "支付结果信息")
    private String payResultMsg;

    public Boolean getPayResult() {
        return payResult;
    }

    public void setPayResult(Boolean payResult) {
        this.payResult = payResult;
    }

    public String getPayResultMsg() {
        return payResultMsg;
    }

    public void setPayResultMsg(String payResultMsg) {
        this.payResultMsg = payResultMsg;
    }
}
