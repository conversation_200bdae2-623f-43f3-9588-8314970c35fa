package com.fenbeitong.dech.api.service.lianlian;

import com.fenbeitong.dech.api.model.dto.lianlian.card.req.*;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.*;
import com.fenbeitong.dech.api.model.dto.lianlian.template.LianLianAccountTemplateRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.template.LianLianAccountTemplateRpcRespDTO;

/**
 * 连连-卡
 */
public interface ILianLianCardService {

    LianLianCreateUserRpcRespDTO createUser(LianLianCreateUserRpcReqDTO reqDTO);
    /**
     * 1.开卡
     */
    LianLianCardApplyRpcRespDTO apply(LianLianCardApplyRpcReqDTO reqDTO);

    /**
     * 2. 更新卡状态
     */
    LianLianCardModifyRpcRespDTO modify(LianLianCardModifyRpcReqDTO lianLianCardModifyRpcReqDTO);
    /**
     * 3. 卡余额调整
     * 用于收到交易通知后设置限额和交易关联
     */
    LianLianCardBalanceAdjustRespDTO balanceAdjust(LianLianCardBalanceAdjustReqDTO lianLianCardBalanceAdjustReqDTO);
    /**
     * 4. 卡详情
     *
     */
    LianLianCardDetailRespDTO detail(LianLianCardDetailReqDTO lianLianCardDetailReqDTO);

    /**
     * 5. 卡限额修改
     */
    LianLianCardLimitRespDTO modifyLimit(LianLianCardLimitReqDTO lianLianCardLimitReqDTO);

    LianLianAccountTemplateRpcRespDTO template(LianLianAccountTemplateRpcReqDTO lianLianAccountTemplateRpcReqDTO);

    LianLianCardVccQueryRespDTO queryVccInfo(LianLianCardVccQueryReqDTO cardVccQueryReqDTO);


    LianLianPhysicalCardApplyRpcRespDTO physicalApply(LianLianPhysicalCardApplyRpcReqDTO lianLianCardApplyRpcReqDTO);

    LianLianPhysicalCardOrderQueryRpcRespDTO physicalApplyOrderQuery(LianLianPhysicalCardOrderQueryRpcReqDTO lianLianPhysicalCardOrderQueryRpcReqDTO);

    LianLianPhysicalCardOrderDetailRpcRespDTO physicalOrderDetail(LianLianPhysicalCardOrderDetailRpcReqDTO lianLianPhysicalCardOrderDetailRpcReqDTO);

    LianLianCaptchaApplyRpcRespDTO captchaApply(LianLianCaptchaApplyRpcReqDTO lianLianCaptchaApplyRpcReqDTO);

    LianLianPhysicalCardActiveRpcRespDTO applyPhysicalCardActive(LianLianPhysicalCardActiveRpcReqDTO lianLianPhysicalCardActiveRpcReqDTO);

    LianLianPhysicalCardResetPinRpcRespDTO applyPhysicalResetPin(LianLianPhysicalResetPinRpcReqDTO lianLianPhysicalResetPinRpcReqDTO);
}
