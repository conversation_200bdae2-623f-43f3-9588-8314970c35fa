package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName IndividualAccountStatusInfoDto
 * @Description: 个人户开户状态查询
 * <AUTHOR>
 * @Date 2021/3/8
 **/
@Data
public class IndividualAccountStatusInfoDto implements Serializable {


    /**
     * 开户状态
     */
    private String accountStatus;

    /**
     * 绑定账户 ID
     */
    private String bandAccountId;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 银行二类户卡号
     */
    private String bankAccountNo;

    /**
     * 银行虚户ID
     */
    private String bankAcctId;

    /**
     * 银行流水号
     */
    private String sysOrdNo;

    /**
     * 平台单号
     */
    private String ordNo;

    private Boolean retry;

}
