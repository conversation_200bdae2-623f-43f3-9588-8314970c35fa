package com.fenbeitong.dech.api.model.dto;

import com.luastar.swift.base.utils.ObjUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName SpaBankTradeBaseDto
 * @Description: 平安交易公共参数
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SpaBankTradeRespBaseDto implements Serializable {

    private String code;

    private String message;

    private SpaBankTradeRespDataDto data;

    public static SpaBankTradeRespBaseDto fail(String code,String message){
        if(ObjUtils.isBlank(code)){
            code = "********";
        }
        SpaBankTradeRespBaseDto respDto = SpaBankTradeRespBaseDto.builder().code(code)
                .message(message)
                .build();
        return respDto;
    }

    public static SpaBankTradeRespBaseDto success(SpaBankTradeRespDataDto respDataDto){
        SpaBankTradeRespBaseDto respDto = SpaBankTradeRespBaseDto.builder().code("000000")
                .message("success")
                .data(respDataDto)
                .build();
        return respDto;
    }

}
