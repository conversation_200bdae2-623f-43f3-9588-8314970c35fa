package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @Author: liyi
 * @Date: 2022/10/21 4:18 PM
 */
@Data
public class BankCardQueryTradeReqDTO implements Serializable {
    /**
     * 银行名
     */
    @NotNull
    private String bankName;

    /**
     * 交易单号
     */
    private String txnId;

    /**
     *  银行订单号
     */
    private String bankTransNo;

    /**
     * 交易类型
     * @see com.fenbeitong.dech.api.enums.BankCardTradeTypeEnum
     */
    private Integer tradeType;

}
