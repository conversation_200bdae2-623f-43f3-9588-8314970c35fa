package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@SuppressWarnings("serial")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateCompanyAccountRespDto implements Serializable {

    private String bankNo;

    private String bankName;

    private String ordNo;

    private Boolean success;

    private String failReason;

    public static CreateCompanyAccountRespDto ofSuccess(String bankNo,String bankName,String ordNo){
    	return CreateCompanyAccountRespDto.builder().success(Boolean.TRUE)
                .bankNo(bankNo)
                .bankName(bankName)
                .ordNo(ordNo)
                .build();
    }

    public static CreateCompanyAccountRespDto ofFail(String bankNo,String bankName,String ordNo,String failReason){
    	return CreateCompanyAccountRespDto.builder().success(Boolean.FALSE)
                .bankNo(bankNo)
                .bankName(bankName)
                .ordNo(ordNo)
                .failReason(failReason)
                .build();
    }

    public static CreateCompanyAccountRespDto ofFail(String bankName,String ordNo,String failReason){
    	return CreateCompanyAccountRespDto.builder().success(Boolean.FALSE)
                .bankNo("")
                .bankName(bankName)
                .ordNo(ordNo)
                .failReason(failReason)
                .build();
    }
}