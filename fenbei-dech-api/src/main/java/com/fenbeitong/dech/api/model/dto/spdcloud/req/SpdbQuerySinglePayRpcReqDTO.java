package com.fenbeitong.dech.api.model.dto.spdcloud.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class SpdbQuerySinglePayRpcReqDTO implements Serializable{

    /**
     * 统一社会信用代码  Y
     *通过统一社会信用代码来区分客户
     */
    private String unfSocCrdtNo ;

    /**
     * 电子凭证号  Y
     *是企业发起该笔业务在ERP系统产生的流水号
     如果客户在EYW1中填写了电子凭证号，则可根据电子凭证号查询对应的业务。
     */
    private String elecChequeNo ;

    /**
     * 账号  Y
     *需要查询的企业账户。
     */
    private String acctNo ;

    /**
     * 交易申请渠道  N
     *为空即查所有
     银企直联-YQZL
     公司网银-DGWY
     公司手机-GSSJ
     小微网银-WXXY
     小微手机-XWSJ
     企业财资-QYCZ
     存管系统- CGXT
     */
    private String channel ;

    /**
     * 收款人账号  N
     *为空即查所有
     */
    private String payeeAcctNo ;

    /**
     * 收款行行号  N
     *为空即查所有
     */
    private String payeeBankNo ;

    /**
     * 本行他行标志  N
     *0：本行
     1：他行
     为空即查所有
     */
    private String sysFlag ;

    /**
     * 受理状态  N
     *新增字段。
     A：业务登记
     0：待补录
     1：待记账
     2：待处理
     3：待授权
     4：受理完成
     8：受理失败
     9：撤销
     为空即查所有
     */
    private String transStatus ;

    /**
     * 二级平台ID  N
     */
    private String SAASId ;

    /**
     * 二级平台名称  Y
     */
    private String SAASName ;

}
