package com.fenbeitong.dech.api.model.dto.lianlian.file;

import lombok.Data;

import java.io.Serializable;

@Data
public class LianLianFileUploadRpcReqDTO implements Serializable {
    private String txnTime;
    private String txnSeqno;

    private String userNo;

    private String fileType;

    private String fileContext;

    private String businessInfo;

    private String timestamp;
    /**
     * 内容类型,
     * UBO_IMAGE 受益所有人文件;
     * USER_IMAGE 用户相关影印文件;
     * JIEHUIBAO_MATERIALS 结汇宝证明材料;
     * SUPPLEMENT_CSV 补单对账CSV文件 ;
     * TRADE_MATERIAL 贸易材料
     */
    private String contextType;
}
