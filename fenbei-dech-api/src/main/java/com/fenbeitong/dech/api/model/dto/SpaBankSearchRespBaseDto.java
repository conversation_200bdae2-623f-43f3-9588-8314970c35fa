package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName SpaBankSearchRespBaseDto
 * @Description: 平安交易公共参数
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SpaBankSearchRespBaseDto implements Serializable {

    private String code;

    private String message;

    private BankQueryTradeRespDto data;

    public static SpaBankSearchRespBaseDto fail(String code, String message){
        SpaBankSearchRespBaseDto respDto = SpaBankSearchRespBaseDto.builder().code(code)
                .message(message)
                .build();
        return respDto;
    }

    public static SpaBankSearchRespBaseDto success(BankQueryTradeRespDto respDataDto){
        SpaBankSearchRespBaseDto respDto = SpaBankSearchRespBaseDto.builder().code("000000")
                .message("success")
                .data(respDataDto)
                .build();
        return respDto;
    }

}
