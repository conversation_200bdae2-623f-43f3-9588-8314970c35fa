package com.fenbeitong.dech.api.service;


import com.fenbeitong.dech.api.model.dto.*;

import java.util.Date;

/**
 * @ClassName ISpaBankSearchService
 * @Description: 平安银行查询
 * <AUTHOR>
 * @Date 2022/1/4
 **/
public interface ISpaBankSearchService {

    /*
     * @MethodName: queryTransferInfo
     * @Description: 查询转账状态（付款3、退款1）
     * @Param: [bassTxnId, bankName]
     * @Return: com.fenbeitong.dech.api.model.dto.BankQueryTradeRespDto
     * @Author: Jarvis.li
     * @Date: 2022/1/7
    **/
    SpaBankSearchRespBaseDto queryTransferInfo(String bassTxnId, String bankName);

    /*
     * @MethodName: queryReturnRemittanceDetails
     * @Description: 查询退票信息
     * @Param: [reqDTO]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankReturnRemittanceRespBaseDto
     * @Author: Jarvis.li
     * @Date: 2022/1/21
    **/
    SpaBankReturnRemittanceRespBaseDto queryReturnRemittanceDetails(SpaBankReturnRemittanceDetailsReqDTO reqDTO);

    /*
     * @MethodName: queryMemberTradeInfo
     * @Description: 查询交易状态（付款1、退款3）
     * @Param: [bassTxnId, bankName]
     * @Return: com.fenbeitong.dech.api.model.dto.BankQueryTradeRespDto
     * @Author: Jarvis.li
     * @Date: 2022/1/7
     **/
    SpaBankSearchRespBaseDto queryMemberTradeInfo(BankQueryTradeReqDto reqDto);

    /*
     * @MethodName: queryCashOutInfo
     * @Description: 查询提现状态（付款2）
     * @Param: [bassTxnId, bankName]
     * @Return: com.fenbeitong.dech.api.model.dto.BankQueryTradeRespDto
     * @Author: Jarvis.li
     * @Date: 2022/1/7
     **/
    SpaBankSearchRespBaseDto queryCashOutInfo(BankQueryTradeReqDto reqDto);

    /**
     * 平安对公付处理电子回单
     */
    void spaPublicReceiptProcess(Date date);

    /*
     * @MethodName: queryHistoryBalance
     * @Description: 查询历史余额
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaBankHistoryBalanceRespDto
     * @Author: Jarvis.li
     * @Date: 2022/3/4
    **/
    SpaBankHistoryBalanceRespDto queryHistoryBalance(SpaBankHistoryBalanceReqDto reqDto);

    /**
     * 查询功能子账户余额
     * 2：挂账子账号   13：营销子账户
     * @param fctId
     * @param bankName
     * @return
     */
    BankAcctBookRespDto queryFunctionAcctBook(String fctId, String bankName, String subAcctProperty);

}
