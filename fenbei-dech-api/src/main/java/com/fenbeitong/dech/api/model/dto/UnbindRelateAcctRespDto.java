package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-09-16 11:04:26
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnbindRelateAcctRespDto implements Serializable {
    /**
     * 是否成功： true：成功  false:失败
     */
    private Boolean isSuccess;
    /**
     * 失败原因
     */
    private String failReason;
}
