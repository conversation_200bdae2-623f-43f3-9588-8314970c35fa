package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName SpaBankRefundToMemberAcctReqDto
 * @Description: 退款3：平台过渡户到企业会员子账户
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
public class SpaBankRefundToMemberAcctReqDto extends SpaBankTradeReqBaseDto {

    /**
     * 退款3必填
     * 退款3：收款账户名（资金汇总账户名）
     */
    @NotNull
    private String receiveAccountName;

    /**
     * 退款3必填
     * 退款3：付款1返回的上账流水号
     */
    @NotNull
    private String oldTxnId;
}
