package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SuspendAccountRefundFundReqDto extends BankTradeBaseReqDto {

    /**
     * 原单号
     */
    private String originalOrderNo;

    /**
     * 交易费用(分)
     */
    private BigDecimal tradeFee;

    /**
     * 收款方银行名称
     */
    private String receiveBankName;

    /**
     * 收款方银行编码
     */
    private String receiveBankCode;
}
