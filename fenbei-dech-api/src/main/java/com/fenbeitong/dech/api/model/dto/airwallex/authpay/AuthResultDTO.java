package com.fenbeitong.dech.api.model.dto.airwallex.authpay;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class AuthResultDTO implements Serializable {

	private String companyId;
	
	/**
	 * 授权状态：true 已授权 false 未授权
	 */
	private boolean authorized;
	
	/**
	 * 来自Airwallex的账户id
	 */
	private String accountId;
	
	/**
	 * 状态：0、未授权 1、已授权 2、授权已过期 3、再次授权
	 */
	private int status;
	
	/**
	 * 授权账户名称
	 */
	private String accountName;
	
	/**
	 * 授权账户号
	 */
	private String accountNumber;
}
