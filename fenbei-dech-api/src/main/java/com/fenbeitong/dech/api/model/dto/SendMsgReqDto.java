package com.fenbeitong.dech.api.model.dto;

import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;

import java.io.Serializable;

/*
 发送短信验证码
**/
@Data
public class SendMsgReqDto implements Serializable {

    /**
     * 	银行名称
     */
    private String bankName;

    /**
     * 	手机号
     */
    private String phoneNumber;


    // ================廊坊银行=================
    /**
     * 	发送类型
     * 	开户 1 提现 2 冻结/解冻 3
     */
    private Integer sendType;

    /**
     * 	账户号
     * 	提现必填
     */
    private String accountNo;

    /**
     * 		持卡人姓名
     * 		提现必填
     */
    private String userName;

    /**
     * 	 卡号
     * 	 提现必填
     */
    private String cardNumber;

    // ================广发银行=================
    /**
     * 业务类型
     */
    private String busiType;

    /**
     * 用途
     */
    private String purpose;

    public Boolean checkParam(){
        Boolean result = true;
        String bankName = getBankName();
        if(ObjUtils.isBlank(bankName)) {
            return false;
        }
        switch (bankName){
            case "ZBBANK":
                if(ObjUtils.isBlank(getPhoneNumber())){
                    result = false;
                }
                break;
            case "CGB":
                if(ObjUtils.isBlank(getBusiType()) || ObjUtils.isBlank(getPhoneNumber()) || ObjUtils.isBlank(getPurpose())){
                    result = false;
                }
                break;
            case "LFBANK":
                if(ObjUtils.isBlank(getSendType())){
                    result = false;
                }else {
                    if(getSendType().equals(1)){
                        if(ObjUtils.isBlank(getPhoneNumber())){
                            result = false;
                        }
                    } else if (getSendType().equals(3)){
                        if(ObjUtils.isBlank(getPhoneNumber()) || ObjUtils.isBlank(getAccountNo())){
                            result = false;
                        }
                    } else {
                        if(ObjUtils.isBlank(getAccountNo()) || ObjUtils.isBlank(getPhoneNumber()) || ObjUtils.isBlank(getUserName()) || ObjUtils.isBlank(getCardNumber())){
                            result = false;
                        }
                    }
                }
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + this.bankName);
        }
        return result;
    }
}
