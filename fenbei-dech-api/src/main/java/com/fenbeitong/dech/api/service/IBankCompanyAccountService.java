package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;
import com.fenbeitong.dech.api.model.dto.vo.CompanyAccountStatusInfoDto;
import com.fenbeitong.dech.api.model.dto.SendMsgRespDto;

/**
 * <AUTHOR>
 * @Date 2021/2/19
 * @Description
 */
public interface IBankCompanyAccountService {


   /**
    * 创建企业账户
    * @param companyAccountRpcDto
    * @return
    */
   CreateCompanyAccountRespDto createCompanyAccount(CreateCompanyAccountRpcDto companyAccountRpcDto);


   /**
    * 众邦文件上传
    * @param fileUrl
    * @param billType 影像类型
    * @return
    */
   String zbUploadFile(String fileUrl,String billType);


   /**
    * 打款金额验证
    * @param reqDto
    * @return
    */
   PaymentAmountCheckRespDto paymentCheck(PaymentAmountCheckReqDto reqDto);


    /**
     * 重新打款
     * @param reqDto
     * @return
     */
    PaymentCheckTryRespDto paymentCheckTry(PaymentCheckTryReqDto reqDto);


   /**
    * 查询企业开户状态状态
    * @param reqDTO
    * @return
    */
   CompanyAccountStatusInfoDto queryCompanyAccountStatus(QueryCompanyAccountStatusReqDTO reqDTO);


    /**
     * 修改企业账户信息
     * @param companyAccountRpcDto
     * @return
     */
    CreateCompanyAccountRespDto updateCompanyAccount(UpdateCompanyAccountRpcDto companyAccountRpcDto);

    /*
     * @MethodName: sendMsg
     * @Description: 公共发送短信验证码
     * @Param: [reqDto]
     * @Return: com.fenbeitong.dech.api.model.dto.SendMsgRespDto
     * @Author: Jarvis.li
     * @Date: 2022/6/10
    **/
    SendMsgRespDto sendMsg(SendMsgReqDto reqDto);
}
