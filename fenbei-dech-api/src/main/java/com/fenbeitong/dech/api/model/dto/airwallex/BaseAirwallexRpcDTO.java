package com.fenbeitong.dech.api.model.dto.airwallex;

import com.fenbeitong.dech.api.model.dto.base.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by FBT on 2023/3/29.
 */
@Data
public class BaseAirwallexRpcDTO extends BaseModel {

    /**
     * 个人持卡人的详细信息。
     */
    @Data
    public static class Individual extends BaseModel {

        /**
         * 出生日期
         */
        private String date_of_birth;

        /**
         * 通过将此字段设置为 yes 您确认您已明确同意Airwallex根据以下条款向Airwallex服务提供商和数据库所有者验证其身份： 身份验证条款.
         */
        private String express_consent_obtained;

        private Identification identification;

        private Name name;


    }


    /**
     * 用于验证个人身份的身份证明形式。
     */
    @Data
    public static class Identification extends BaseModel {

        /**
         * 识别文件的ISO国家代码。
         */
        private String country;

        /**
         * 给定文档背面图像的ID。这可以通过将文件上载到API中的文件上载端点来检索
         */
        private String document_back_file_id;

        /**
         * 给定文档正面图像的ID。这可以通过将文件上传到API内的文件上传端点来检索。
         */
        private String document_front_file_id;

        /**
         * 提供的身份证明文件的有效期，格式为YYYY-MM-DD。
         */
        private String expiry_date;

        /**
         * 英国驾照上注明的性别。其他标识类型不需要。 M, F
         */
        private String gender;

        /**
         * 证件号码。
         */
        private String number;

        /**
         * 说明文件的签发地点。仅当类型为DRIVERS_LICENSE且国家/地区为Au时才需要。
         */
        private String state;

        /**
         * 提供的标识类型。可以是PASSPORT、DRIVERS_LICENSE或ID_CARD。
         */
        private String type;


    }

    /**
     * 姓名。
     */
    @Data
    public static class Name extends BaseModel {
        /**
         * 名
         */
        private String first_name;
        /**
         * 姓
         */
        private String last_name;

        /**
         * 中间名
         */
        private String middle_name;

        /**
         * 持卡人的可选头衔
         */
        private String title;
    }


    /**
     * 地址
     */
    @Data
    public static class Address extends BaseModel {

        /**
         * City of address地址城市
         */
        private String city;

        /**
         * ISO country code of addressISO国家地址代码
         */
        private String country;

        /**
         * 地址行1
         */
        private String line1;

        /**
         * 地址行1
         */
        private String line2;

        /**
         * 地址邮编或邮政编码
         */
        private String postcode;

        /**
         * 地址州或地区
         */
        private String state;

    }

    /**
     * 授权控制类
     */
    @Data
    public static class AuthorizationControls extends BaseModel {
        private String active_from;
        private String active_to;
        private List<String> allowed_currencies;
        private List<String> allowed_merchant_categories;
        private String allowed_transaction_count;
        private TransactionLimits transaction_limits;
    }

    @Data
    public static class TransactionLimits extends BaseModel {
        private String currency;
        private List<Limit> limits;
    }

    @Data
    public static class Limit extends BaseModel {
        private Long amount;
        private String interval;
    }


    /**
     * 主要联系人详细信息
     */
    @Data
    public static class PrimaryContactDetails extends BaseModel {
        private String email;
        private String full_name;
        private String mobile_number;
    }

}
