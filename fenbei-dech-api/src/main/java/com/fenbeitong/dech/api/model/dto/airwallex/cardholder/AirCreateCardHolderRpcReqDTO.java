package com.fenbeitong.dech.api.model.dto.airwallex.cardholder;

import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Created by FBT on 2023/3/29.
 */
@Data
public class AirCreateCardHolderRpcReqDTO implements Serializable {

    private static final long serialVersionUID = -564280357800467520L;

    private String email;

    private String mobile_number;

    private BaseAirwallexRpcDTO.Individual individual;

    /**
     * 地址
     */
    private BaseAirwallexRpcDTO.Address address;

    /**
     * 邮寄地址
     */
    private BaseAirwallexRpcDTO.Address postalAddress;

    public interface Type{
        String ID_CARD = "ID_CARD";

        String PASSPORT = "PASSPORT";

        String DRIVERS_LICENSE = "DRIVERS_LICENSE";
    }

}
