package com.fenbeitong.dech.api.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ZsOrderQueryResDto extends UnionPayBaseRespDto implements Serializable {
    private static final long serialVersionUID = 8624162050724796411L;

    @ApiModelProperty(value = "交易序列号")
    private String bankTxnNo;

    @ApiModelProperty(value = "订单号")
    private String bankOrderNo;

    @ApiModelProperty(value = "订单类型")
    private Integer payType;

    @ApiModelProperty(value = "支付有效时间")
    private String paymentValidTime;

    @ApiModelProperty(value = "收款方信息")
    private String receiverInfo;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "交易币种")
    private String currency;

    @ApiModelProperty(value = "收款方备注")
    private String receiverRemark;
}
