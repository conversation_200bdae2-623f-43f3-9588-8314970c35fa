package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName SpaBankToInterAcctReqDto
 * @Description: 付款2：平台过渡户到平安易内部户
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
public class SpaBankToInterAcctReqDto extends SpaBankTradeReqBaseDto {

    /**
     * 付款2必填
     * 付款2：收款账户名（平安易内部户）
     */
    @NotNull
    private String receiveAccountName;
}
