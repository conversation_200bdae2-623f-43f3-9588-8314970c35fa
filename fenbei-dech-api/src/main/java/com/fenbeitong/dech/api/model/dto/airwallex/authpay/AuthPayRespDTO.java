package com.fenbeitong.dech.api.model.dto.airwallex.authpay;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class AuthPayRespDTO implements Serializable {

	/**
	 * 请求流水号 长度 <= 50字符
	 */
	private String requestId;
	
	/**
	 * 用于定位账户
	 */
	private String companyId;
	
	/**
	 * 支付金额，单位为分
	 */
	private BigDecimal amount;
	
	/**
	 * 选填，如果为空那么默认为人民币
	 */
	private String currency;
	
	/**
	 * 交易状态 @see AuthPayStatusEnum
	 */
	private String status;
	
	/**
	 * 支付业务类型：1、同步付款 2、异步付款 3、退款
	 */
	private Integer type;
	
	/**
	 * 第三方交易流水号
	 */
	private String bankTransNo;
	
	/**
	 * 第三方交易时间
	 */
	private String bankTransTime;
}
