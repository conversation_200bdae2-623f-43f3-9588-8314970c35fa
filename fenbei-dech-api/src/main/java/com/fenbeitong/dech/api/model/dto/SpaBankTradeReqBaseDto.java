package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName SpaBankTradeReqBaseDto
 * @Description: 平安交易公共参数
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
public class SpaBankTradeReqBaseDto implements Serializable {

    /**
     * 银行名称 - SPABANK
     * @see com.fenbeitong.finhub.common.constant.BankNameEnum
     */
    @NotNull
    private String bankName;

    /**
     * 操作金额（分）
     */
    @NotNull
    private BigDecimal operationAmount;

    /**
     * 订单流水号
     */
    @NotNull
    private String txnId;

    /**
     * 付款1：付款方账号（企业账户号）
     * 付款2：付款方账号（平台过渡户）
     * 付款3：付款方账号（平安易内部户）
     */
    @NotNull
    private String payAccountNo;

    /**
     * 付款1：收款账号（平安平台过渡户）
     * 付款2：收款账号（平安易内部户）
     * 付款3：收款账号（企业对公户）
     */
    @NotNull
    private String receiveAccountNo;

    /**
     * 标识  1. 企业  2. 个人
     * @see com.fenbeitong.dech.api.enums.CustomerTypeEnum
     */
    @NotNull
    private Integer customerType;

    /**
     * 对公付款全局订单号
     */
    @NotNull
    private String publicOrderNo;

    // 账户类型 虚拟卡个人账户 21 报销卡个人账户20
    private Integer accountSubType;

}
