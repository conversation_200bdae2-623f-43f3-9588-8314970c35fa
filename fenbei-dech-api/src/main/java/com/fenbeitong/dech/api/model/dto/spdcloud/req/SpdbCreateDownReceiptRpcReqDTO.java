package com.fenbeitong.dech.api.model.dto.spdcloud.req;


import lombok.Data;

import java.io.Serializable;


@Data
public class SpdbCreateDownReceiptRpcReqDTO implements Serializable {

    /**
     *统一社会信用代码
     */
    private String unfSocCrdtNo ;

    private String acctNo;

    /**
     *本交易得到成功返回后，请发起WJC7交易获取结果文件
     */
    private String billDownloadChanel ;
    /**
     *单笔批量标志
     */
    private String singleOrBatchFlag ;
    /**
     *起始笔数  单笔批量标志为1时必输
     */
    private String beginNumber ;
    /**
     * 查询笔数   单笔批量标志为1时必输，不超过10笔，超过10默认送10
     */
    private String queryNumber ;
    /**
     * 交易码
     */
    private String businessCode ;
    /**
     * 柜员流水号
     */
    private String backhostGyno ;
    /**
     * 借贷标记
     */
    private String debitFlag ;

    /**
     * 传票组内序号
     */
    private String subpoenaSeqNo ;

    /**
     * 对方行号
     */
    private String oppositeBankNo ;

    /**
     * 对方行名
     */
    private String oppositeBankName ;

    /**
     * 对方帐号
     */
    private String oppositeAcctNo ;

    /**
     * 对方户名
     */
    private String oppositeAcctName ;

    /**
     * 起始日期
     */
    private String beginDate ;

    /**
     * 终止日期
     */
    private String endDate ;

    /**
     * 最小金额
     */
    private String mixAmount ;

    /**
     * 最大金额
     */
    private String maxAmount ;

    /**
     * 正序/倒序输出
     */
    private String ascOrDes ;

    /**
     * 摘要
     */
    private String summary ;

    /**
     * 备注
     */
    private String remark ;

    /**
     *二级平台ID
     */
    private String SAASId ;

    /**
     *二级平台名称
     */
    private String SAASName ;

    /**
     *备用字段1
     */
    private String reserve1 ;



}
