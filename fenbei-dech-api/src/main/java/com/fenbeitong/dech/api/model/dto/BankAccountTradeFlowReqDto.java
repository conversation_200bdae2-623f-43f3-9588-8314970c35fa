package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/2/27
 * @Description
 */
@Data
public class BankAccountTradeFlowReqDto implements Serializable {


    /**
     * 企业id
     */
    private String companyId;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 每页条数
     **/
    private Integer pageSize = 10;

    /**
     * 账户(银行返回的 不是卡号)
     */
    private String accountNo;

    /**
     * 系统订单号(不是fbOrderId 是银行返回)
     */
    private String sysOrdNo;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
