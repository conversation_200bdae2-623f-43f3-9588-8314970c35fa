package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.cgb.*;

public interface ICgbSearchService {

    /**
     * 账簿日终余额查询
     *
     * @param reqDTO
     * @return
     */
    CgbEndBalanceRespDTO endBalanceQuery(CgbEndBalanceReqDTO reqDTO);

    /**
     * 账簿交易明细查询(分页查询)
     *
     * @param reqDTO
     * @return
     */
    CgbQueryAccountInfoRespDTO queryAccountInfo(CgbQueryAccountInfoReqDTO reqDTO);

    /**
     * 专户交易明细查询（分页）
     *
     * @param reqDTO
     * @return
     */
    CgbManagerTransferRespDTO queryManagerTransfer(CgbManagerTransferReqDTO reqDTO);

    /**
     * 接口描述：查询实体专用对公户余额信息。
     * @param merchantAccountBalanceQueryReqDTO
     * @return
     */
    CgbMerchantAccountBalanceQueryRespDTO merchantAccountBalanceQuery(CgbMerchantAccountBalanceQueryReqDTO merchantAccountBalanceQueryReqDTO);


}
