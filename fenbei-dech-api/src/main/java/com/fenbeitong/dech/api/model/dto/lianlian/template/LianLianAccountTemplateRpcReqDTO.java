package com.fenbeitong.dech.api.model.dto.lianlian.template;

import lombok.Data;

import java.io.Serializable;
@Data
public class LianLianAccountTemplateRpcReqDTO implements Serializable {
    /**
     * 商户ID
     */
    private String mchId;
    /**
     * 卡模版类型
     * YQX 易企行卡
     * YQY 易权益卡
     * YQG 易企购卡
     * LLCZ 标准卡
     *
     */
    private String templateType;

    private String templateId;
    /**
     * 受理范围
     * 可选
     * 枚举值:
     * DOMESTIC 境内卡
     * OFFSHORE 境外卡
     */
    private String acceptScope;
    /**
     * 额度自动恢复方式
     * 可选
     * 枚举值:
     * NONE 不自动恢复
     * WRITE_OFF 核销后恢复
     */
    private String quotaRecovery;
}
