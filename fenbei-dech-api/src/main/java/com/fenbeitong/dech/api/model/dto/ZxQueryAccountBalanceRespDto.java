package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName ZxQueryAccountBalanceRespDto
 * @Description: 中信-余额查询响应DTO
 * <AUTHOR>
 * @Date 2021/4/8
 **/
@Data
public class ZxQueryAccountBalanceRespDto implements Serializable {

    /**
     * 资金分簿编号
     */
    private String subAccNo;

    /**
     * 资金分簿名称
     */
    private String subAccName;


    /*
    透支额度
     */
    private BigDecimal overdraftAmount;

    /*
    实体账户可用资金
     */
    private BigDecimal availableAmount;

    /*
    可用余额
     */
    private BigDecimal availableBalance;

    /*
    实际余额
     */
    private BigDecimal actualBalance;

    /*
    冻结金额
     */
    private BigDecimal frozenAmount;
}
