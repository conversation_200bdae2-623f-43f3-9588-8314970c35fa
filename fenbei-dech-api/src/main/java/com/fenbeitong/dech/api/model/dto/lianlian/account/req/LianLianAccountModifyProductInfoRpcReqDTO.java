package com.fenbeitong.dech.api.model.dto.lianlian.account.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LianLianAccountModifyProductInfoRpcReqDTO implements Serializable {

    /**
     * 请求单号
     */
    private String txnSeqno;
    /**
     * 结果通知地址
     */
    private String notifyUrl;
    /**
     * 商户号
     */
    @JSONField(name = "mch_id")
    private String mchid;
    /**
     * 产品信息，产品信息
     */
    private List<LianLianProductInfo> productInfos;
    /**
     * 协议信息，协议信息
     */
    private LianLianProtocolInfo protocolInfo;
    /**
     * 经营场景信息，目前只支持全量修改（全量修改包括新增或编辑经营场景）
     */
    private LianLianSalesInfos salesInfos;
    /**
     * 结算信息，结算信息 只支持修改不支持新增多个结算卡
     */
    private LianLianSettleInfo settleInfo;

}
