package com.fenbeitong.dech.api.model.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ZsPayReqDto implements Serializable {
    private static final long serialVersionUID = 18765408730697697L;

    @ApiModelProperty(value = "交易序列号")
    private String txnNo;

    @ApiModelProperty(value = "交易金额")
    private String txnAmt;

    @ApiModelProperty(value = "银行卡号或者token")
    private String accNo;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "付款备注")
    private String payerRemark;

    @ApiModelProperty(value = "付款方姓名")
    private String name;

    @ApiModelProperty(value = "风控信息")
    private Map<String, String> riskInfo;

    //营销信息（目前不用）
    private String couponInfo;

    //初始交易金额，有营销信息时必传
    private String origTxnAmt;
}
