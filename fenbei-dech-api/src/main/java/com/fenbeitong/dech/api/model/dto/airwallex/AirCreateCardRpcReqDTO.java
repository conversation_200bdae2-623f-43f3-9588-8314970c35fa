package com.fenbeitong.dech.api.model.dto.airwallex;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by yang.Li on 2023/3/29.
 */
@Data
public class AirCreateCardRpcReqDTO implements Serializable {

    //在有问题时激活
    private Boolean activate_on_issue;
    //授权控制
    private BaseAirwallexRpcDTO.AuthorizationControls authorization_controls;
    //持卡人ID
    private String cardholder_id;
    //客户端数据
    private String client_data;
    //创建者
    private String created_by;
    //形状因子
    private String form_factor;
    //发出
    private String issue_to;
    //昵称
    private String nick_name;
    //根据卡申请存储的注释（供客户参考）
    private String note;
    //地址
    private BaseAirwallexRpcDTO.Address postal_address;
    //主要联系人详细信息
    private BaseAirwallexRpcDTO.PrimaryContactDetails primary_contact_details;
    //目的用途
    private String purpose;
    //请求标识
    private String request_id;
    //类型
    private String type;

}
