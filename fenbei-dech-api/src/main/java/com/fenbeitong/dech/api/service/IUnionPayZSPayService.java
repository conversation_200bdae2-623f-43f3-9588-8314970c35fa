package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @Description: 银联云闪付-主扫
 * @Author: liyi
 * @Date: 2022/4/11 下午4:04
 */
public interface IUnionPayZSPayService {

    ZsOrderQueryResDto getOrder(ZsOrderQueryReqDto zsOrderQueryReqDto);

    ZsPayResDto pay(ZsPayReqDto payDto);

    UnionPayBaseRespDto getPayResult(String txnNo);

    /**
     * 云闪付签名验证
     */
    Boolean verifySign(Map<String, String> dataMap);

    String packageRespData(String respCode, String respMsg);
}
