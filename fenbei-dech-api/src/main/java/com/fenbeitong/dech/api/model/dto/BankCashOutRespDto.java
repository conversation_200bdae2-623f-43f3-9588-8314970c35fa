package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/26
 * @Description
 */
@Data
public class BankCashOutRespDto implements Serializable {

    /**
     * 交易状态
     */
    private String txnStatus;

    /**
     * 订单号
     */
    private String fbOrderId;

    /**
     * 账户
     */
    private String accountNo;

    /**
     * 银行订单号
     */
    private String sysOrdNo;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 对接银行的流水号
     */
    private String syncBankTransNo;
}
