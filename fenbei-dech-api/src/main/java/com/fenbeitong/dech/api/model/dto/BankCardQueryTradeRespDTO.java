package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description:
 * @Author: liyi
 * @Date: 2022/10/21 4:15 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankCardQueryTradeRespDTO implements Serializable {
    /**
     * 银行流水号
     */
    private String bankTransNo;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 失败原因
     */
    private String failedReason;
}
