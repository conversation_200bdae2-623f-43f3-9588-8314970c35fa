package com.fenbeitong.dech.api.model.dto;

import com.fenbeitong.dech.api.model.dto.vo.ZBAgentProtocolVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/2/3
 * @Description 创建企业账户
 */
@Data
public class UpdateCompanyAccountRpcDto extends CreateCompanyAccountRpcDto {

    /**
     * 修改账户编号
     */
    private String accountNo;

    /**
     * 原联系人手机号
     */
    private String oldContactMobile;

    /**
     * 变更类型  空、0：变更账簿联系人手机号   1：变更企业名称或法人名称  2：变更税金入金账户  3：变更账簿联系人证件信息
     */
    private String transType;

    /**
     * 众邦经办人修改-授权书协议协议信息
     */
    private ZBAgentProtocolVo zBAgentProtocolVo;
    /**
     * 加一段丑陋的代码
     */
    private ZBAgentProtocolVo protocol;
}
