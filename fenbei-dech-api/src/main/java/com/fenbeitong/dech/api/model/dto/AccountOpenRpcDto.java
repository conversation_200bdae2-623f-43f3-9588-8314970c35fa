package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName AccountOpenRpcDto
 * @Description: 虚拟卡开户RPC请求STO
 * <AUTHOR>
 * @Date 2021/3/8
 **/
@Data
public class AccountOpenRpcDto implements Serializable {

    /**
     * 银行注册成功流水号
     */
    private String sysOrdNo;

    /**
    手机号
     */
    private String phoneNum;

    /**
    绑定账户信息
     */
    private BankAccountInfo bankAccountInfo;

    /**
    短信验证码
     */
    private String smsVerificationCode;

    /**
    交易授权码
     */
    private String businessAuthCode;


    @Data
    public class BankAccountInfo implements Serializable{
        /**
         * 开户行行号
         */
        private String openAcctBankNum;

        /**
         * 银行编码
         */
        private String openAcctBankCode;

        /**
         * 开户银行
         */
        private String bankName;

        /**
         * 开户银行联行号
         */
        private String bankBrnNo;

        /**
         * 账号
         */
        private String bankAccountNo;

    }

    public BankAccountInfo createBankAccountInfo(){
        BankAccountInfo bankAccountInfo = new BankAccountInfo();
        return bankAccountInfo;
    }
}