package com.fenbeitong.dech.api.model.dto.lianlian.account.resp;

import com.fenbeitong.dech.api.model.dto.lianlian.LianLianBaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LianLianAccountApplyResultQueryRpcRespDTO extends LianLianBaseDTO {
    /**
     * 请求流水号
     */
    private String txnSeqno;
    /**
     * 商户ID
     */
    private String mchId;
    /**
     * 进件状态
     * 必需
     * APPLY已受理； REJECTED 驳回；SUCCESS进件成功； REFUSED进件失败
     */
    private String status;
    /**
     * 进件状态
     * 必需
     * APPLY已受理； REJECTED 驳回；SUCCESS进件成功； REFUSED进件失败
     */
    private String failReason;
    /**
     * 微信子商户号
     * 可选
     * 多个子商户号以逗号分隔
     */
    private String wxSubMchid;
    /**
     * 支付宝子商户号
     * 可选
     * 多个子商户号以逗号分隔
     */
    private String aliSubMchid;
    /**
     * array[string]
     * 虚拟卡号
     * 可选
     * 虚拟卡号
     */
    private List<String> virtualCard;
}
