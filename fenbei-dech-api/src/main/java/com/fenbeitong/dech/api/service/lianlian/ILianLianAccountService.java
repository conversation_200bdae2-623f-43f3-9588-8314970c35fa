package com.fenbeitong.dech.api.service.lianlian;

import com.fenbeitong.dech.api.model.dto.lianlian.LianLianBaseDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.req.*;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianAccountActiveApplyRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianAccountApplyResultQueryRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianAccountQueryRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.account.resp.LianLianProtocolDownloadRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.file.LianLianFileUploadRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.file.LianLianFileUploadRpcRespDTO;

public interface ILianLianAccountService {

    LianLianFileUploadRpcRespDTO fileUpload(LianLianFileUploadRpcReqDTO lianLianFileUploadRpcReqDTO);

    /**
     * 开户申请
     */
    boolean apply(LianLianAccountApplyRpcReqDTO lianLianAccountApplyRpcReqDTO);


    LianLianAccountApplyResultQueryRpcRespDTO applyResultQuery(LianLianAccountApplyResultQueryRpcReqDTO lianLianAccountApplyResultQueryRpcReqDTO);

    boolean modifyBaseInfo(LianLianAccountModifyBaseInfoRpcReqDTO lianLianAccountModifyBaseInfoRpcReqDTO);

    boolean modifyProductInfo(LianLianAccountModifyProductInfoRpcReqDTO lianLianAccountModifyProductInfoRpcReqDTO);

    /**
     * 查询账户信息
     * @param request
     * @return
     */
    QueryLianlianAcctRespDTO queryAcctInfor(QueryLianlianAcctReqDTO request);


    /**
     * /account/v1/active/apply
     * 账户激活申请
     */
    LianLianAccountActiveApplyRpcRespDTO activeApply(LianLianAccountActiveApplyRpcReqDTO lianLianAccountActiveApplyRpcReqDTO);
    /**
     * /account/v1/active/apply
     * 账户激活申请
     */
    LianLianBaseDTO activeVerify(LianLianAccountActiveVerifyRpcReqDTO lianLianAccountActiveVerifyRpcReqDTO);

    LianLianAccountQueryRpcRespDTO accountDetail(LianLianAccountQueryRpcReqDTO lianLianAccountQueryRpcReqDTO);

    /**
     * 协议下载
     * @param protocolDownloadReqDTO
     * @return
     */
    LianLianProtocolDownloadRespDTO protocolDownload(LianLianProtocolDownloadReqDTO protocolDownloadReqDTO);
}
