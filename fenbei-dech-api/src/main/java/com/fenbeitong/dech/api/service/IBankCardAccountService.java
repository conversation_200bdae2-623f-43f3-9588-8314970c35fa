package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

/**
 * <AUTHOR>
 * @Date 2021/3/6
 * @Description 员工虚拟卡
 */
public interface IBankCardAccountService {


    /**
     * @MethodName: queryIndividualAccountStatus
     * @Description: 个人账户开户状态查询
     * @Param: [sysOrdNo] 银行流水号
     * @Return: IndividualAccountStatusInfoDto
     * @Author: Jarvis.li
     * @Date: 2021/3/8
     **/
    IndividualAccountStatusInfoDto queryIndividualAccountStatus(String sysOrdNo);

    /**
     * @MethodName: accountRegister
     * @Description: 虚拟卡账户注册
     * @Param: [accountRegisterRpcDto]
     * @Return: AccountRegisterRespDto
     * @Author: Jarvis.li
     * @Date: 2021/3/8
    **/
    AccountRegisterRespDto accountRegister(AccountRegisterRpcDto accountRegisterRpcDto);

    /**
     * @MethodName: accountOpen
     * @Description: 虚拟卡账户开户
     * @Param: [accountOpenRpcDto]
     * @Return: com.fenbeitong.dech.api.model.dto.AccountOpenRespDto
     * @Author: Jarvis.li
     * @Date: 2021/3/8
    **/
    AccountOpenRespDto accountOpen(AccountOpenRpcDto accountOpenRpcDto);
}
