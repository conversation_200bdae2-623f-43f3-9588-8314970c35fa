package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class BankEntRefundExchangeQueryReqDTO implements Serializable {

    private String custNo;

    /**
     * 平台编码 用友-UFIDA,招商-CMB
     */
    @NotBlank
    private String platformCode;

    /**
     * 账号
     */
    @NotBlank
    private String accountNo;

    /**
     * 账户名称
     */
    @NotBlank
    private String accountName;

    /**
     * 起始日期 yyyyMMdd
     */
    @NotBlank
    private String beginDate;

    /**
     * 截止日期 yyyyMMdd
     */
    @NotBlank
    private String endDate;

    /**
     * 交易状态
     */
    private String tradeStatus;

    private String accountInfoId;

    private String companyId;

    private Integer businessType;
}
