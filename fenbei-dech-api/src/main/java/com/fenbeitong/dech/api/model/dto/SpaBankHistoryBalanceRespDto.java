package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName SpaBankHistoryBalanceRespDto
 * @Description: 平安查询历史余额
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SpaBankHistoryBalanceRespDto implements Serializable {


    /**
     * 			账号	C(20)	必输
     */
    private String account;

    /**
     * 			币种	C(3)	必输
     */
    private String ccyCode;

    /**
     * 			历史日期	C(8)	必输
     */
    private String rptDate;

    /**
     * 			钞汇标志	C(1)	非必输
     */
    private String ccyType;

    /**
     * 			账单余额	NUM(15,2)	必输	账单余额，如包括智能存款、阶梯财富产品等计算出来的余额。和4013历史明细最后一笔交易后余额等价。
     */
    private String hisBalance;

    /**
     * 			账面余额	NUM(15,2)	非必输	账面余额，等于历史回单的余额
     */
    private String hisBookBalance;

}
