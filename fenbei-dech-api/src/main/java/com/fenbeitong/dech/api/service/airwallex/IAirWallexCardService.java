package com.fenbeitong.dech.api.service.airwallex;

import com.fenbeitong.dech.api.model.dto.airwallex.AirCardDetailsRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.AirCreateCardRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.AirGetSensitiveCardDetailsRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.AirUpdateCardRpcReqDTO;

/**
 * Created by yang.Li on 2023/3/30.
 */

public interface IAirWallexCardService {


    /**
     * 获取虚拟卡的敏感卡信息
     */
    AirGetSensitiveCardDetailsRpcRespDTO getSensitiveCardDetails(String id);

    /**
     * 获取卡详情信息
     */
    AirCardDetailsRpcRespDTO getCardDetails(String id);


    /**
     * 创建卡片
     */
    AirCardDetailsRpcRespDTO createCard(AirCreateCardRpcReqDTO reqDTO);


    /**
     * 更新卡片
     */
    AirCardDetailsRpcRespDTO updateCard(String id, AirUpdateCardRpcReqDTO reqDTO);

    /**
     * 激活卡
     */
    void activateCard(String id);



}
