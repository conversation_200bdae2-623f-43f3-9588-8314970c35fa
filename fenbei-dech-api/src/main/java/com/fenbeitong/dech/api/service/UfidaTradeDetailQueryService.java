package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.ufida.TradeRecordReqDTO;
import com.fenbeitong.dech.api.model.dto.ufida.TradeRecordRespDTO;

/**
 * <AUTHOR>
 * @description 交易明细查询（用友）
 * @date 2022-07-04 15:35 下午
 */
public interface UfidaTradeDetailQueryService {

	/**
	 * 查询当天的交易记录
	 * @param param
	 * @return
	 */
	TradeRecordRespDTO queryTradeRecord(TradeRecordReqDTO param);
	
}