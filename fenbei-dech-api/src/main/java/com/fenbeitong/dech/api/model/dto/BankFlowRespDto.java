package com.fenbeitong.dech.api.model.dto;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BankFlowRespDto implements Serializable {

    /**
     * 分贝通流水号
     */
    private String txnId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * bass自己的流水号
     */
    private String bassTxnId;

    /**
     * 企业账号 银行企业虚户
     */
    private String accountNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行交易流水号
     */
    private String bankTransNo;

    /**
     * 绑定账户类型 0 绑定账户 1 非绑定账户
     */
    private Integer bindAcctType;

    /**
     * 账户类型
     */
    private Integer accountSubType;

    /**
     * 交易状态 processing：处理中 succeeded：提现成功 failed：失败
     */
    private String txnStatus;

    /**
     * 金额 分
     */
    private BigDecimal amount;

    /**
     * 付款方银行卡号
     */
    private String payBankAccountNo;

    /**
     * 收款方账号
     */
    private String receiveAccountNo;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 交易创建时间
     */
    private String tradeDate;

    /**
     * 账户流水号
     */
    private String accountFlowId;


    /**
     * 银行流水号--柜员流水号
     * 目前只有浦发银行使用，后期如果其他银行交易存在两种流水，也可以使用当前字段存储
     */
    private String bankTellerTransNo;

}
