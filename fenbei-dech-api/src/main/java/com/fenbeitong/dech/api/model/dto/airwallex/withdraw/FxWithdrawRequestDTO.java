package com.fenbeitong.dech.api.model.dto.airwallex.withdraw;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import com.fenbeitong.dech.api.enums.AirwallexEntityTypeEnum;
import com.fenbeitong.dech.api.enums.AirwallexFeePaidByEnum;
import com.fenbeitong.dech.api.enums.AirwallexPaymentMethodEnum;
import com.fenbeitong.dech.api.enums.AirwallexPaymentReasonEnum;
import com.fenbeitong.dech.api.enums.SwiftChargeOptionEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class FxWithdrawRequestDTO implements Serializable {
    /**
     * 支付请求中指定的唯一请求ID
     */
    @NotNull(message = "请求流水id不能为空")
    private String requestId;
    
    /**
     * 平台 @see FxAcctChannelEnum
     */
    @NotNull
    private String channel;
    
    /**
     * 将显示在收款人银行交易记录上的银行付款参考号
     */
    private String reference;

    /**
     * 收款人地址
     */
    @NotNull
    private AddressDTO address;
    
    /**
     * 收款人账户信息
     */
    @NotNull
    private BankAccountDTO bankDetails;
    
    /**
     * 收款人公司名称
     */
    @NotNull
    private String companyName;

    /**
     * 收款人实体类型：PERSONAL, COMPANY
     */
    @Builder.Default
    private String entityType = AirwallexEntityTypeEnum.COMPANY.name();
    
    /**
     * 要支付的金额(目标币种金额)
     */
    private BigDecimal paymentAmount;
    
    /**
     * 要支付的金额(付款币种金额)
     */
    private BigDecimal sourceAmount;
    
    /**
     * 付款id，付款时不用传，取消时必传
     */
    private String paymentId;

  //*******************************以下信息有默认值，可不填*******************************

    /**
     * 如果指定，从请求中复制。否则，系统将自动填充这个字段
     */
    @Builder.Default
    private String paymentMethod = AirwallexPaymentMethodEnum.SWIFT.name();

    /**
     * 支付指令的原因
     */
    @Builder.Default
    private String reason = AirwallexPaymentReasonEnum.professional_business_services.name();

    /**
     * 付款账户币种
     */
    @Builder.Default
    private String sourceCurrency = "USD";
    
    /**
     * 表示付款人/受益人是否有责任支付费用。除非在请求中填充字段，否则默认为PAYER
     */
    @Builder.Default
    private String feePaidBy = AirwallexFeePaidByEnum.PAYER.name();

    /**
     * (仅适用于SWIFT支付），指定谁应承担SWIFT费用，是共享（默认）还是支付方
     */
    @Builder.Default
    private String swiftChargeOption = SwiftChargeOptionEnum.PAYER.getCode();
}
