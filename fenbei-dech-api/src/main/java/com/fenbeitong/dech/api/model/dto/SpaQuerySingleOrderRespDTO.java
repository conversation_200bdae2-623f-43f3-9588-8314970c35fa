package com.fenbeitong.dech.api.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName SpaQuerySingleOrderRespDTO
 * @Description: 查询充值订单
 * <AUTHOR>
 * @Date 2021/10/25
 **/
@Data
public class SpaQuerySingleOrderRespDTO implements Serializable {

    /*
     * 商户编号	Y 32
     * 商户在云收款系统的编号
     */
    private String traderNo;

    /*
     * 商户订单号	Y 100
     * 商户系统生成的订单号
     */
    private String traderOrderNo;

    /*
     * 银行订单号	Y 50
     * 云收款系统订单号
     */
    private String bankOrderNo;

    /*
     * 支付方式编号	Y 50
     * 云收款支付方式编号
     */
    private String payModeNo;

    /*
     * 交易金额	Y 20
     * 订单金额（单位：分）
     */
    private String tranAmt;

    /*
     * 订单类型	Y 3
     * 1支付 2退款 3撤销
     * SpaOrderTypeEnum
     */
    private String orderType;

    /*
     * 订单状态	Y 20
     * 0 已受理;1 交易成功 ;2 交易中; 3 用户支付中;  4 交易关闭; 9 已撤销
     * SpaOrderStatusEnum
     */
    private String orderStatus;

    /*
     * 订单发送时间	Y 20
     * 商户系统订单生成时间 格式：yyyyMMddHHmmss
     */
    private String orderSendTime;


    /*
     * 支付成功时间	N 20
     * 云收款系统订单支付成功时间，格式：yyyyMMddHHmmss
     */
    private String paySuccessTime;

    /*
     * 通道订单号	N 32
     * 上游通道返回的订单号
     */
    private String channelOrderNo;

    /*
     * 付款方信息	N 100
     * 支付账户信息
     */
    private String payerInfo;

    /*
     * 支付卡类型	N 20
     * 1：借记卡/储蓄卡2：贷记卡/信用卡
     * PayCardTypeEnum
     */
    private String payCardType;

    /*
     * 二维码	N 300
     * 商户被扫二维码
     */
    private String dimensionalCode;

    /*
     * 清算属性	N 20
     * 订单支付成功时返回订单清算属性的值
     */
    private String settleProperty;
}
