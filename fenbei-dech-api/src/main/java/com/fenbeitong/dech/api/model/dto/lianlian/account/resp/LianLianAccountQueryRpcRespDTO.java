package com.fenbeitong.dech.api.model.dto.lianlian.account.resp;

import com.fenbeitong.dech.api.model.dto.lianlian.LianLianBaseDTO;
import lombok.Data;

@Data
public class LianLianAccountQueryRpcRespDTO extends LianLianBaseDTO {
    /**
     * 商户号
     */
    private String mchId;

    /**
     * 公司主体id
     */
    private String enterpriseId;

    /**
     * 公司主体名称
     */
    private String enterpriseName;

    /**
     * 账户号
     */
    private String accountNo;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户状态 NORMAL
     */
    private String accountStatus;

    /**
     * 账户币种
     */
    private String currency;

    /**
     * 银行账户号 加款充值的银行账户号
     */
    private String bankAccountNo;

    /**
     * 银行账户名称
     */
    private String bankAccountName;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行号
     */
    private String bankNo;

    /**
     * 支行名称
     */
    private String bankBranchName;

    /**
     * 资金余额 单位：元
     */
    private String amtBalcur;

    /**
     * 可用余额 单位：元
     */
    private String amtBalaval;

    /**
     * 冻结金额 单位：元
     */
    private String amtBalfrz;
}
