package com.fenbeitong.dech.api.model.dto;

import com.fenbeitong.dech.api.enums.AccountStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/2/8
 * @Description
 */
@Data
public class BankTradeRespDto implements Serializable {

    /**
     * 交易状态
     */
    private String txnSt;

    /**
     * 订单流水号
     */
    private String txnId;

    /**
     * 账户
     */
    private String accountNo;

    /**
     * 银行订单号
     */
    private String sysOrdNo;

    /**
     * 交易金额
     */
    private BigDecimal operationAmount;


    /**
     * 消费方账户
     */
    private String payAccountNo;

    /**
     * 收款方账户
     */
    private String receiveAccountNo;

    /**
     * 交易是否成功 true 成功
     */
    private Boolean tradeSucceeded;

    /**
     * 对接银行的流水号
     */
    private String syncBankTransNo;

    /**
     *  失败原因
     */
    private String failReason;


    public String setStatus(){
        if(AccountStatusEnum.isTradeProcessing(txnSt)){
            this.txnSt = AccountStatusEnum.FAILED.getStatus();
            this.tradeSucceeded = false;
        }else {
            this.tradeSucceeded = true;
        }
        return txnSt;
    }

}
