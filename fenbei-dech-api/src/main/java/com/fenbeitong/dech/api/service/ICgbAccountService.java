package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.BankAcctBookRespDto;
import com.fenbeitong.dech.api.model.dto.cgb.CgbReplaceCardReqDTO;
import com.fenbeitong.dech.api.model.dto.cgb.CgbReplaceCardRespDTO;
import com.fenbeitong.dech.api.model.dto.cgb.MerchantAccountBalanceQueryReqDto;
import com.fenbeitong.dech.api.model.dto.cgb.MerchantAccountBalanceQueryRespDto;

public interface ICgbAccountService {

    /**
     * 平台账簿信息查询
     *
     * @param custAccno
     * @return
     */
    BankAcctBookRespDto queryCustAccount(String custAccno);

    /**
     * 查询实体专用对公户余额信息
     *
     * @param merchantAccountBalanceQueryReqDto
     * @return
     */
    MerchantAccountBalanceQueryRespDto merchantAccountBalanceQuery(MerchantAccountBalanceQueryReqDto merchantAccountBalanceQueryReqDto);

    CgbReplaceCardRespDTO replaceCard(CgbReplaceCardReqDTO cgbReplaceCardReqDTO);

}
