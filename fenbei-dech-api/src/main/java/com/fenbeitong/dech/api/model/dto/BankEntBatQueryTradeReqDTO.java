package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class BankEntBatQueryTradeReqDTO implements Serializable {

    /**
     * 客户号
     */
    private String custNo;

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 平台编码 用友-UFIDA,招商-CMB
     */
    @NotBlank
    private String platformCode;

    /**
     * 批次号
     */
    @NotBlank
    private String batNo;

    /**
     * 分贝通流水号
     */
    private String tradeFlowId;

    /**
     * 请求银行订单号
     */
    private String syncBankTransNo;
}
