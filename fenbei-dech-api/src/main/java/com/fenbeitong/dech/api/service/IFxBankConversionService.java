package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.airwallex.*;

public interface IFxBankConversionService {
    /**
     * 创建换汇请求
     * @param fxConversionsCreateRpcReqDTO
     * @return
     */
    FxBaseDTO<FxConversionsCreateRpcRespDTO> conversionCreate(FxConversionsCreateRpcReqDTO fxConversionsCreateRpcReqDTO);

    FxBaseDTO<FxConversionsQueryRpcRespDTO> conversionQuery(FxConversionsQueryRpcReqDTO fxConversionsQueryRpcReqDTO);

    FxBaseDTO<FxConversionsListAllRpcRespDTO> listAllConversion(FxConversionsListAllRpcReqDTO fxConversionsListAllRpcReqDTO);

    FxBaseDTO<FxMarketFxRpcRespDTO> marketFX(FxMarketFxRpcReqDTO fxMarketFxRpcReqDTO);
}
