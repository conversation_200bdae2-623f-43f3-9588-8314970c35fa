package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName QueryCompanyAccountStatusReqDTO
 * @Description: 查询企业开户状态
 * <AUTHOR>
 * @Date 2021/4/9
 **/
@Data
public class QueryCompanyAccountStatusReqDTO implements Serializable {

    /*
    账户
     */
    private String accountNo;

    /*
    请求流水号-平安使用
     */
    private String seqNo;

    /*
    银行编码
     */
    private String bankName;

    /*
    资料提交时间：注册时间
     */
    private Date submitTime;
}
