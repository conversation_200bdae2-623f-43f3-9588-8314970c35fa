package com.fenbeitong.dech.api.model.dto.airwallex;

import lombok.Data;

import java.io.Serializable;
@Data
public class FxConversionsCreateRpcReqDTO implements Serializable {
    private String channel;
    /**
     * 可选
     */
    private String buyAmount;
    /**
     * 必选
     */
    private String buyCurrency;
    /**
     * 可选
     */
    private String conversionDate;
    /**
     * 可选
     */
    private String quoteId;
    /**
     * 必选
     */
    private String reason;
    /**
     * 必选
     */
    private String requestId;
    /**
     * 可选
     */
    private String sellAmount;
    /**
     * 必选
     */
    private String  sellCurrency;
    /**
     * 必选
     */
    private boolean termAgreement;
}
