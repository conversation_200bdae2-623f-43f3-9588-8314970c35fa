package com.fenbeitong.dech.api.service.cmb;

import com.fenbeitong.dech.api.model.dto.cmb.CmbPayStatementFileQueryReqDTO;
import com.fenbeitong.dech.api.model.dto.cmb.CmbPayStatementFileRespDTO;
import com.fenbeitong.dech.api.model.dto.cmb.CmbPayStatementQueryReqDTO;
import com.fenbeitong.dech.api.model.dto.cmb.CmbPayStatementRespDTO;

import java.util.List;

/**
 * Created by FBT on 2023/4/12.
 */
public interface IcmbBankEntStatementService {

    List<CmbPayStatementRespDTO> queryPayStatementTaskInfo(CmbPayStatementQueryReqDTO cmbPayStatementQueryReqDTO);

    CmbPayStatementFileRespDTO queryPayStatementFile(CmbPayStatementFileQueryReqDTO cmbPayStatementFileQueryReqDTO);

}
