package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;
import com.fenbeitong.dech.api.model.dto.BankAcctBookRespDto;
import com.fenbeitong.dech.api.model.dto.ZxTradeDetailsReqDTO;
import com.fenbeitong.dech.api.model.dto.ZxTradeDetailsRespDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/19
 * @Description
 */
public interface IZxBankAccountService {

    /**
     * 中信明细查询
     */
    List<ZxTradeDetailsRespDTO> querySubAccountTradeDetail(ZxTradeDetailsReqDTO reqDTO);

    /**
     * 中信余额查询
     */
    BankAcctBookRespDto queryAcctBalance(String fctId);
    /**
     * 中信历史余额查询
     */
    ZxAcctHistoryBalanceRespDTO queryAcctHistoryBalance(Date date);

    /**
     * 中信查询银行交易明细下载页码
     */
    List<ZxQueryTradeFlowPageRespDto> queryTradeFlowPage(ZxQueryTradeFlowPageReqDTO zxQueryTradeFlowPageReqDTO);

    /**
     * 中信处理电子回单
     */
    void zxReceiptProcess(String aubAcctNo, Date date);

    void zxPlatformAccountReceiptProcess(String aubAcctNo, Date startTime);
}
