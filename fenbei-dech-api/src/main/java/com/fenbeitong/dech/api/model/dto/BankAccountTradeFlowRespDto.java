package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/27
 * @Description 账簿流水明细
 */
@Data
public class BankAccountTradeFlowRespDto implements Serializable {


    /**
     *交易流水 ID
     */
    private String TranSrId;

    /**
     *电子账簿 Id
     */
    private String FctId;

    /**
     *系统订单号
     */
    private String SysOrdNo;

    /**
     *订单号
     */
    private String OrdNo;

    /**
     *交易类型：充值、消费、担保
     */
    private String TxnTp;

    /**
     *可提现金额
     */
    private String AvlWthAmt;

    /**
     *在途金额
     */
    private String PrpAmt;

    /**
     *不可用金额
     */
    private String UndAmt;

    /**
     *交易时间
     */
    private String TranTm;

}
