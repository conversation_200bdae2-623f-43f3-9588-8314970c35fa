package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022年10月11日 15:34
 * @description 虚拟卡交易公共请求DTO
 */
@Data
public class BankCardTradeBaseDto implements Serializable {

    /**
     * 银行名称
     */
    @NotNull
    private String bankName;

    /**
     * 公司id
     */
    @NotNull
    private String companyId;

    /**
     * 账户类型
     */
    @NotNull
    private Integer accountSubType;

    /**
     * 账户模式
     */
    @NotNull
    private Integer accountModel;

    /**
     * 标识  1. 企业  2. 个人
     */
    @NotNull
    private Integer customerType;

    /**
     * 账户流水id
     */
    @NotNull
    private String accountFlowId;

    /**
     * 订单流水号
     */
    @NotNull
    private String txnId;

    /**
     * 消费方账户
     */
    @NotNull
    private String payAccountNo;

    /**
     * 消费方账户名
     */
    @NotNull
    private String payAccountName;

    /**
     * 收款方账户
     */
    @NotNull
    private String receiveAccountNo;

    /**
     * 收款方账户名
     */
    @NotNull
    private String receiveAccountName;

    /**
     * 操作金额 分
     */
    @NotNull
    private BigDecimal operationAmount;
}
