package com.fenbeitong.dech.api.model.dto.airwallex.withdraw;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**  
 * @Description: 
 * <AUTHOR>
 * @date 2023年8月8日 
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class BankAccountDTO implements Serializable {

    /**
     * 账户货币 @see CurrencyEnum
     */
    @NotBlank
    private String accountCurrency;

    /**
     * 收款人银行账户的账户持有人姓名
     * 长度为2-200个字符
     * 不应包含表情符号
     */
    @NotBlank
    private String accountName;

    /**
     * 账号，主要针对非欧洲国家，应填写Account_number或iban
     * 仅限数字
     * 1-17个字符长
     */
    @NotBlank
    private String accountNumber;
    
    /**
     * 银行国家代码（2位ISO 3166-2国家代码）@see CountryCodeEnum
     */
    @NotBlank
    private String bankCountryCode;
    
    /**
     * 收款人银行账户的银行名称
     * 1-200个字符长
     * 不应包含表情符号
     */
    @NotBlank
    private String bankName;
    
    /**
     * 银行swift代码
     */
    @NotBlank
    private String swiftCode;
}
