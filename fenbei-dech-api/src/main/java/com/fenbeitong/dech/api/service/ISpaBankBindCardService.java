package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;
import com.fenbeitong.dech.api.model.dto.spabank.req.MntMbrBindRelateAcctBankCodeReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.MntMbrBindRelateAcctBankCodeRespDto;

/**
 * @description: 银行绑卡
 * @author: yanqiu.hu
 * @create: 2022-09-16 11:21:19
 * @Version 1.0
 **/
public interface ISpaBankBindCardService {

    /**
     * 平安-查询绑定信息
     *
     * @param reqDto
     * @return
     */
    BindQueryRespDto memberBindQuery(BindQueryReqDto reqDto);

    /**
     * 平安-会员解绑提现账户
     *
     * @param reqDto
     * @return
     */
    UnbindRelateAcctRespDto unbindRelateAcct(UnbindRelateAcctReqDto reqDto);


    /**
     * 平安-绑卡
     *
     * @param reqDto
     */
    BindSmallAmountWithCheckCorpRespDto bindSmallAmountWithCheckCorp(CreateCompanyAccountRpcDto reqDto);

    /**
     * 维护会员绑定提现账户联行号
     *
     * @param reqDto
     * @return
     */
    MntMbrBindRelateAcctBankCodeRespDto mntMbrBindRelateAcctBankCode(MntMbrBindRelateAcctBankCodeReqDto reqDto);


}
