package com.fenbeitong.dech.api.model.dto.airwallex;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FxMarketFxRpcRespDTO implements Serializable {

    private String channel;

    /**
     * 仅供参考。这是Airwallex通过分析多家银行供应商和独立利率发布者的价格而编制的参考利率。
     */
    private String awxRate;

    /**
     * 客户买入的金额，以buy_currency表示。
     */
    private String buyAmount;

    /**
     * 客户购买的货币（3个字母的ISO-4217格式）。
     */
    private String buyCurrency;

    /**
     * 转换的交易执行率。这是向客户收费的比率。
     */
    private String clientRate;

    /**
     * 转换将被结算的日期。转换日期默认为当前日期，如果有资金，除非指定。
     */
    private String conversionDate;

    /**
     * Airwallex为转换记录生成的ID。
     */
    private String conversionId;

    /**
     * 创建转换记录的时间。遵循与Airwallex其他API相同的时间戳格式。
     */
    private String createdAt;

    /**
     * 与awx_rate和client_rate相关的货币对。
     */
    private String currencyPair;

    /**
     * 成交表示转换请求的固定一方。如果指定buy_amount，则buy_currency；否则，sell_currency（以3个字母的ISO-4217格式）。
     */
    private String dealtCurrency;

    /**
     * 转换最后一次被更新的时间（包括状态变化）。遵循与Airwallex的其他API相同的时间戳格式。
     */
    private String lastLpdatedAt;

    /**
     * 仅供参考。该汇率代表当前买入和卖出价格的中点。
     */
    private String midRate;

    /**
     * 用于执行转换的LockFX报价。如果提供的话，会呼应请求的LockFX报价。
     */
    private String quoteId;

    private List<FxConversionRateDetailsDTO> rateDetails;

    /**
     * 回应请求的客户提供的转换原因。
     */
    private String reason;

    /**
     * 响应请求的客户提供的请求ID（idempotency key）。
     */
    private String requestId;

    /**
     * 客户端卖出的以sell_currency为单位的金额。
     */
    private String sellAmount;

    /**
     * 客户卖出的货币（以3个字母的ISO-4217格式）。
     */
    private String sellCurrency;

    /**
     * 这是在Airwallex账户余额中需要有卖出货币的卖出金额的最晚时间，以便成功结算。
     */
    private String settlementCutoffTime;

    /**
     * 简化的交易ID，便于在Web GUI和Airwallex支持中使用。
     */
    private String shortReferenceId;

    /**
     * 转换的状态。
     */
    private String status;
}
