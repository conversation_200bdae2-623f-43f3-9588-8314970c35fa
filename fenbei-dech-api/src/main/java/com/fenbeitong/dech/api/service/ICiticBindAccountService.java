package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.citic.*;

/**
 * 中信换绑
 */
public interface ICiticBindAccountService {

    CiticBindAccountSingleRespDTO bindAccountSingle(CiticBindAccountSingleReqDTO citicBindAccountSingleReqDTO);
    /**
     * 出入金绑定账户管理
     * @param zxBindAccountManagerReqDTO 出入金绑定账户管理
     * @return ZxBindAccountManagerRespDTO
     */
    CiticBindAccountManagerRespDTO bindAccountManager(CiticBindAccountManagerReqDTO zxBindAccountManagerReqDTO);

    CiticBindAccountQueryRespDTO bindAccountQuery(CiticBindAccountQueryReqDTO zxBindAccountQueryReqDTO);
}
