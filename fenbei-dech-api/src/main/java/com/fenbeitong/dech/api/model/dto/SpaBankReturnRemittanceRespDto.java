package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SpaBankReturnRemittanceRespDto
 * <AUTHOR>
 * @Date 2022/1/21
 **/
@Data
public class SpaBankReturnRemittanceRespDto implements Serializable {


    /**
     * 		符合查询条件的笔数	9(10)	Y
     * 		符合当前查询条件的笔数
     */
    private String totalCts;

    /**
     * 		记录结束标志	C(1)	Y
     * 	Y:无剩余记录
     * 	N:有剩余记录
     */
    private String isEnd;

    /**
     * 		当前页码	C(10)	Y
     * 	同上送
     */
    private String pageNo;

    /**
     * 		每页记录条数	C(5)	Y
     * 		同上送
     */
    private String PageCts;

    private List<SpaReturnRemittanceDetails> list;

}
