package com.fenbeitong.dech.api.service.zbbank;

import com.fenbeitong.dech.api.model.dto.zbbank.req.*;
import com.fenbeitong.dech.api.model.dto.zbbank.resp.*;

/**
 *  众邦银行虚拟卡
 *  时间: 20230-11月
 *  原因: 银行升级
 *  升级原因: 银行合规原因实现方案发生,重新对接
 */
public interface IZbBankVirtualCardService {
    /**
     * 虚拟卡账户注册
     * @param zbAccountRegisterRpcReqDTO 注册请求
     * @return ZbAccountRegisterRespDTO
     */
    ZbAccountRegisterRpcRespDTO accountRegister(ZbAccountRegisterRpcReqDTO zbAccountRegisterRpcReqDTO);

    /**
     * 虚拟卡账户开通
     * @param zbAccountOpenRpcReqDTO  开户请求
     * @return ZbAccountOpenRpcRespDTO
     */
    ZbAccountOpenRpcRespDTO accountOpen(ZbAccountOpenRpcReqDTO zbAccountOpenRpcReqDTO);

    /**
     * 更换绑定卡
     */
    ZbAccountBindCardRpcRespDTO changeAccountBindCard(ZbAccountBindCardRpcReqDTO zbAccountBindCardRpcDTO);

    ZbAccountOpenResultRpcRespDTO accountOpenResultQuery(ZbAccountOpenResultRpcReqDTO zbAccountBindCardRpcReqDTO);

    /**
     * 迁移专用:针对存量通过E账通开立的二类户，在过来直接调用二类户相关接口时，都需要先通过账户信息查询，互金这边会自动的内部激活和同步原来E账通开立的二类户
     * @param zbAccountInfoQueryRpcReqDTO
     * @return
     */
    ZbAccountInfoQueryRpcRespDTO accountInfoQuery(ZbAccountInfoQueryRpcReqDTO zbAccountInfoQueryRpcReqDTO);

    /**
     * 首次错花还款，银行下发验证码
     * 二类户签约申请
     */
    ZbAccountSignApplyRpcRespDTO signApply(ZbAccountSignApplyRpcReqDTO zbAccountSignApplyRpcReqDTO);

    /**
     * ShrtMsgSign
     * 首次错花还款，银行校验验证码
     */
    ZbAccountSignApplyQueryRpcRespDTO signApplyQuery(ZbAccountSignApplyQueryRpcReqDTO zbAccountSignApplyVerifyRpcReqDTO);

    /**
     * ShrtMsgSign
     * 首次错花还款，银行校验验证码
     */
    ZbAccountSignApplyVerifyRpcRespDTO signApplyVerify(ZbAccountSignApplyVerifyRpcReqDTO zbAccountSignApplyRpcReqDTO);

    /**
     * 解约
     */
    ZbAccountSignCancelRpcRespDTO signCancel(ZbAccountSignCancelRpcReqDTO zbAccountSignApplyVerifyRpcReqDTO);

    ZbAccountAgreementPayRpcRespDTO agreementPay(ZbAccountAgreementPayRpcReqDTO zbAccountAgreementPayRpcReqDTO);

    /**
     *
     */
    ZbAccountAgreementPayQueryRpcRespDTO agreementPayQuery(ZbAccountAgreementPayQueryRpcReqDTO zbAccountAgreementPayQueryRpcReqDTO);
}
