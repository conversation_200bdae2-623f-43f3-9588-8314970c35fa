package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.BankQueryTradeReqDto;
import com.fenbeitong.dech.api.model.dto.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/2/24
 * @Description
 */
public interface IBankSearchService {


    /**
     * 查询交易流水
     *
     * @param reqDto
     * @return
     */
    BankAccountTradeFlowListRespDto queryAccountBookFlow(BankAccountTradeFlowReqDto reqDto);

    /**
     * 提现信息查询
     *
     * @param reqDto
     * @return
     */
    BankQueryTradeRespDto queryCashOutInfo(BankQueryTradeReqDto reqDto);

    /**
     * 交易信息查询
     *
     * @return
     */
    BankQueryTradeRespDto queryTradeInfo(BankQueryTradeReqDto reqDto);


    /**
     * 电子回单查询
     * @param reqDto

     */
    BankTradeReceiptQryRespDto queryTxnReceipt(BankTradeReceiptQryReqDto reqDto);

    /**
     * 对账单查询上传
     * @param reqDto
     * @return
     */
    BankDownloadBillRespDto queryBill(BankDownloadBillReqDto reqDto);

    /**
     * 电子账簿查询
     * @param fctId
     * @return
     */
    BankAcctBookRespDto queryAcctBook(String fctId, String bankName, Date time);

    /**
     * 电子合同查询
     * @param accountNo
     * @return
     */
    BankElectronicContractRespDto queryElectronicContract(String accountNo, String bankName);

}
