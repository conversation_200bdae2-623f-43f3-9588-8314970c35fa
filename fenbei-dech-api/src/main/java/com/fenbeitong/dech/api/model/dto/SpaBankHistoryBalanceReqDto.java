package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName SpaBankHistoryBalanceReqDto
 * @Description: 平安查询历史余额
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SpaBankHistoryBalanceReqDto implements Serializable {

    /**
     * 	账号
     */
    private String account;

    /**
     * 	日期
     */
    private String rptDate;

}
