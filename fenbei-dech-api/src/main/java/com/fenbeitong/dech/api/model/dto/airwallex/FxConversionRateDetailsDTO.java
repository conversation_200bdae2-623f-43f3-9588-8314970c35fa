package com.fenbeitong.dech.api.model.dto.airwallex;

import lombok.Data;

import java.io.Serializable;

@Data
public class FxConversionRateDetailsDTO implements Serializable {
    /**
     * 在相应级别上被购买的总金额。
     */
    private String buyAmount;

    /**
     * 视图的级别。目前只支持CLIENT级别
     */
    private String level;

    /**
     * 从各自级别的角度来看的汇率。
     */
    private String rate;

    /**
     * 在相应级别上被出售的总金额。
     */
    private String sellAmount;
}
