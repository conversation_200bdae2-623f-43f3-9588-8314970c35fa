package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.ufida.*;



/**
 * @Description:用友电子回单
 * @Author: guogx
 * @Date: 2022/7/6 上午11:07
 */
public interface IUfidaReceiptService {

    /**
     * @Description: 电子回单查询
     * @Author: guogx
     * @Date: 2022/7/6 上午11:11
     */
    UfidaReceiptQueryRespDTO queryElectronicReceipt(UfidaReceiptQueryReqDTO receiptQueryReqDTO);


    /**
     * @Description: 单笔单子回单下载并上传到OSS
     * @Author: guogx
     * @Date: 2022/7/6 下午5:44
     */
    UfidaReceiptSingleUploadRespDTO uploadElectronicReceipt(UfidaReceiptSingleUploadReqDTO receiptSingleDownReqDTO);

    /**
     * 电子回单同步
     *
     * @param reqDTO
     * @return
     */
    Boolean synElectronicReceipt(UfidaReceiptSynReqDTO reqDTO);


}
