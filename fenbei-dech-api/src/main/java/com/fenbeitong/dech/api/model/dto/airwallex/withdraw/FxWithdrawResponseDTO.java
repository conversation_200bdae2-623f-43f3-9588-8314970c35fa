package com.fenbeitong.dech.api.model.dto.airwallex.withdraw;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @Description: 
 * <AUTHOR>
 * @date 2023年8月8日
 */
@SuppressWarnings("serial")
@Data
public class FxWithdrawResponseDTO implements Serializable {

    /**
     * 收款人为此付款收到的总金额（以付款付款货币表示），
     * 由fee_paid_by中指定的价值决定，即如果fee_paid_by是PAYER，
     * 则收款人收到完全相同的付款金额，因为付款人负责支付费用。
     * 否则，收款人将收到payment_amount-费用，因为该费用已隐含地传递给收款人
     */
    private BigDecimal amountBeneficiaryReceives;

    /**
     * 付款人为进行此付款而必须支付的总金额（以源货币表示）。
     * 例如，如果fee_paid_by是PAYER，并且不涉及外汇转换，那么这是payment_amount+费用的总和，
     * 因为付款人负责费用。否则，此字段与payment_amount相同
     */
    private BigDecimal amountPayerPays;

    /**
     * 收款人信息
     */
    private BeneficiaryDTO beneficiary;

    /**
     * 用于创建此付款的收款人id
     */
    private String beneficiaryId;

    /**
     * 创建此付款请求的时间
     */
    private String createdAt;

    /**
     * 实际支出分派日期。在实际处理中，在该日期发送付款。此日期可能晚于付款日期。
     */
    private String dispatchDate;

    /**
     * 除非付款处于错误状态，否则为空
     */
    private String failureReason;

    /**
     * 除非付款处于错误状态，否则为空
     */
    private String failureType;

    /**
     * 发送此付款的付款费用（以源货币表示）
     */
    private	BigDecimal feeAmount;

    /**
     * 支付费用货币。这应该始终等于source_current
     */
    private String feeCurrency;

    /**
     * 表示付款人/收款人是否有责任支付费用。除非在请求中填充字段，否则默认为PAYER
     */
    private String feePaidBy;

    /**
     * 上次更新此付款请求的时间
     */
    private String lastUpdatedAt;

    /**
     * 一组键值对，用于将您自己的数据与付款一起存储。
     */
    private MetadataDTO metadata;

    /**
     * 付款人的详细信息
     */
    private PayerDTO payer;

    /**
     * 用于创建此付款的付款人联系人的Payer_id
     */
    private String payerId;

    /**
     * 要支付的金额
     */
    private	BigDecimal paymentAmount;

    /**
     * 付款应使用的货币（3个字母的ISO-4217代码）。
     */
    private String paymentCurrency;

    /**
     * 应该付款的日期（如果指定，从请求中复制）。
     */
    private String paymentDate;

    /**
     * 返回的付款id，可用于查询付款的状态。
     */
    private String paymentId;

    /**
     * 如果指定，从请求中复制。否则，系统将自动填充这个字段
     */
    private String paymentMethod;

    /**
     * 支付指令的原因
     */
    private String reason;

    /**
     * 将显示在收款人银行交易记录上的银行付款参考号
     */
    private String reference;

    /**
     * 支付请求中指定的唯一请求ID
     */
    private String requestId;

    /**
     * 用于支持目的的简短支付参考
     */
    private String shortReferenceId;

    /**
     * 将要收到的金额
     */
    private BigDecimal sourceAmount;

    /**
     * 如果指定的话，从请求中复制的
     */
    private String sourceCurrency;

    /**
     * 付款状态，@see AirwallexPaymentStatusEnum
     */
    private String status;

    /**
     * (仅适用于SWIFT支付），指定谁应承担SWIFT费用，是共享（默认）还是支付方
     */
    private String swiftChargeOption;

    /**
     * 底层货币转换码（如果源货币与支付货币不同）。
     */
    private String underlyingConversionId;
}
