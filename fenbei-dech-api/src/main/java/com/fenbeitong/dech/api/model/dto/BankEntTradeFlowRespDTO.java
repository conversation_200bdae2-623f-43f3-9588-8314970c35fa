package com.fenbeitong.dech.api.model.dto;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BankEntTradeFlowRespDTO implements Serializable {

    private Long id;

    /**
     * 分贝通流水号
     */
    private String tradeFlowId;

    /**
     * 同account_inf表 id
     */
    private String accountInfoId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行交易流水号
     */
    private String bankTransNo;

    /**
     * 银行上账流水号
     */
    private String syncBankTransNo;

    /**
     * 预下单编号
     */
    private String preTradeId;

    /**
     * 是否同步交易 0 同步 1 异步
     */
    private Integer syncTrade;

    /**
     * 银行原交易流水号
     */
    private String orgBankTransNo;

    /**
     * 交易状态 processing：处理中 succeeded：提现成功 failed：失败 ,proSucceeded 处理中后成功
     */
    private String txnStatus;

    /**
     * 分贝通原订单号
     */
    private String orgTxnId;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 金额 单位：分
     */
    private BigDecimal amount;

    /**
     * 付款方账号 银行虚户id
     */
    private String payAccountNo;

    /**
     * 收款方账号 银行虚户id
     */
    private String receiveAccountNo;

    /**
     * 交易时间
     */
    private String tradeDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 通知状态
     */
    private Integer callStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 下次回调时间
     */
    private Date nextCallDate;

    /**
     * 交易结果异步通知状态
     */
    private Integer noticeStatus;

    /**
     * 电子回单是否生成 1 已生成 2 未生成
     */
    private Integer receiptSuccess;

    /**
     * 电子回单地址
     */
    private String receiptUrl;

    /**
     * 批次号
     */
    private String batNo;

    /**
     * 备注（已被使用）
     */
    private String remark;

    /**
     * 备注2
     */
    private String remark2;

    /**
     * 银行对账编码(UFIDA) 流程号（CMB）
     */
    private String bankCheckCode;

}
