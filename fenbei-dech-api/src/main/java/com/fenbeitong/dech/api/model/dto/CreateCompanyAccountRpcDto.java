package com.fenbeitong.dech.api.model.dto;

import com.fenbeitong.dech.api.model.dto.vo.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/3
 * @Description 创建企业账户
 */
@Data
public class CreateCompanyAccountRpcDto implements Serializable {

    /**
     * 企业开户不传
     * 手动开通收款担保账户传定义类型
     */
    private String internal;

    /**
     * 平台订单号（分贝通订单号）
     */
    private String ordNo;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 公司基本信息
     */
    private CompanyBasicInfoStructVo basicInfoStructVo;

    /**
     * 营业执照结构体
     */
    private BusinessLicenseStructVo businessLicenseStructVo;

    /**
     * 法人证件信息结构体
     */
    private LegalPersonInformationStructVo legalPersonInformationStructVo;

    /**
     * 联系人信息结构体
     */
    private ContactInformationStructVo contactInformationStructVo;

    /**
     * 开户行为记录信息
     */
    private CompanyAgreeLicenseVo companyAgreeLicenseVo;

    /**
     * 平安专用
     * 商户/普通 00/SH
     */
    private String memberProperty;

    /**
     * 广发/廊坊专用
     * 短信验证码(广发开户使用)
     */
    private String verCode;

    /**
     * 短信业务代码
     */
    private String smsId;

    /**
     * 股东信息
     */
    private ShareholderInfoVo shareholderInfoVo;
    /**
     * 收益人信息
     */
    private BenefInfoVo benefInfoVo;
}
