package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.lf.LfTradeDetailsDTO;
import com.fenbeitong.dech.api.model.dto.lf.LfTradeDetailsReqDTO;

import java.util.List;

public interface ILfBankAccountService {

    String uploadFile(String fileUrl, String fileName, String accountNo);

    /**
     * 记账簿交易明细查询接口
     * @param req
     * @return
     */
    List<LfTradeDetailsDTO> queryTradeDetail(LfTradeDetailsReqDTO req);
}
