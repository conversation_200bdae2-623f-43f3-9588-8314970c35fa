package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

public interface IBankEntTradeService {

    /**
     * 对公交易
     *
     * @param reqDTO
     * @return
     */
    BankEntPublicTradeRespDTO publicTrade(BankEntPublicTradeReqDTO reqDTO);

    /**
     * 对私交易
     *
     * @param reqDTO
     * @return
     */
    BankEntPrivateTradeRespDTO privateTrade(BankEntPrivateTradeReqDTO reqDTO);


    /**
     * 查询交易流水（从数据库查）
     */
    BankEntTradeFlowRespDTO getTradeFlowByTradeFlowId(String tradeFlowId);

}
