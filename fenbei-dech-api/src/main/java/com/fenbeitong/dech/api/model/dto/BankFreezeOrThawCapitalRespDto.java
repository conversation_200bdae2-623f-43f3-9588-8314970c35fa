package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-06-21 15:22:53
 * @Version 1.0
 **/
@Data
public class BankFreezeOrThawCapitalRespDto implements Serializable {
    /**
     * 是否处理成功
     */
    private Boolean isSuccess;
    /**
     * 失败描述 Y
     */
    private String errorMessage;
    /**
     * 订单编号	Number(18)
      */
    private Long orderid;
    /**
     * 	受理编号	Number(15)	Y	系统受理编号
     */
    private Long transid;

    /**
     * 用户私有域	String(100)	N	原样返回（注意大小写）
     */
    private String userpriv;
}
