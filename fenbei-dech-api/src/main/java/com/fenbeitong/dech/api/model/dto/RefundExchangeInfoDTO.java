package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class RefundExchangeInfoDTO implements Serializable {

    /**
     * 退回日期
     */
    private String refundExchangeDate;

    /**
     * 银行流水号
     */
    private String bankTransNo;

    /**
     * 收款方账户(原支付收方账号)
     */
    private String receiveAccountNo;

    /**
     * 收款方账户名称
     */
    private String receiveAccountName;

    /**
     * 交易金额(与原支付金额相同)
     */
    private BigDecimal operationAmount;

    /**
     * 退汇原因
     */
    private String refundExchangeMemo;

    /**
     * 原支付对账码
     */
    private String paybackCheckCode;

    /**
     * 业务类型
     */
    private Integer businessType;
}
