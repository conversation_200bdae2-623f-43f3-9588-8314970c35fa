package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/2/27
 * @Description
 */
@Data
public class BankQueryTradeRespDto implements Serializable {


    /**
     * 银行系统单号
     */
    private String sysOrdNo;

    /**
     * 分贝通订单编号
     */
    private String fbOrderId;

    /**
     * processing：处理中 succeeded：提现成功 failed：失败
     */
    private String txnStatus;

    /**
     * 失败原因
     */
    private String failedReason;

    /**
     * 操作金额 分
     */
    private BigDecimal operationAmount;

    /**
     * 账号
     */
    private String accountNo;

    /**
     * 银行流水号--柜员流水号
     * 目前只有浦发银行使用，后期如果其他银行交易存在两种流水，也可以使用当前字段传输
     */
    private String bankTellerTransNo;

    /**
     * 完成时间
     */
    private String finishTime;
}
