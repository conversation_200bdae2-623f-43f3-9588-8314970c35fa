package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName SpaBankRefundToTransAcctReqDto
 * @Description: 退款1：平安易内部户到平台过渡户
 * <AUTHOR>
 * @Date 2022/1/4
 **/
@Data
public class SpaBankRefundToTransAcctReqDto extends SpaBankTradeReqBaseDto {

    /**
     * 退款1必填
     * 退款1：付款方账户名（平安易内部账户名）
     */
    @NotNull
    private String payAccountName;

    /**
     * 退款1必填
     * 退款1：收款账户名（资金汇总账户名）
     */
    @NotNull
    private String receiveAccountName;
}
