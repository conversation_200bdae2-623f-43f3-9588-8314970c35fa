package com.fenbeitong.dech.api.model.dto.lianlian.account.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LianLianAccountModifyBaseInfoRpcReqDTO implements Serializable {
    /**
     * 请求单号
     */
    private String txnSeqno;
    /**
     * 结果通知地址
     */
    private String notifyUrl;
    /**
     * 商户号
     */
    private String mchid;
    /**
     * 主体信息
     */
    private LianLianSubjectInfo subjectInfo;

    /**
     * 经营信息
     */
    private LianLianBusinessInfo businessInfo;

    /**
     * 特许资质
     */
    private List<LianLianBusinessQualification> businessQualifications;

    /**
     * 协议信息
     */
    private LianLianProtocolInfo protocolInfo;
    /**
     * 干系人信息
     */
    private LianLianRelatedPersonnel relatedPersonnel;

}
