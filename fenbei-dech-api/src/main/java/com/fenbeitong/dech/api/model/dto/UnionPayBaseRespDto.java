package com.fenbeitong.dech.api.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * @Description:
 * @Author: liyi
 * @Date: 2022/7/1 下午7:30
 */
@Data
public class UnionPayBaseRespDto implements Serializable {
    @ApiModelProperty(value = "交易应答码")
    private String respCode;

    @ApiModelProperty(value = "交易响应信息")
    private String respMsg;
}
