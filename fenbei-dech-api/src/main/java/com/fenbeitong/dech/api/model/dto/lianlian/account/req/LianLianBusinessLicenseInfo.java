package com.fenbeitong.dech.api.model.dto.lianlian.account.req;

import lombok.Data;

import java.io.Serializable;
@Data
public class LianLianBusinessLicenseInfo implements Serializable {
    private String licenseType;

    private String licenseImg;

    private String licenseNumber;

    private String licenseName;

    private String validityStartTime;

    private String validityEndTime;

    private String businessScope;

    private String registeredCapital;
    /**
     * 注册资本
     */
    private String registeredCapitalCurrency;

    private String realPayCapital;

    private String realPayCapitalCurrency;

    private String registerDate;

    private String approvedDate;

    private LianLianAddressInfo addressInfo;
}
