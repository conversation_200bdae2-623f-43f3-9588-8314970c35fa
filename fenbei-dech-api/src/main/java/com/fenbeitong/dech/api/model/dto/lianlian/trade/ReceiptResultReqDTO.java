package com.fenbeitong.dech.api.model.dto.lianlian.trade;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/08/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class ReceiptResultReqDTO implements Serializable {

	/**
	 * 商户提现单号
	 */
	private String outOrderNo;
	
	/**
	 * 系统提现单号
	 */
	private String orderNo;
		
	/**
	 * 渠道订单号
	 */
	private String chnlTxno;

	/**
	 * 商户回单申请流水
	 */
	private String receiptSeqno;
	
	/**
	 * 回单状态 APPLY：申请中、SUCCESS：成功 FAIL：失败
	 */
	private String receiptStatus;
	
	/**
	 * 回单下载链接
	 */
	private String downloadUrl;
	
	/**
	 * 回单类型 CASHOUT：代付类回单
	 */
//	private String receiptType;
}
