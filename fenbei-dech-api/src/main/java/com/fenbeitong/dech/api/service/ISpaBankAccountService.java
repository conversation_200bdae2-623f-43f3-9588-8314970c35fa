package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;
import com.fenbeitong.dech.api.model.dto.spabank.req.SpaApplyForChangeOfCellPhoneNumReqDTO;
import com.fenbeitong.dech.api.model.dto.spabank.req.SpaBackFillDynamicPasswordReqDTO;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaApplyForChangeOfCellPhoneNumRespDTO;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaBackFillDynamicPasswordRespDTO;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaPersonAcctRespBaseDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/10/20
 * @Description
 */
public interface ISpaBankAccountService {

    /*
     * @MethodName: getSpaRechargeChannel
     * @Description: 获取充值通道
     * @Param: [spaRechargeChannelReqDTO]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaRechargeChannelRespDTO
     * @Author: Jarvis.li
     * @Date: 2021/10/25
    **/
    SpaRechargeChannelRespDTO getSpaRechargeChannel(SpaRechargeChannelReqDTO spaRechargeChannelReqDTO);

    /*
     * @MethodName: querySingleOrder
     * @Description: 查询充值订单信息
     * @Param: [spaQuerySingleOrderReqDTO]
     * @Return: com.fenbeitong.dech.api.model.dto.SpaQuerySingleOrderRespDTO
     * @Author: Jarvis.li
     * @Date: 2021/10/25
    **/
    SpaQuerySingleOrderRespDTO querySingleOrder(SpaQuerySingleOrderReqDTO spaQuerySingleOrderReqDTO);

    /*
     * @MethodName: verifySortSign
     * @Description: 验签
     * @Param: [params]
     * @Return: boolean
     * @Author: Jarvis.li
     * @Date: 2021/10/25
    **/
    boolean verifySortSign(Map<String,String> params);


    /*
     * @MethodName: transferVerify
     * @Description: 线下充值通知验签
     * @Param: [params]
     * @Return: boolean
     * @Author: Jarvis.li
     * @Date: 2021/10/25
     **/
    boolean transferVerify(Map<String, String> params);

    /*
     * @MethodName: transferSign
     * @Description: 线下充值通知加签
     * @Param: [params]
     * @Return: boolean
     * @Author: Jarvis.li
     * @Date: 2021/10/25
     **/
    String transferSign(String message);

    /*
     * @MethodName: transferRechargeQuery
     * @Description: 查询普通转账充值明细
     * @Param: [reqDTO]
     * @Return: java.util.List<com.fenbeitong.dech.api.model.dto.SpaTradeDetailsRespDTO>
     * @Author: Jarvis.li
     * @Date: 2022/2/25
    **/
    List<SpaTradeDetailsRespDTO> transferRechargeQuery(SpaTradeDetailsReqDTO reqDTO);

    /**
     * 申请修改手机号码	ApplyForChangeOfCellPhoneNum	用于有需要进行短信动态码验证的平台，申请修改会员手机号码。
     *
     * @param reqDTO
     * @return
     */
    SpaPersonAcctRespBaseDto<SpaApplyForChangeOfCellPhoneNumRespDTO> applyForChangeOfCellPhoneNum(SpaApplyForChangeOfCellPhoneNumReqDTO reqDTO);

    /**
     * 回填动态码-修改手机	BackfillDynamicPassword	用于申请修改手机号码的会员，回填手机动态码发送至银行。
     *
     * @param reqDTO
     * @return
     */
    SpaPersonAcctRespBaseDto<SpaBackFillDynamicPasswordRespDTO> backFillDynamicPassword(SpaBackFillDynamicPasswordReqDTO reqDTO);

    /**
     *
     * @param start 开始时间
     * @param end 结束时间
     * @param type 类型
     * @return 更新条数Integer
     */
    Integer registerCheck(Date start, Date end, Integer type);

}
