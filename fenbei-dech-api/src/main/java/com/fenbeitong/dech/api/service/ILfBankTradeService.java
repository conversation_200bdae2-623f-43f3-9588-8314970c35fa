package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.BankFreezeOrThawCapitalReqDto;
import com.fenbeitong.dech.api.model.dto.BankFreezeOrThawCapitalRespDto;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-06-22 17:16:11
 * @Version 1.0
 **/
public interface ILfBankTradeService {

    /**
     * 资金冻结/解冻
     * @param reqDto
     */
    BankFreezeOrThawCapitalRespDto freezeOrThawCapital(BankFreezeOrThawCapitalReqDto reqDto);

}
