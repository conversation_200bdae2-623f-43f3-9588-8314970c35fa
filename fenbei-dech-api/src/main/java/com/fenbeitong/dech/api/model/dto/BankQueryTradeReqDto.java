package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/2/27
 * @Description
 */
@SuppressWarnings("serial")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankQueryTradeReqDto implements Serializable {

    /**
     * 分贝通订单号
     */
    @NotNull
    private String fbOrderId;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 银行名称
     */
    @NotNull
    private String bankName;

    /**
     * 请求流水号
     */
    private String seqNo;

    /**
     * 账户号
     */
    @NotNull
    private String accountNo;

    /**
     * 交易时间
     */
    @NotNull
    private Date tradeDate;

    /**
     * 银行流水号
     */
    private String bankTransNo;
}
