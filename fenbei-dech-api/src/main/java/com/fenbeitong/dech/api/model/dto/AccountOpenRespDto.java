package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName AccountOpenRespDto
 * @Description: 虚拟卡账户开户RPC返回DTO
 * <AUTHOR>
 * @Date 2021/3/8
 **/
@SuppressWarnings("serial")
@Data
public class AccountOpenRespDto implements Serializable {


    /**
     * 系统订单号
     */
    private String sysOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 开户状态
     */
    private String openStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     *绑定账户 ID
     */
    private String bandAccountId;

    /**
     *二类户账号
     */
    private String bankAccountNo;

    /**
     * 银行虚户ID
     */
    private String bankAcctId;

}
