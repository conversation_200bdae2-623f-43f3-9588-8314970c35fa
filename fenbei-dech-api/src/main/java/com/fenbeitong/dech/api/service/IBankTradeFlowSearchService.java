package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

/**
 * <AUTHOR>
 * @Date 2021/6/25
 * @Description
 */
public interface IBankTradeFlowSearchService {


    /**
     * 查询交易流水
     *
     * @param bankTransNo
     * @return
     */
    BankFlowRespDto queryTradeFlow(String bankTransNo);

    /**
     * 查询出金流水
     *
     * @param bankTransNo
     * @return
     */
    BankFlowRespDto queryCashOutFlow(String bankTransNo);

    /**
     * 查询交易流水
     *
     * @param txnId
     * @return
     */
    BankFlowRespDto queryTradeFlowByTxnId(String txnId);

    /**
     * 查询出金流水
     *
     * @param txnId
     * @return
     */
    BankFlowRespDto queryCashOutFlowByTxnId(String txnId);

    /**
     * 查询出金流水
     *
     * @param bassId
     * @return
     */
    BankFlowRespDto queryCashOutFlowByBassId(String bassId);

    /**
     * 查询出金流水
     *
     * @param txnId
     * @return
     */
    BankFlowRespDto getSuccessCashFlowByTxnId(String txnId);

    /**
     * 查询交易流水
     *
     * @param bassTxnId
     * @return
     */
    BankFlowRespDto getFlowByBassTxnId(String bassTxnId);

    /**
     * 查询出金流水
     *
     * @param bankName
     * @return bankTellerTransNo
     */
    BankFlowRespDto queryCashOutFlowByBankNameAndTellerTransNo(String bankName, String bankTellerTransNo);

}
