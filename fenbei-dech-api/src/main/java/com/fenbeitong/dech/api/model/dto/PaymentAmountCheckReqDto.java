package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/2/23
 * @Description 打款金额验证
 */
@Data
public class PaymentAmountCheckReqDto implements Serializable {


    /**
     * 绑定账户
     */
    @NotBlank
    private String bindAccount;

    /**
     * 认证金额 分
     */
    @NotBlank
    private String amount;

    /**
     * 银行编码
     */
    @NotBlank
    private String bankName;

    /**
     * 账户名称：同企业名称
     */
    private String accountName;

    /**
     * 资料提交时间：注册时间
     */
    private Date submitTime;

    /**
     * 短信验证码或者短信序列号
     */
    private String smsNo;

    /**
     * 绑定卡信息
     */
    private String bankCardNo;}
