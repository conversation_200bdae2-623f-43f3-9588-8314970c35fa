package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName MemberBindQueryRespDTO
 * @Description: KFEJZB6098    会员绑定信息查询	MemberBindQuery
 * <AUTHOR>
 * @Date 2022/08/01
 **/
@Data
public class BindQueryItemDto implements Serializable {


    /*
     * 	资金汇总账号	string(32)
     */
    private String fundSummaryAcctNo;

    /*
     * 	见证子账户的账号	string(32)
     */
    private String subAcctNo;

    /*
     * 	交易网会员代码	string(32)
     */
    private String tranNetMemberCode;

    /*
     * 	会员名称	string(120)
     */
    private String memberName;

    /*
     * 会员证件类型	string(2)	见“会员接口证件类型说明”例如身份证，送1。
     */
    private String memberGlobalType;

    /*
     *  会员证件号码	string(20)
     */
    private String memberGlobalId;

    /*
     * 	会员绑定账户的账号	string(32)		提现的银行卡
     */
    private String memberAcctNo;

    /*
     * 	会员绑定账户的本他行类型	string(1)		1：本行 2：他行
     */
    private String bankType;

    /*
     * 会员绑定账户的开户行名称	string(120)
     */
    private String acctOpenBranchName;

    /*
     * 	会员绑定账户的开户行的联行号	string(14)
     */
    private String cnapsBranchId;

    /*
     * 	会员绑定账户的开户行的超级网银行号	string(14)
     */
    private String eiconBankBranchId;

    /*
     * 	会员的手机号	string(12)
     */
    private String mobile;


}
