package com.fenbeitong.dech.api.service.cmb;

import com.fenbeitong.dech.api.model.dto.cmb.*;


/**
 * 招行账户接口
 * <AUTHOR>
 */
public interface ICmbBankEntAccountService {

    /**
     * 批量 查询账户余额
     *
     * @param reqDTO
     * @return
     */
    AcctBalanceModelRpcRespDTO queryAccountBalance(CmbBankEntAcctBalanceRpcReqDTO reqDTO);


    /**
     * 客户银行账户信息查询
     */
    CustomInfoRpcResDTO queryAcctOpenResult(CmbBankEntAcctOpenResultRpcReqDTO ufidaAcctResultQueryDTO);

    /**
     * 账号支行信息查询
     */
    AcctBankBranchNoResDTO queryAcctBankBranchNo(CmbBankEntAcctBankBranNoRpcReqDTO ufidaAcctResultQueryDTO);

    /**
     * 业务模式
     */
    HandBusiModelRpcResDTO queryHandBusiModel(HandBusiModelRpcReqDTO rpcReqDTO);
}
