package com.fenbeitong.dech.api.model.dto.airwallex;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by yang.Li on 2023/3/29.
 */
@Data
public class AirUpdateCardRpcReqDTO implements Serializable {

    //授权控制
    private BaseAirwallexRpcDTO.AuthorizationControls authorization_controls;
    /**
     * 卡状态
     */
    private String card_status;
    /**
     * 昵称
     */
    private String nick_name;
    /**
     * 主要联系人详细信息
     */
    private BaseAirwallexRpcDTO.PrimaryContactDetails primary_contact_details;
    /**
     * 目的用途
     */
    private String purpose;

}
