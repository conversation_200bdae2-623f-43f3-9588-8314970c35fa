package com.fenbeitong.dech.api.service.spabank;

import com.fenbeitong.dech.api.model.dto.spabank.req.*;
import com.fenbeitong.dech.api.model.dto.spabank.resp.*;

/**
 * @description: 平安个人见证账户操作接口
 * @author: yanqiu.hu
 * @create: 2022-08-11 19:05:45
 * @Version 1.0
 **/
public interface ISpaPersonSubAccountOperationService {

    /**
     * 创建平安个人见证子账户
     *
     * @param spaPersonSubAccountReqDTO
     * @return
     */
    Boolean createSubAccount(SpaPersonSubAccountReqDTO spaPersonSubAccountReqDTO);

    /**
     * 更新平安个人见证子账户
     *
     * @param spaPersonSubAccountReqDTO
     * @return
     */
    Boolean updateBySubAccountId(SpaPersonSubAccountReqDTO spaPersonSubAccountReqDTO);

    /**
     * 创建平安见证子账户流水
     *
     * @param spaPersonSubAccountFlowReqDTO
     * @return
     */
    Boolean createSubAccountFlow(SpaPersonSubAccountFlowReqDTO spaPersonSubAccountFlowReqDTO);

    /**
     * 更新平安见证子账户流水
     *
     * @param spaPersonSubAccountFlowReqDTO
     * @return
     */
    Boolean updateSubAccountFlow(SpaPersonSubAccountFlowReqDTO spaPersonSubAccountFlowReqDTO);

    /**
     * 更新平安见证子账户流水
     *
     * @param spaPersonSubAccountFlowReqDTO
     * @return
     */
    Boolean updateSubAccountFlowById(SpaPersonSubAccountFlowReqDTO spaPersonSubAccountFlowReqDTO);

    /**
     * 报销打款交易-个人见证账户转入
     * @param reqDto
     * @return
     */
    SpaPersonSubAccountFlowRespDto transferInto(SpaPersonSubAccountTransferReqDTO reqDto);

    /**
     * 报销打款交易-个人见证账户转出提现
     * @param reqDto
     * @return
     */
    SpaPersonSubAccountFlowRespDto transferOutWithdrawal(SpaPersonSubAccountTransferReqDTO reqDto);

    /**
     * 报销打款交易退款-个人见证账户转出到企业见证
     * @param reqDto
     * @return
     */
    SpaPersonSubAccountFlowRespDto transferOutTransfer2Company(SpaPersonSubAccountTransferReqDTO reqDto);

    /**
     * 平安虚拟卡额度退回-个人见证账户转出到企业见证
     * @param transferOutDTO
     * @return
     */
    SpaPersonSubAccountFlowRespDto transferOutPerson2CompanySubAccount(SpaPersonSubAccountTransferReqDTO transferOutDTO);

    /**
     * 平安虚拟卡额度退回-个人见证账户转出到个人卡
     * @param transferOutDTO
     * @return
     */
    SpaPersonSubAccountFlowRespDto transferOutTransfer2SecondCard(SpaPersonSubAccountTransferReqDTO transferOutDTO);
}
