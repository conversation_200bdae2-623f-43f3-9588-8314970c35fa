package com.fenbeitong.dech.api.model.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CloudPayStandardResponse implements Serializable {

    @ApiModelProperty(value = "银行侧响应code")
    private String respCode;

    @ApiModelProperty(value = "银行侧响应报文")
    private String respMsg;

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }
}
