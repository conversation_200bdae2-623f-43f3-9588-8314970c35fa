package com.fenbeitong.dech.api.model.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/23
 * @Description 重新打款
 */
@Data
@Builder
public class PaymentCheckTryRespDto implements Serializable {

    private String bankName;

    /**
     * 绑定账户
     */
    private String bindAccount;

    /**
     * 分贝通订单号
     */
    private String ordNo;

    /**
     * 打款验证是否成功 true 成功
     */
    private Boolean validSuccess;

    /**
     * 验证状态
     */
    private String validStatus;

    /**
     * 验证失败原因
     */
    private String failReason;

    /**
     * TODO
     */
    private String frontSeqNo;


    public static PaymentCheckTryRespDto ofSuccess(String bankNo, String bankName, String ordNo, String frontSeqNo) {
        PaymentCheckTryRespDto respDto = builder().validSuccess(Boolean.TRUE).bindAccount(bankNo).bankName(bankName).ordNo(ordNo).frontSeqNo(frontSeqNo).build();
        return respDto;
    }

    public static PaymentCheckTryRespDto ofFail(String bankNo, String bankName, String ordNo, String failReason) {
        PaymentCheckTryRespDto respDto = builder().validSuccess(Boolean.FALSE).bindAccount(bankNo).bankName(bankName).ordNo(ordNo).failReason(failReason).build();
        return respDto;
    }

    public static PaymentCheckTryRespDto ofFail(String bankName, String ordNo, String failReason) {
        PaymentCheckTryRespDto respDto = builder().validSuccess(Boolean.FALSE).bindAccount("").bankName(bankName).ordNo(ordNo).failReason(failReason).build();
        return respDto;
    }
}
