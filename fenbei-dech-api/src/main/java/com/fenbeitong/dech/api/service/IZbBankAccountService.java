package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;
import com.fenbeitong.dech.api.model.dto.zbbank.req.ZbRechargeQueryReq;
import com.fenbeitong.dech.api.model.dto.zbbank.resp.ZbRechargeRecordRespDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/19
 * @Description
 */
public interface IZbBankAccountService {

    /**
     * 众邦明细查询
     */
    List<ZbQueryCashInListRespDTO> queryCashInDetail(ZbQueryCashInListReqDTO reqDTO);

    /**
     * 退款交易信息查询
     *
     * @return
     */
    BankQueryTradeRespDto queryRefundTradeInfo(BankQueryTradeReqDto reqDto);


    List<ZbRechargeRecordRespDto> queryRechargeRecord(ZbRechargeQueryReq req);

}
