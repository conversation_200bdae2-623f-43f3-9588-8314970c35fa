package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BankEntQueryTradeRespDTO implements Serializable {

    /**
     * 分贝通流水号
     */
    private String tradeFlowId;

    private String tradeBatNo;

    /**
     * 请求银行的流水号
     */
    private String syncBankTransNo;

    /**
     * 银行流水号
     */
    private String bankTransNo;

    /**
     * 交易状态
     */
    private String txnStatus;

    /**
     * 失败原因
     */
    private String failMsg;

    /**
     * 交易金额
     */
    private BigDecimal operationAmount;

    /**
     * 付款方账户
     */
    private String payAccountNo;

    /**
     * 付款方账户名
     */
    private String payAccountName;

    /**
     * 收款方账户
     */
    private String receiveAccountNo;

    /**
     * 收款方账户名
     */
    private String receiveAccountName;

    /**
     * 退汇对账码
     */
    private String paybackCheckCode;

    /**
     * 银行明细对账编号
     */
    private String bankCheckCode;

    /**
     * 用途
     */
    private String useDesc;

    /**
     * 附言
     */
    private String remark;
}
