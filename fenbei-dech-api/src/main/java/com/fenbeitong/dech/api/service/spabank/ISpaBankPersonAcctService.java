package com.fenbeitong.dech.api.service.spabank;


import com.fenbeitong.dech.api.model.dto.spabank.req.*;
import com.fenbeitong.dech.api.model.dto.spabank.resp.*;

/**
 * @ClassName ISpaBankPersonCardService
 * @Description: 平安银行个人开户
 * <AUTHOR>
 * @Date 2022/08/10
 **/
public interface ISpaBankPersonAcctService {

    /**
     * 开户前置校验
     */
    SpaPersonAcctRespBaseDto<SpaOpenAcctPreRespDto> openAccountPre(SpaOpenAcctPreReqDto reqDto);

    /**
     * 开户前置校验职业家庭地址接口
     */
    SpaPersonAcctRespBaseDto<SpaCheckUserInfoRespDto> checkUserInfo(SpaCheckUserInfoReqDto reqDto);

    /**
     * 上传身份证正反面文件
     */
    SpaPersonAcctRespBaseDto<SpaUploadFileRespDto> uploadFile(SpaUploadFileReqDto reqDto);

    /**
     * 影像审核
     */
    SpaPersonAcctRespBaseDto<SpaSubmitImageRespDto> submitImage(SpaSubmitImageReqDto reqDto);

    /**
     * 联动账户-发送otp
     */
    SpaPersonAcctRespBaseDto<SpaLinkedSendOtpRespDto> linkedSendOtp(SpaLinkedSendOtpReqDto reqDto);

    /**
     * II类户III类户联动开户
     */
    SpaPersonAcctRespBaseDto<SpaOpenLinkedAccountRespDto> openLinkedAccount(SpaOpenLinkedAccountReqDto reqDto);

    /**
     * 查询II类户III类户联动开户状态
     */
    SpaPersonAcctRespBaseDto<SpaQueryOpenLinkedAcctStatusRespDto> queryOpenLinkedAcctStatus(SpaQueryOpenLinkedAcctStatusReqDto reqDto);

    /**
     * 开通个人见证账户
     * I类户：开通见证子账户+绑定I类户
     * II类户：开通见证子账户+绑定II类户+后续
     */
    SpaPersonAcctRespBaseDto<SpaOpenPersonAccountRespDto> openPersonAccount(SpaOpenPersonAccountReqDto reqDto);

    /**
     * I类户：验证码校验
     */
    SpaPersonAcctRespBaseDto<SpaBindCardCheckMsgRespDto> bindCardCheckMsg(SpaBindCardCheckMsgReqDto reqDto);

    /**
     * 发送验证码
     */
    SpaPersonAcctRespBaseDto<SpaSendOtpRespDto> sendOtp(SpaSendOtpReqDto reqDto);

    /**
     * 查询卡余额
     */
    SpaPersonAcctRespBaseDto<SpaQueryCardBalanceRespDto> queryCardBalance(SpaQueryCardBalanceReqDto reqDto);

    /**
     * 平安二类户提现到绑定的他行一类户
     */
    SpaPersonAcctRespBaseDto<SpaToTransferWithOtpRespDto> toTransferWithOtp(SpaToTransferWithOtpReqDto reqDto);

    /**
     * 代扣前置检查
     */
    SpaPersonAcctRespBaseDto<TransferInPreCheckRespDto> transferInPreCheck(TransferInPreCheckReqDto reqDto);

    /**
     * 代扣签约
     */
    SpaPersonAcctRespBaseDto<TransferInApplySignRespDto> transferInApplySign(TransferInApplySignReqDto reqDto);

    /**
     *  代扣签约验证
     */
    SpaPersonAcctRespBaseDto<TransferInApplySignVerifyRespDto> transferInApplySignVerify(TransferInApplySignVerifyReqDto reqDto);

    /**
     *  代扣签约查询
     */
    SpaPersonAcctRespBaseDto<QueryTransferInApplySignResultSmartRespDto> queryTransferInApplySignResultSmart(QueryTransferInApplySignResultSmartReqDto reqDto);

    /**
     * 代扣银行卡限额查询
     */
    SpaPersonAcctRespBaseDto<QueryBankTransferLimitSmartRespDto> queryBankTransferLimitSmart(QueryBankTransferLimitSmartReqDto reqDto);

    /**
     * 代扣
     */
    SpaPersonAcctRespBaseDto<TransferInRespDto> transferIn(TransferInReqDto reqDto);

    /**
     *  代扣结果查询
     */
    SpaPersonAcctRespBaseDto<TransferInResultQueryRespDto> transferInResultQuery(TransferInResultQueryReqDto reqDto);

    /**
     * 出入金交易明细查询
     * 场景：出入金交易明细接口。会返回转入（代扣）、跨行转账（绑定卡转账到二类户卡）、网银转账（二类户卡转账到绑定卡）、冲销（二类户卡转账到绑定卡交易失败的数据）、产品认购（购买）、产品回款（赎回）、利息收入。测试环境查询历史交易明细数据请联系银行同步交易明细数据，查当天交易明细数据需要联系银行修改服务器时间
     */
    SpaPersonAcctRespBaseDto<QryTransListOutLoginRespDto> qryTransListOutLogin(QryTransListOutLoginReqDto reqDto);

    /**
     * 获取腾讯H5人脸识别流水号
     */
    SpaPersonAcctRespBaseDto<GetH5FaceIdRespDto> getH5FaceId(GetH5FaceIdReqDto reqDto);

    /**
     * 获取腾讯h5人脸比对结果
     */
    SpaPersonAcctRespBaseDto<GetH5AuthResultRespDto> getH5AuthResult(GetH5AuthResultReqDto reqDto);

    /**
     * 申请划扣二三类户资金至平台监管户
     */
    SpaPersonAcctRespBaseDto<ApplyAccountRefundRespDto> applyAccountRefund(ApplyAccountRefundReqDto applyAccountRefundReqDto);

    /**
     * 查询交易明细
     */
    SpaPersonAcctRespBaseDto<QueryTransactionDetailRespDto> queryTransactionDetail(QueryTransactionDetailReqDto reqDto);

    /**
     * 联动账户信息查询
     */
    SpaPersonAcctRespBaseDto<QueryLinkedAcctDetailRpcDTO> queryLinkedAcct(QueryLinkedAcctRpcReqDTO reqDto);

    /**
     * 签约结果查询
     */
    SpaPersonAcctRespBaseDto<SpaEJZBCustInformationQueryRespDTO> eJZBCustInformationQuery(SpaEJZBCustInformationQueryReqDTO ejzbCustInformationQueryReqDTO);

}
