package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName BankDownloadBillRespDto
 * @Description: 电子账簿返回DTO
 * <AUTHOR>
 * @Date 2021/3/12
 **/
@Data
public class BankAcctBookRespDto implements Serializable {

    /**
     * 电子账簿 Id
     */
    private String fctId;

    /*
    可提现余额
     */
    private String drawableBalance;

    /*
    在途余额
     */
    private String onOrderBalance;

    /*
    不可用余额
     */
    private String unUseBalance;

    /*
    是否被冻结
     */
    private boolean frozenStatus;

    /*
    备注
     */
    private String remark;
}
