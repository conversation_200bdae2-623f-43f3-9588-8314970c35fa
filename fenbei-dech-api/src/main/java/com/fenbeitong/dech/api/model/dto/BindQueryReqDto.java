package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 绑定查询请求入参
 * @author: yanqiu.hu
 * @create: 2022-09-15 20:44:26
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindQueryReqDto implements Serializable {

    /*
     * 1：全部会员
     * 2：单个会员
     */
    private String queryFlag;

    /*
     * 子账户账号	Y 32
     * 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”(右侧)进行分隔。
     */
    private String subAcctNo;

    /*
     * 页码
     */
    private String pageNum;

}
