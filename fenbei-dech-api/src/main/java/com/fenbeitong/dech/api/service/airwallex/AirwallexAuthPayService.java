package com.fenbeitong.dech.api.service.airwallex;

import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AirwallexAcctDetailDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthPayRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthPayRespDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.AuthResultDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.QueryAccountBalanceRequestDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.authpay.QueryAccountBalanceRespDTO;

/**
 * Airwallex授权账户支付服务
 */
public interface AirwallexAuthPayService {

	/**
	 * 授权
	 * @param param
	 * @return
	 */
	AuthResultDTO authorize(AuthRequestDTO request);
	
	/**
	 * 查询授权结果
	 * @param request
	 * @return
	 */
	AuthResultDTO queryAuthResult(AuthRequestDTO request);
		
	/**
	 * 同步支付
	 */
	AuthPayRespDTO syncPay(AuthPayRequestDTO payRequest);
	
	/**
	 * 异步支付
	 */
	AuthPayRespDTO asyncPay(AuthPayRequestDTO payRequest);
	
	/**
	 * 退款
	 * @param payRequest
	 * @return
	 */
	AuthPayRespDTO refund(AuthPayRequestDTO payRequest);
	
	/**
	 * 查询支付结果 传requestId即可
	 * @return
	 */
	AuthPayRespDTO queryPayResult(AuthPayRequestDTO payRequest);
	
	/**
	 * 查询账户余额
	 * @param request
	 * @return
	 */
	QueryAccountBalanceRespDTO queryAWAccountBalance(QueryAccountBalanceRequestDTO request);
	
	/**
	 * 查询Airwa账户详情
	 * @param companyId
	 * @return
	 */
	AirwallexAcctDetailDTO queryAWAccountDetail(String companyId);
}
