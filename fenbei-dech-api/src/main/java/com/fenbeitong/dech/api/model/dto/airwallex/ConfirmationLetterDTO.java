package com.fenbeitong.dech.api.model.dto.airwallex;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**  
 * @Description: 
 * <AUTHOR>
 * @date 2023年6月28日 
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfirmationLetterDTO implements Serializable {

    private static final long serialVersionUID = -8737619330975813233L;

    /**
     * 1、待结算账户 2、结算账户
     */
    @NotNull(message = "账户类型不能为空")
    private Integer acctType;
    
    /**
     * STANDARD - this format includes fee info.
     * NO_FEE_DISPLAY - this format won't display fee info.
     */
    @Builder.Default
    private String format = "STANDARD";
    
    /**
     * airwallex的交易流水号
     */
    @JSONField(name = "transaction_id")
    @JsonProperty("transaction_id")
    @NotNull(message = "交易流水号不能为空")
    private String transactionId;
    
    /**
     * 确认函下载地址
     */
    private String letterUrl;
}
