package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.ufida.*;

import java.util.List;

/**
 * 用友账户接口
 * <AUTHOR>
 */
public interface IUfidaAccountService {

    /**
     * 4.1.1.12客户银行账户信息查询：01a21a001
     * @param ufidaAccountBalanceReqDTO
     * @return
     */
    UfidaAccountBalanceRespDTO queryAccountBalance(UfidaAccountBalanceReqDTO ufidaAccountBalanceReqDTO);

    /**
     * 获取支持银行列表 01d11a004
     */
    List<BankInfoDTO> querySupportBank(String custNo);

    /**
     * 开户申请 01a11a002
     * @param ufidaCreateAccountReqDTO
     * @return
     */
    UfidaCreateAccountRespDTO createAccount(UfidaCreateAccountReqDTO ufidaCreateAccountReqDTO);

    /**
     * 客户银行账户信息查询：01a21a001
     */
    UfidaAcctOpenResultRespDTO queryAcctOpenResult(UfidaAcctResultQueryDTO ufidaAcctResultQueryDTO);
}
