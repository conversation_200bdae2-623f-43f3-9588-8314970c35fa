package com.fenbeitong.dech.api.model.dto.spdcloud.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class SpdbQueryBalanceRpcReqDTO implements Serializable {

    /**
     *统一社会信用代码
     */
    private String unfSocCrdtNo ;

    /**
     *二级平台ID
     */
    private String SAASId ;

    /**
     *二级平台名称
     */
    private String SAASName ;

    /**
     *备用字段1
     */
    private String reserve1 ;

    private List<SpdbSignAcctReqDTO> singAccts ;

    @Data
    public static class SpdbSignAcctReqDTO implements Serializable {

        /**
         * 卡号
         */
        private String acctNo;

        private String unitNo;

    }

}
