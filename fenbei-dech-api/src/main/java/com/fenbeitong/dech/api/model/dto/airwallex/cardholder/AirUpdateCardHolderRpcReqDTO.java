package com.fenbeitong.dech.api.model.dto.airwallex.cardholder;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Created by FBT on 2023/3/29.
 */
@Data
public class AirUpdateCardHolderRpcReqDTO extends AirCreateCardHolderRpcReqDTO {

    private static final long serialVersionUID = -713008291092372609L;

    /**
     * 持卡人的唯一标识符
     */
    private String cardholder_id;


}
