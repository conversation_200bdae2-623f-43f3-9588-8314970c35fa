package com.fenbeitong.dech.api.model.dto.airwallex.withdraw;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 
 * @Description: 
 * <AUTHOR>
 * @date 2023年8月8日
 */
@SuppressWarnings("serial")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalInfoDTO implements Serializable {

    /**
     * 个人电子邮件
     */
    private String personalEmail;

    /**
     * 企业区域
     */
    private	String businessArea;

    /**
     * 企业电话号码
     */
    private	String businessPhoneNumber;

    /**
     * 企业注册号
     */
    private	String businessRegistrationNumber;

    /**
     * 在当地政府机构注册的企业类型
     */
    private	String	businessRegistrationType;

    /**
     * 法定代表人的个人银行账号，用于申报目的。任何资金都不会被汇入该账号。
     */
    private	String legalRepBankAccountNumber;

    /**
     * 法定代表人中文名
     */
    private	String	legalRepFirstNameInChinese;

    /**
     * 法定代表人身份证号码
     */
    private	String	legalRepIdNumber;

    /**
     * 法定代表人中文姓氏
     */
    private	String	legalRepLastNameInChinese;

    /**
     * 法定代表人与其银行账户关联的手机号码。
     */
    private	String legalRepMobileNumber;

    /**
     * 受益人的中文名字
     */
    private	String	personalFirstNameInChinese;

    /**
     * 个人身份证号码
     */
    private	String	personalIdNumber;

    /**
     * 个人证件类型
     */
    private	String	personalIdType;

    /**
     * 受益人的中文姓氏
     */
    private	String	personalLastNameInChinese;

    /**
     * 个人手机号码
     */
    private	String	personalMobileNumber;

    /**
     * 公司人员的唯一ID
     */
    private String externalId;
}
