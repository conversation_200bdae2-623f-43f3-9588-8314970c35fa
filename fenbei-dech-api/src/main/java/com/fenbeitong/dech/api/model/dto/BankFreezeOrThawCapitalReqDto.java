package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-06-21 15:22:12
 * @Version 1.0
 **/
@Data
public class BankFreezeOrThawCapitalReqDto implements Serializable {
    /**
     * 对接银行流水号
     */
    private Long orderid;
    /**
     * 银行
     */
    private String bankName;
    /**
     * 会员通记账簿	Number(15)	Y	用户在会员通系统的唯一标识
     */
    private Long userid;
    /**
     * 冻结（解冻）原因	Number(6)	Y	50 企业提现冻结； 51 企业提现解冻；
     *
     * @see com.fenbeitong.dech.api.enums.LfFzTransCodeEnum
     */
    private Long fztranscode;
    /**
     * 冻结（解冻）金额	Number(15)	Y	金额单位：分
     */
    private Long transamt;
    /**
     * 提现手续费	Number(15)	N	提现用户需支付的手续费,
     * 单位：分。提现交易必填
     */
    private Long transfee;
    /**
     * 原冻结编号	Number(17)	Y	原冻结受理编号，冻结固定值（0）
     */
    private Long oldtransid;
    /**
     * 短信请求码	Number(20)	N	资金解冻不填
     */
    private Long smsid;
    /**
     * 短信验证码	Number(6)	N	资金解冻不填
     */
    private Long validatemsg;
    /**
     * 私有域	String(100)	N	原样返回
     */
    private String userpriv;
}
