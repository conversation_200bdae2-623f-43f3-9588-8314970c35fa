package com.fenbeitong.dech.api.service.spdcloud;

import com.fenbeitong.dech.api.model.dto.spdcloud.req.SpdSignQueryRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.spdcloud.resp.SpdSignQueryRpcRespDTO;

import java.util.List;


/**
 * 浦发云直连银行账户
 */
public interface ISpdCloudAccountService {

    List<SpdSignQueryRpcRespDTO> querySign(String unfSocCrdtNo , SpdSignQueryRpcReqDTO spdbSignQueryReqDTO);

}
