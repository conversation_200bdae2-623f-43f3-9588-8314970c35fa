package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

/**
 * @Author: liuzhitao
 * @Date: 2021/3/2 20:05
 * @Description:电子回单查询
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
@Data
public class BankTradeReceiptQryReqDto implements Serializable {

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 企业账户id
     */
    private String companyAccountId;
    
    private String mchId;

    /**
     * 分贝订单id
     */
    private String fbOrderId;

    /**
     * 银行订单id
     */
    private String bankOrderId;

    /**
     * 交易类型 1 充值 2 提现 3 转账
     */
    private String txnType;

    /**
     * 交易成功时间YYYYMMdd
     */
    private Date tradeTime;

    /**
     * 银行交易流水号
     */
    private String bankTransNo;

    public boolean isSysOrdNo(){
        return StringUtils.isNotBlank(bankTransNo);
    }

}
