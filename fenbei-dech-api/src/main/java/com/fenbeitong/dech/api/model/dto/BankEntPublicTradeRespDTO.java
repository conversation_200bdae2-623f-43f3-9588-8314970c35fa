package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class BankEntPublicTradeRespDTO extends BankEntBaseTradeRespDTO {

    /**
     * 是否有风险
     */
    private Boolean riskFlag;

    /**
     * 风险等级 00无风险 01-低风险 02-高风险 110008-疑似重复已拦截
     */
    private String riskLevel;

    /**
     * 风险提示信息
     */
    private String riskMsg;

    /**
     * 退汇对账码
     */
    private String paybackCheckCode;

    /**
     * 银行明细对账编号
     */
    private String bankCheckCode;

}
