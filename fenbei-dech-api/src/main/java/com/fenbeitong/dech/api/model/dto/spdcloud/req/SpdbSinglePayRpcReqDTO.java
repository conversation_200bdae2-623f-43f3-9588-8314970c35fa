package com.fenbeitong.dech.api.model.dto.spdcloud.req;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Data
public class SpdbSinglePayRpcReqDTO implements Serializable {
    /**
     * 统一社会信用代码
     */
    private String unfSocCrdtNo;

    /**
     * 电子凭证号
     */
    private String elecChequeNo;
    /**
     * 付款账号
     */
    private String acctNo;
    /**
     * 付款人账户名称
     */
    private String acctName;
    /**
     * 收款人账号
     */
    private String payeeAcctNo;
    /**
     * 收款人名称
     */
    private String payeeName;
    /**
     * 收款人账户类型
     * 0-公司账号；1-东方卡；2-活期一卡通；3-个人帐号；4-银行卡；5-单位结算卡；6-虚账户
     *当收款人为本行个人账户时必须填写此项
     */
    private String payeeType;
    /**
     * 支付金额
     */
    private String amount;
    /**
     * 备注 N
     */
    private String note;
    /**
     * 支付用途 N
     */
    private String payPurpose;
    /**
     * 收款行行号 N   跨行必输
     */
    private String payeeBankNo;
    /**
     * 收款行速选标志
     * 1-速选
     当本行/他行标志为“1”（他行）时才能生效。
     如果希望跨行汇款自动处理，请务必填写此项。
     */
    private String payeeBankSelectFlag;
    /**
     * 本他行标志
     * 必输
     *0-本行（行内转账）
     *1-他行（跨行转账）
     */
    private String sysFlag;
    /**
     * 同步异步标志
     * 0-同步；1-异步
     不送则默认异步
     */
    private String synFlag;
    /**
     * 收款行名称
     */
    private String payeeBankName;
    /**
     * 二级平台ID
     */
    private String SAASId;
    /**
     * 二级平台名称
     */
    private String SAASName;



}
