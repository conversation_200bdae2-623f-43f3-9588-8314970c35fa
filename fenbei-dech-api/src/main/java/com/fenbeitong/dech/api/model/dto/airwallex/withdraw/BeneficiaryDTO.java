package com.fenbeitong.dech.api.model.dto.airwallex.withdraw;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @Description: 
 * <AUTHOR>
 * @date 2023年8月8日
 */
@SuppressWarnings("serial")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BeneficiaryDTO implements Serializable {

    /**
     * 关于收款方的其他信息
     */
    private AdditionalInfoDTO additionalInfo;

    /**
     * 收款方地址信息
     */
    private AddressDTO address;

    /**
     * 收款方的银行账户详细信息，付款将记入其中
     */
    private BankAccountDTO bankDetails;

    /**
     * 受益人的公司名称
     */
    private String companyName;

    /**
     * 受益人出生日期
     * 应为有效日期，格式为ISO8601日期格式
     */
    private String dateOfBirth;

    /**
     * 受益人的实体类型
     */
    private String entityType;

    /**
     * 受益人的名字
     * 1-100个字符长
     * 不应包含表情符号
     */
    private String firstName;

    /**
     * 受益人的姓氏
     * 1-100个字符长
     * 不应包含表情符号
     */
    private String lastName;
}
