package com.fenbeitong.dech.api.model.dto.spdcloud.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;


@Data
public class SpdbQueryTradeListRpcReqDTO implements Serializable {

    /**
     *统一社会信用代码
     */
    private String unfSocCrdtNo ;

    /**
     *账号
     */
    private String acctNo ;
    private String dateBeginDate ;
    private String dateEndDate ;
    /**
     * 对方帐号
     */
    private String subAccount ;
    /**
     * 对方名称（户名）
     */
    private String oppositeName ;
    /**
     * 交易金额
     */
    private String transAmount ;
    /**
     * 查询笔数
     */
    private String queryNumber ;

    /**
     * 起始笔数
     */
    private String beginNumber ;

    /**
     *二级平台ID
     */
    private String SAASId ;

    /**
     *二级平台名称
     */
    private String SAASName ;

    /**
     *备用字段1
     */
    private String reserve1 ;

    /**
     *备用字段1
     */
    private String reserve2 ;

    /**
     *备用字段1
     */
    private String reserve3 ;


}
