package com.fenbeitong.dech.api.model.dto;

import java.io.Serializable;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder.Default;

/**
 * @ClassName ResponseDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/8/2 9:26 下午
 * @Version 1.0
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("serial")
public class ResponseDTO<T> implements Serializable {

    /**
     * 返回码
     */
    @Default
    private String code = "success";

    /**
     * 数据信息
     */
    private T data;

    /**
     * 返回信息
     */
    @Default
    private String message = "success";
    
    /**
     * 异常返回时有值，用于判断异常根因
     */
    private String source;

    private String type;

    private Integer level;
    
    @SuppressWarnings("unchecked")
    public static <T> ResponseDTO<T> success(T data) {
        return (ResponseDTO<T>)ResponseDTO.builder().data(data).build();
    }
    
    public static <T> ResponseDTO<T> failure(String code, String message) {
        return failure(code, message, StringUtils.EMPTY);
    }
    
    @SuppressWarnings("unchecked")
    public static <T> ResponseDTO<T> failure(String code, String message, String source) {
        return (ResponseDTO<T>)ResponseDTO.builder().code(code).message(message).source(source).build();
    }
    
    public boolean isSuccessful() {
        return Objects.nonNull(data);
    }
}
