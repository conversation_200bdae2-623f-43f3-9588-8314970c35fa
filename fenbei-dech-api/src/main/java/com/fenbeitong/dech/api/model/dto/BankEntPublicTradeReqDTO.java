package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

@Data
public class BankEntPublicTradeReqDTO extends BankEntBaseTradeReqDTO {

    /**
     * 跨行标记
     */
    private String toBankType;

    /**
     * 是否需要风控
     */
    private Boolean needRisk;

    /**
     * 客户号(用友传传,对应招商银行uid)
     */
    private String custNo;

    /**
     * 收方开户行联行号
     */
    private String toBankNo;

    /**
     * 收方开户行名
     */
    private String toBrchName;

    /**
     * 用途
     */
    private String useDesc;

    /**
     * 附言
     */
    private String remark;

}
