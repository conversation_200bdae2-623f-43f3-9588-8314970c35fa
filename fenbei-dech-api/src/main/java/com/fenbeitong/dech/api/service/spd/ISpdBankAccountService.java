package com.fenbeitong.dech.api.service.spd;

import com.fenbeitong.dech.api.model.dto.BankQueryTradeReqDto;
import com.fenbeitong.dech.api.model.dto.BankQueryTradeRespDto;
import com.fenbeitong.dech.api.model.dto.spd.SpdClearingsReqDTO;
import com.fenbeitong.dech.api.model.dto.spd.SpdClearingsRespDTO;

import java.util.Map;

public interface ISpdBankAccountService {

    /**
     * @MethodName: transferVerify
     * @Description: 线下充值通知验签
     * @Param: [params]
     * @Return: boolean
     * @Author: Jarvis.li
     * @Date: 2023/07/17
     */
    boolean transferVerify(Map<String, String> params);

    /**
     * @MethodName: unknownClearings
     * @Description: 不明入金清分
     * @Param: [params]
     * @Return: SpdClearingsRespDTO
     * @Author: Jarvis.li
     * @Date: 2023/07/17
     */
    SpdClearingsRespDTO unknownClearings(SpdClearingsReqDTO spdClearingsReqDTO);

    /**
     * @MethodName: queryClearings
     * @Description: 查询不明入金清分结果
     * @Param: [params]
     * @Return: SpdClearingsRespDTO
     * @Author: Jarvis.li
     * @Date: 2023/07/17
     */
    SpdClearingsRespDTO queryClearings(SpdClearingsReqDTO spdClearingsReqDTO);
    
    /**
     * 查询出金交易详情
     * @param reqDto
     * @return
     */
    BankQueryTradeRespDto queryCashOutInfo(BankQueryTradeReqDto reqDto);

    /**
     * @MethodName: cashOutProcess
     * @Description: 出金处理
     * @Param: [params]
     * @Return: bassTxnId
     * @Author: Jarvis.li
     * @Date: 2023/07/17
     */
    boolean cashOutProcess(String bassTxnId);
}
