package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.BsAddDealResDto;
import com.fenbeitong.dech.api.model.dto.CloudPayStandardResponse;
import com.fenbeitong.dech.api.model.dto.UnionPayBsC2bReqDto;
import com.fenbeitong.dech.api.model.dto.UnionPayBsC2bResDto;

import java.util.Map;

public interface IUnionPayBsUnionPayService {
    /**
     * c2b码申请
     * @param unionPayBsC2bReqDto
     */
    UnionPayBsC2bResDto c2bRequest(UnionPayBsC2bReqDto unionPayBsC2bReqDto);

    /**
     * 异步通知银联
     * @param qrNo
     * @param voucherNum
     * @return
     */
    CloudPayStandardResponse addDealNotifyBank(String qrNo, String voucherNum);

    /**
     * 云闪付签名验证
     */
    Boolean verifySign(Map<String, String> dataMap);

    BsAddDealResDto handleAddDeal(Map<String, String> dataMap);
}
