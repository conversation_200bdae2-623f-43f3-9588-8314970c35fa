package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.*;

import java.util.List;

public interface IBankEntSearchService {

    /**
     * 查询交易信息
     *
     * @param reqDTO
     * @return
     */
    BankEntQueryTradeRespDTO queryTradeInfo(BankEntQueryTradeReqDTO reqDTO);

    /**
     * 查询批量交易信息
     *
     * @param reqDTO
     * @return
     */
    List<BankEntQueryTradeRespDTO> queryBatTradeInfo(BankEntBatQueryTradeReqDTO reqDTO);

    /**
     * 退汇交易明细查询
     *
     * @param reqDTO
     * @return
     */
    BankEntRefundExchangeRespDTO queryRefundExchangeInfo(BankEntRefundExchangeQueryReqDTO reqDTO);

    /**
     * 发薪批量结果查询
     *
     * @param reqDTO
     * @return
     */
    BankEntSalaryPayRespDTO querySalaryPayTradeInfo(BankEntSalaryPayReqDTO reqDTO);
}
