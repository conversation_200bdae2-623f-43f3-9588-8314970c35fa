package com.fenbeitong.dech.api.model.dto.airwallex.cardholder;

import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import com.fenbeitong.dech.api.model.dto.base.BaseModel;
import lombok.Data;


/**
 * Created by FBT on 2023/3/29.
 */
@Data
public class CardHolderRpcRespDTO extends BaseModel {

    /**
     * 状态
     */
    private String status;

    /**
     * 电话
     */
    private String mobile_number;

    /**
     * 个人持卡人的详细信息。
     */
    private BaseAirwallexRpcDTO.Individual individual;

    private String email;

    /**
     * 持卡人的唯一标识符
     */
    private String cardholder_id;

    /**
     * 持卡人地址
     */
    private BaseAirwallexRpcDTO.Address address;

    /**
     * 持卡人邮政地址
     */
    private BaseAirwallexRpcDTO.Address postal_address;


}
