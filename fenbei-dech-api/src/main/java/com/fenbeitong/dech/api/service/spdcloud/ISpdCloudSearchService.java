package com.fenbeitong.dech.api.service.spdcloud;

import com.fenbeitong.dech.api.model.dto.spdcloud.req.*;
import com.fenbeitong.dech.api.model.dto.spdcloud.resp.*;

import java.util.List;

public interface ISpdCloudSearchService {

    SpdbQuerySinglePayRpcRespDTO querySinglePayResult (SpdbQuerySinglePayRpcReqDTO spdbSinglePayQueryReqDTO);

    SpdbQueryBatchPayRpcRespDTO queryBatchPayResult(SpdbQueryBatchPayRpcReqDTO spdbBatchPayQueryDTO);

    SpdbQueryBalanceRpcRespDTO queryBalance(SpdbQueryBalanceRpcReqDTO spdbBalanceQueryReqDTO);

    List<SpdbTradeDetailListRespDTO> queryTradeDetailList(SpdbQueryTradeListRpcReqDTO spdbQueryTradeListRpcReqDTO);

    SpdbReceiptRpcRespDTO queryReceiptDownReq(SpdbCreateDownReceiptRpcReqDTO spdbCreateDownReceiptReqDTO);

    String receiptDownload(SpdbReceiptDownRpcReq spdbReceiptDownRpcReq);

}
