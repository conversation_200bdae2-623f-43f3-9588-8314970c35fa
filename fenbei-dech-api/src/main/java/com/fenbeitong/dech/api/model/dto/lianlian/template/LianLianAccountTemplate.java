package com.fenbeitong.dech.api.model.dto.lianlian.template;

import lombok.Data;

import java.io.Serializable;
@Data
public class LianLianAccountTemplate implements Serializable {
    private String templateId;

    private String templateName;

    private String templateQuota;

    private String enterpriseId;

    private String enterpriseName;

    private String acceptScope;

    private String organizationalScope;

    private LianLianConsumeRule consumeRule;

    private LianLianProvideRule provideRule;

    private String quotaRecovery;

    private String templateType;

    private String currency;
}
