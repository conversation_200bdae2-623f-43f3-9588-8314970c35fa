package com.fenbeitong.dech.api.model.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 附加处理
 */
@Data
@Builder
public class BsAddDealResDto implements Serializable {
    /**
     * c2b码
     */
    private String qrNo;
    /**
     *证书 ID
     */
    private String certId;
    /**
     * 交易类型
     */
    private String reqType;
    /**
     * 交易金额
     */
    private String txnAmt;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 附加信息
     */
    private String transAddnInfo;
    /**
     * 付款凭证号
     */
    private String voucherNum;
}
