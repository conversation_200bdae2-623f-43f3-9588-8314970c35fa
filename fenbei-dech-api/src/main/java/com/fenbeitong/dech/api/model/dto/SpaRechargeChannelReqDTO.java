package com.fenbeitong.dech.api.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @ClassName SpaRechargeChannelReqDTO
 * @Description: 充值支付通道获取
 * <AUTHOR>
 * @Date 2021/10/20
 **/
@Data
public class SpaRechargeChannelReqDTO implements Serializable {

    /*
     * 账户号
     */
    @NotBlank
    private String accountNo;

    /*
     * 充值订单号
     */
    @NotBlank
    private String orderNo;

    /*
     * 订单时间
     */
    @NotBlank
    private String orderTime;

    /*
     * 交易金额
     */
    @NotBlank
    private String tranAmt;

    /*
     * 交易手续费
     */
    @NotBlank
    private String tranFree;

    /*
     * 订单名称
     */
    private String orderName;

    /*
     * 银行编码
     */
    @NotBlank
    private String bankCode;

    /*
     * 订单备注
     */
    private String orderRemark;

    /*
     * 前端跳转url
     */
    @NotBlank
    private String frontSkipUrl;

    /*
     * 回调通知url
     */
    @NotBlank
    private String callBackNoticeUrl;

    /*
     * 交易清算类型
     */
    @NotBlank
    private String tranSettleType;
}
