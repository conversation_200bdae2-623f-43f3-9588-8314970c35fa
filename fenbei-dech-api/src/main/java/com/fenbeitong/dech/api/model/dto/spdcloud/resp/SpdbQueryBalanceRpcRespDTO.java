package com.fenbeitong.dech.api.model.dto.spdcloud.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SpdbQueryBalanceRpcRespDTO implements Serializable {
    /**
     *账号
     */
    private String acctNo ;

    /**
     *客户号
     */
    private String masterID ;

    /**
     *账号余额
     */
    private String balance ;


    /**
     *保留余额
     */
    private String reserveBalance ;

    /**
     *冻结余额
     */
    private String freezeBalance ;

    /**
     *控制余额
     */
    private String cortrolBalance ;

    /**
     *可用金额
     */
    private String canUseBalance ;

    /**
     *透支余额
     */
    private String overdraftBalance ;


}
