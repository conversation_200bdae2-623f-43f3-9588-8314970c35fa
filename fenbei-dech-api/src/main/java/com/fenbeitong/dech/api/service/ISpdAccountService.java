package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.spd.SpdYqdzSignReqDTO;
import com.fenbeitong.dech.api.model.dto.spd.SpdYqdzSignRespDTO;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-09 下午5:34
 */

public interface ISpdAccountService {

    /**
     * 银企对账签约
     * @param spdYqdzSignReqDTO
     * @return
     */
    SpdYqdzSignRespDTO yqdzSign(SpdYqdzSignReqDTO spdYqdzSignReqDTO);
}
