package com.fenbeitong.dech.api.model.dto.spdcloud.resp;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Data
public class SpdbBatchPayRpcRespDTO implements Serializable {

    /**
     *交易当前状态
     0-请至网银处理
     1-成功
     2-失败
     （当异步判断标志为0时返回0）
     */
    private String onlineBankStatus ;

    /**
     *记录网银录入失败的原因
     */
    private String errMessage ;
    /**
     *后续查询交易情况用包号发EYW5查询
     */
    private String packageNo ;
    /**
     * 受理编号
     *交易的受理编号，同步模式下有值。
     异步模式下为空
     */
    private String reserve1 ;

}
