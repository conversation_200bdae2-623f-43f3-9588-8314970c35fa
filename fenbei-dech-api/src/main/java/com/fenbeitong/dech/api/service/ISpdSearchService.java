package com.fenbeitong.dech.api.service;

import com.fenbeitong.dech.api.model.dto.spd.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-03-13 下午8:31
 */

public interface ISpdSearchService {

    /**
     * 查询已分摊状态
     * @param spdAllocatedQueryReqDTO
     * @return
     */
    public SpdAllocatedQueryRespDTO queryAllocatedStatus(SpdAllocatedQueryReqDTO spdAllocatedQueryReqDTO);

    /**
     * 查询未分摊明细
     * @param beginDate
     * @param endDate
     * @return
     */
    public List<SpdUnAllocatedListRespDTO> queryUnAllocatedDetail(String beginDate, String endDate);

    /**
     * 虚账户已分摊明细查询
     * @param spdVirtualAllocatedQueryReqDTO
     * @return
     */
    List<SpdVirtualAllocatedListRespDTO> queryVirtualAllocatedDetail(SpdVirtualAllocatedQueryRpcReqDTO spdVirtualAllocatedQueryReqDTO);

    /**
     * 根据对手方信息查询交易明细
     * @param spdTradeDetailListReqDTO
     * @return
     */
    SpdTradeDetailListRespDTO queryTradeDetailList(SpdTradeDetailListReqDTO spdTradeDetailListReqDTO);

    /**
     * 获取虚户银企对账文件
     * @param acctNo
     * @param dzDay
     * @return
     */
    String queryYqdzDownLoadUrl(String acctNo,String dzDay);
}
