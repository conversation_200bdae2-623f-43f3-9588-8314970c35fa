package com.fenbeitong.dech.api.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Author: liyi
 * @Date: 2022/7/1 上午11:45
 */
@Data
public class ZsQueryCouponInfoDto extends ZsPayReqDto  {
    @ApiModelProperty(value = "收款方银联标识ID")
    private String payeeId;

    @ApiModelProperty(value = "收款方名称")
    private String payeeName;

    @ApiModelProperty(value = "收款方商户类型")
    private String payeeMerCatCode;

    @ApiModelProperty(value = "收款方商户终端号")
    private String payeeTermId;
}
