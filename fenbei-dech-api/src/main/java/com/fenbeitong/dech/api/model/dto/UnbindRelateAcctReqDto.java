package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: yanqiu.hu
 * @create: 2022-09-16 11:03:05
 * @Version 1.0
 **/
@Data
public class UnbindRelateAcctReqDto implements Serializable {

    /*
     * 功能标志	Y 1
     * 1：解绑
     */
    private String functionFlag;

    /*
     * 交易网会员代码	Y 32
     * 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的交易网代码，并且须用“|::|”(右侧)进行分隔。
     */
    private String tranNetMemberCode;

    /*
     * 子账户账号	Y 32
     * 若需要把一个待绑定账户关联到两个会员名下，此字段可上送两个会员的子账户账号，并且须用“|::|”(右侧)进行分隔。
     */
    private String subAcctNo;

    /*
     * 待解绑的提现账户的账号	Y 32
     * 提现的银行卡
     */
    private String memberAcctNo;

    /*
     * 保留域	N 120
     */
    private String reservedMsg;

}
