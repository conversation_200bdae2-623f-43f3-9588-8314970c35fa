package com.fenbeitong.dech.api.service.spabank;

import com.fenbeitong.dech.api.model.dto.spabank.req.SpaBalanceQueryReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.req.SpaSignQueryReqDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaBalanceQueryRespDto;
import com.fenbeitong.dech.api.model.dto.spabank.resp.SpaSignQueryRespDto;

import java.util.List;

/**
 * 平安银行账户
 */
public interface IBankEntSpaBankAccountService {

    /**
     * 银企联账户实时余额查询
     * @param spaBalanceQueryReqDto
     * @return
     */
    SpaBalanceQueryRespDto queryAccountBalanceInfo(SpaBalanceQueryReqDto spaBalanceQueryReqDto);

    SpaSignQueryRespDto querySignResult(SpaSignQueryReqDto spaSignQueryReqDto);
}
