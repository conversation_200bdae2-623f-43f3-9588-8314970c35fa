package com.fenbeitong.dech.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName BankDownloadBillReqDto
 * @Description: 平台对账单生成请求DTO
 * <AUTHOR>
 * @Date 2021/3/12
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankDownloadBillReqDto implements Serializable {

    private String accountNo;
    private String billDate;
    private String bankName;
    private String fileType;
}
