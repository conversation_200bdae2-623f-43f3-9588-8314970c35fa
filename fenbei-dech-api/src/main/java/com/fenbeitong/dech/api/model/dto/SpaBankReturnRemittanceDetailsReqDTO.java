package com.fenbeitong.dech.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SpaQueryReturnRemittanceDetailsRespDTO
 * @Description: 3.11支付退票查询[4019]
 * <AUTHOR>
 * @Date 2022/1/7
 **/
@Data
public class SpaBankReturnRemittanceDetailsReqDTO implements Serializable {

    /**
     * 	企业付款帐号	C(20)	Y
     */
    private String accNo;

    /**
     * 	查询开始时间	C(8)	Y	yyyyMMdd 查询时间范围为30天
     */
    private String startDate;

    /**
     * 	查询结束时间	C(8)	Y	yyyyMMdd查询时间范围为30天，包含次天
     */
    private String endDate;

    /**
     * 	当前页码	C(10)	Y	从1开始递增
     */
    private String pageNo;

    /**
     * 	每页记录条数	C(5)	N	默认为30；最大300
     * 对同一个账户的分页查询此值保持一致
     */
    private String pageCts;

}
