package com.fenbeitong.dech.api.model.dto.spdcloud.resp;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;

@Data
public class SpdbQuerySinglePayRpcRespDTO implements Serializable {

    /**
     * 电子凭证号  Y
     *是企业发起该笔业务在ERP系统产生的流水号
     */
    private String elecChequeNo ;

    /**
     * 账号  Y
     */
    private String acctNo ;

    /**
     * 付款人账户名称  Y
     */
    private String acctName ;

    /**
     记帐日期
     */
    private Date transDate ;

    /**
     * 收款人账号  N
     *为空即查所有
     */
    private String payeeAcctNo ;

    /**
     * 收款人名称  N
     */
    private String payeeName ;

    /**
     * 收款人账户类型  N
     */
    private String payeeType ;

    /**
     * 收款行名称  N
     */
    private String payeeBankName ;

    /**
     *  地址N
     */
    private String payeeAddress ;

    /**
     * 本行他行标志  N
     *0：本行
     1：他行
     为空即查所有
     */
    private String sysFlag ;

    /**
     * 交易金额
     */
    private String amount;

    /**
     * 当明细的交易状态为8-失败拒绝时，在“附言”字段中拼接摘要代码和退票理由。
     当明细的交易状态为9-撤销，则附言字段返回为附言+ "<错误原因:" + " " + 错误信息 + ">"。
     如果客户使用5148虚账户付款时，此项将显示虚账号+@+附言  N
     */
    private String note ;

    /**
     * 00：待处理
     01：处理中：在网银页面上点击确认后到提交之前或授权前
     02：已处理：网银发核心交易成功
     03：已拒绝：客户取消交易
     04：校验失败：交易内容的格式或逻辑校验未通过或发后台失败
     05：已失效：客户提交交易后未到网银页面处理，过了该笔交易的处理时效
     正常返回有值
     */
    private String interHandleStatus ;

    /**
     * 交易当前状态
     A-业务登记
     0-待补录
     1-待记帐
     2-待复核
     3-待授权
     4-完成
     8-拒绝
     9-撤销
     */
    private String transStatus ;



    /**
     * 交易流水号
     */
    private String seqNo ;

    /**
     * 后台错误码
     */
    private String abstractCode ;

    /**
     * 后台错误原因
     */
    private String reason ;
}
