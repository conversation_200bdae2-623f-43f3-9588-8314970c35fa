<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.finhub</groupId>
        <artifactId>finhub-root</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.fenbeitong</groupId>
    <artifactId>fenbei-dech</artifactId>
    <version>5.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>fenbei-dech-boot</module>
        <module>fenbei-dech-manager</module>
        <module>fenbei-dech-core</module>
        <module>fenbei-dech-common</module>
        <module>fenbei-dech-api</module>
        <module>fenbei-dech-dubbo</module>
        <module>fenbei-dech-dto</module>
        <module>fenbei-dech-zbank</module>
        <module>fenbei-dech-zxbank</module>
        <module>fenbei-dech-spabank</module>
        <module>fenbei-dech-cgb</module>
        <module>fenbei-dech-cloudpay</module>
        <module>fenbei-dech-lfbank</module>
        <module>fenbei-dech-ufida</module>
        <module>fenbei-dech-cmb</module>
        <module>fenbei-dech-icbc</module>
        <module>fenbei-dech-spd</module>
        <module>fenbei-dech-airwallex</module>
        <module>fenbei-dech-nbcb</module>
        <module>fenbei-dech-lianlian</module>
    </modules>

    <properties>
        <!--大版本-->
        <dech.version>6.2.9.022${current.version}</dech.version>
        <guava.version>32.0.0-jre</guava.version>
        <!-- finhub  version -->
        <finhub.version>3.1.76${current.version}</finhub.version>
        <finhub.resource.pool.version>3.1.20${current.version}</finhub.resource.pool.version>
        <!-- 3rd zx version -->
        <zx.3rd.bcmail.version>1.45-SNAPSHOT</zx.3rd.bcmail.version>
        <zx.3rd.bcprov.version>1.45-SNAPSHOT</zx.3rd.bcprov.version>
        <zx.3rd.bouncycastle.version>112-SNAPSHOT</zx.3rd.bouncycastle.version>
        <zx.3rd.CNCBCryptoPkg.version>1.0.0-SNAPSHOT</zx.3rd.CNCBCryptoPkg.version>
        <zx.3rd.sadk.version>3.0.0.2-SNAPSHOT</zx.3rd.sadk.version>
        <!-- 3rd zb version -->
        <zb.3rd.openSdk.version>3.0.0Obfusc</zb.3rd.openSdk.version>
        <zb.3rd.bcprov.version>1.64</zb.3rd.bcprov.version>
        <!-- 3rd spa version -->
        <spa.3rd.SPACryptoPkg.version>1.8.3-SNAPSHOT</spa.3rd.SPACryptoPkg.version>
        <spa.3rd.SPASignPkg.version>1.0.0-SNAPSHOT</spa.3rd.SPASignPkg.version>
        <spa.3rd.SPADcRSAPkg.version>1.0.0-SNAPSHOT</spa.3rd.SPADcRSAPkg.version>
        <spa.3rd.SPAPKIBASE.version>1.0.0-SNAPSHOT</spa.3rd.SPAPKIBASE.version>
        <spa.3rd.SPACFCACertKitJS.version>1.0.0-SNAPSHOT</spa.3rd.SPACFCACertKitJS.version>
        <spa.3rd.SPAnetsign18.version>1.0.0-SNAPSHOT</spa.3rd.SPAnetsign18.version>
        <spa.3rd.SPAnetsignderutil.version>1.0.0-SNAPSHOT</spa.3rd.SPAnetsignderutil.version>
        <spa.3rd.SPAApacheConfiguration.version>1.0.0-SNAPSHOT</spa.3rd.SPAApacheConfiguration.version>
        <spa.3rd.SPAApacheHttpclient.version>1.0.0-SNAPSHOT</spa.3rd.SPAApacheHttpclient.version>
        <!-- other -->
        <com.jcraft.jsch.version>0.1.55</com.jcraft.jsch.version>

        <fenbei.bank.ent.api.version>5.2.8.052${current.version}</fenbei.bank.ent.api.version>
        <material.version>5.0.17${current.version}</material.version>
        <elasticsearch.version>7.10.0</elasticsearch.version>
        <fastjson.version>1.2.83</fastjson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Module -->
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-boot</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-manager</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-core</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-common</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-api</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-dubbo</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-dto</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-zbank</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-cloudpay</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-zxbank</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-spabank</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-cgb</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-cloudpay</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-lfbank</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-ufida</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-cmb</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-airwallex</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-spd</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-icbc</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-nbcb</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>fenbei-dech-lianlian</artifactId>
                <version>${dech.version}</version>
            </dependency>
            <!-- finhub -->
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-common</artifactId>
                <version>${finhub.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-crypto</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
	            	<exclusion>
	            		<groupId>org.apache.shiro</groupId>
	            		<artifactId>shiro-spring</artifactId>
	            	</exclusion>
	            	<exclusion>
	            		<groupId>org.apache.shiro</groupId>
	            		<artifactId>shiro-core</artifactId>
	            	</exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-base</artifactId>
                <version>${finhub.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-crypto</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-kafka</artifactId>
                <version>${finhub.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.jpountz.lz4</groupId>
                        <artifactId>lz4</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-common-oss</artifactId>
                <version>${finhub.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong</groupId>
                <artifactId>finhub-resource-pool</artifactId>
                <version>${finhub.resource.pool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>${com.jcraft.jsch.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong.bank.ent</groupId>
                <artifactId>fenbei-bank-ent-api</artifactId>
                <version>${fenbei.bank.ent.api.version}</version>
            </dependency>

            <dependency>
			    <groupId>com.github.pagehelper</groupId>
	            <artifactId>pagehelper-spring-boot-starter</artifactId>
	            <version>1.3.0</version>
			</dependency>

            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>icbc-api-sdk-cop</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>icbc-api-sdk-cop-io</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>icbc-ca</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>icbc-infosec</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>hsm-software-share</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <!--三方jar-->
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>reindex-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <!-- Java High Level REST Client -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>verifycert</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>isf139</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.icbc</groupId>
                <artifactId>isfjsm2</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.fenbeitong.3rd</groupId>
                <artifactId>open-basic-sdk</artifactId>
                <version>1.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pingan.openbank</groupId>
                <artifactId>api-sdk</artifactId>
                <version>1.9.104-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.pingan.openbank</groupId>
                <artifactId>obp-client</artifactId>
                <version>1.0.1-RELEASE</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
